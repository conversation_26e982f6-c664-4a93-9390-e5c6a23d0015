// 导入 Vue 的 ref 函数
import {
	ref
} from 'vue';

// 创建一个响应式的登录状态变量
const isLoggedIn = ref(false);

// 检查用户 token 的异步函数
const checkToken = async () => {
	// 从本地存储获取登录状态和 token
	const islogin = uni.getStorageSync("islogin") || 0;
	const token = uni.getStorageSync("token");

	// 如果未登录或没有 token，执行登录
	if ((islogin != 1) || (!token)) {
		await login();
	} else {
		try {
			// 调用云函数检查 token 有效性
			const tokenRes = await uniCloud.callFunction({
				name: 'usercenter',
				data: {
					action: 'checkToken',
					params: {
						uniIdToken: token
					},
					uniIdToken: token
				},
			});

			// 如果 token 无效，执行登录
			if (tokenRes.result.code != 0) {
				await login();
			}
		} catch (err) {
			// 发生错误时，打印错误并执行登录
			console.log(err);
			await login();
		}
	}
};

// 登录函数
async function login() {
	// 检查是否在 Web 平台
	if (uni.getSystemInfoSync().uniPlatform === 'web') {
		// 获取存储的真实姓名
		const uni_truename = uni.getStorageSync('uni_truename');
		if (uni_truename) {
			// 如果有真实姓名，设置登录状态为 true 并返回
			isLoggedIn.value = true;
			return;
		}
		// 设置登录状态为 true
		isLoggedIn.value = true;

		// 调用云函数进行登录
		let res = await uniCloud.callFunction({
			name: 'user-center',
			data: {
				action: 'user/login',
				params: {
					"username": "admin",
					"password": "42E2154583F3679D583FB274CB7570A3"
				}
			}
		});
		// 打印登录结果
		console.log(res);
		// 将用户信息存储到本地
		uni.setStorageSync("uid", res.result.userId);
		uni.setStorageSync("dept", res.result.dept);
		uni.setStorageSync("token", res.result.uniIdToken);
		uni.setStorageSync("username", res.result.userName);
		uni.setStorageSync("user", res.result);
		uni.setStorageSync('uni_tel', res.result.mobile);
		uni.setStorageSync('uni_truename', res.result.truename);
		uni.setStorageSync('uni_cardNumber', res.result.cardNumber);
		uni.setStorageSync('dept', res.result.dept);
		uni.setStorageSync('nickname', res.result.truename);
		uni.setStorageSync('username', res.result.truename);
		uni.setStorageSync('islogin', 1);

	} else {
		// 微信小程序环境下的登录逻辑
		isLoggedIn.value = true;

		console.log("WeChat Mini Program environment detected");
		try {
			// 调用微信登录
			const loginRes = await new Promise((resolve, reject) => {
				uni.login({
					provider: 'weixin',
					success: resolve,
					fail: reject,
				});
			});

			// 检查是否成功获取登录 code
			if (!loginRes.code) {
				throw new Error('获取微信登录 code 失败');
				console.log(`获取微信登录 code 失败`);
			}
			// 调用云函数进行微信登录
			const userRes = await uniCloud.callFunction({
				name: 'usercenter',
				data: {
					action: 'loginByWeixin',
					params: {
						code: loginRes.code,
						xcxflag:'hj'
					},
				},
			});
			// 处理登录结果
			if (userRes.result.code === 0) {
				const userInfo = userRes.result.userInfo;
				// 将用户信息存储到本地
				uni.setStorageSync('username', userInfo.username);
				uni.setStorageSync('dept', userInfo.dept);
				uni.setStorageSync('uid', userRes.result.uid);
				uni.setStorageSync('token', userRes.result.token);
				uni.setStorageSync('uni-isgm', userRes.result.isgm);
				uni.setStorageSync('islogin', userInfo.islogin || 0);

				if (userInfo.truename)
					uni.setStorageSync('uni_truename', userInfo.truename)
				if (userInfo.cardNumber)
					uni.setStorageSync('uni_cardNumber', userInfo.cardNumber)
				if (userInfo.nickname)
					uni.setStorageSync('nickname', userInfo.nickname);

				if (userInfo.mobile)
					uni.setStorageSync('uni_tel', userInfo.mobile);
				uni.setStorageSync('user', userInfo);
				isLoggedIn.value = true;
				// 检查是否需要跳转到绑定页面
				if (uni.getStorageSync('islogin') != 1) {
					uni.navigateTo({
						url: '/pages/login/login'
					});
				}

			} else {
				// 登录失败处理
				isLoggedIn.value = false;
				console.log(`登录失败: ${userRes.result.msg}`);
				throw new Error(`登录失败: ${userRes.result.msg}`);
			}
		} catch (error) {
			// 错误处理
			isLoggedIn.value = false;
			console.error('登录失败', error);
			uni.showModal({
				title: '登录失败',
				content: error.message,
				showCancel: false,
			});
		}
	}
}

// 导出函数和变量
export {
	checkToken,
	isLoggedIn,
	login
};