!function(e,t){for(var r in t)e[r]=t[r]}(exports,function(e){var t={};function r(n){if(t[n])return t[n].exports;var i=t[n]={i:n,l:!1,exports:{}};return e[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)r.d(n,i,function(t){return e[t]}.bind(null,i));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=23)}([function(e,t,r){var n;e.exports=(n=n||function(e,t){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&"undefined"!=typeof global&&global.crypto&&(n=global.crypto),!n)try{n=r(2)}catch(e){}var i=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),a={},s=a.lib={},u=s.Base={extend:function(e){var t=o(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},c=s.WordArray=u.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||l).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var a=r[o>>>2]>>>24-o%4*8&255;t[n+o>>>2]|=a<<24-(n+o)%4*8}else for(var s=0;s<i;s+=4)t[n+s>>>2]=r[s>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=u.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],r=0;r<e;r+=4)t.push(i());return new c.init(t,e)}}),f=a.enc={},l=f.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new c.init(r,t/2)}},h=f.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new c.init(r,t)}},p=f.Utf8={stringify:function(e){try{return decodeURIComponent(escape(h.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return h.parse(unescape(encodeURIComponent(e)))}},d=s.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=p.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r,n=this._data,i=n.words,o=n.sigBytes,a=this.blockSize,s=o/(4*a),u=(s=t?e.ceil(s):e.max((0|s)-this._minBufferSize,0))*a,f=e.min(4*u,o);if(u){for(var l=0;l<u;l+=a)this._doProcessBlock(i,l);r=i.splice(0,u),n.sigBytes-=f}return new c.init(r,f)},clone:function(){var e=u.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),y=(s.Hasher=d.extend({cfg:u.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){d.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new y.HMAC.init(e,r).finalize(t)}}}),a.algo={});return a}(Math),n)},function(e,t,r){var n,i,o,a,s,u,c,f,l,h,p,d,y,g,v,_,m,b,w;e.exports=(n=r(0),r(4),void(n.lib.Cipher||(i=n,o=i.lib,a=o.Base,s=o.WordArray,u=o.BufferedBlockAlgorithm,c=i.enc,c.Utf8,f=c.Base64,l=i.algo.EvpKDF,h=o.Cipher=u.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){u.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?w:m}return function(t){return{encrypt:function(r,n,i){return e(n).encrypt(t,r,n,i)},decrypt:function(r,n,i){return e(n).decrypt(t,r,n,i)}}}}()}),o.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),p=i.mode={},d=o.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),y=p.CBC=function(){var e=d.extend();function t(e,t,r){var n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(var o=0;o<r;o++)e[t+o]^=n[o]}return e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize;t.call(this,e,r,i),n.encryptBlock(e,r),this._prevBlock=e.slice(r,r+i)}}),e.Decryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,o=e.slice(r,r+i);n.decryptBlock(e,r),t.call(this,e,r,i),this._prevBlock=o}}),e}(),g=(i.pad={}).Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,o=[],a=0;a<n;a+=4)o.push(i);var u=s.create(o,n);e.concat(u)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},o.BlockCipher=h.extend({cfg:h.cfg.extend({mode:y,padding:g}),reset:function(){var e;h.reset.call(this);var t=this.cfg,r=t.iv,n=t.mode;this._xformMode==this._ENC_XFORM_MODE?e=n.createEncryptor:(e=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==e?this._mode.init(this,r&&r.words):(this._mode=e.call(n,this,r&&r.words),this._mode.__creator=e)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e,t=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(t.pad(this._data,this.blockSize),e=this._process(!0)):(e=this._process(!0),t.unpad(e)),e},blockSize:4}),v=o.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),_=(i.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;return(r?s.create([1398893684,1701076831]).concat(r).concat(t):t).toString(f)},parse:function(e){var t,r=f.parse(e),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(t=s.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),v.create({ciphertext:r,salt:t})}},m=o.SerializableCipher=a.extend({cfg:a.extend({format:_}),encrypt:function(e,t,r,n){n=this.cfg.extend(n);var i=e.createEncryptor(r,n),o=i.finalize(t),a=i.cfg;return v.create({ciphertext:o,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:n.format})},decrypt:function(e,t,r,n){return n=this.cfg.extend(n),t=this._parse(t,n.format),e.createDecryptor(r,n).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),b=(i.kdf={}).OpenSSL={execute:function(e,t,r,n){n||(n=s.random(8));var i=l.create({keySize:t+r}).compute(e,n),o=s.create(i.words.slice(t),4*r);return i.sigBytes=4*t,v.create({key:i,iv:o,salt:n})}},w=o.PasswordBasedCipher=m.extend({cfg:m.cfg.extend({kdf:b}),encrypt:function(e,t,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,e.keySize,e.ivSize);n.iv=i.iv;var o=m.encrypt.call(this,e,t,i.key,n);return o.mixIn(i),o},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=n.kdf.execute(r,e.keySize,e.ivSize,t.salt);return n.iv=i.iv,m.decrypt.call(this,e,t,i.key,n)}}))))},function(e,t){e.exports=require("crypto")},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n={FormData:!0,UniCloudError:!0};Object.defineProperty(t,"FormData",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(t,"UniCloudError",{enumerable:!0,get:function(){return o.default}});var i=u(r(26)),o=u(r(27)),a=r(28);Object.keys(a).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(n,e)||e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))}));var s=r(7);function u(e){return e&&e.__esModule?e:{default:e}}Object.keys(s).forEach((function(e){"default"!==e&&"__esModule"!==e&&(Object.prototype.hasOwnProperty.call(n,e)||e in t&&t[e]===s[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return s[e]}}))}))},function(e,t,r){var n,i,o,a,s,u,c,f;e.exports=(f=r(0),r(11),r(12),i=(n=f).lib,o=i.Base,a=i.WordArray,s=n.algo,u=s.MD5,c=s.EvpKDF=o.extend({cfg:o.extend({keySize:4,hasher:u,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r,n=this.cfg,i=n.hasher.create(),o=a.create(),s=o.words,u=n.keySize,c=n.iterations;s.length<u;){r&&i.update(r),r=i.update(e).finalize(t),i.reset();for(var f=1;f<c;f++)r=i.finalize(r),i.reset();o.concat(r)}return o.sigBytes=4*u,o}}),n.EvpKDF=function(e,t,r){return c.create(r).compute(e,t)},f.EvpKDF)},function(e,t,r){var n,i,o;e.exports=(o=r(0),i=(n=o).lib.WordArray,n.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],o=0;o<r;o+=3)for(var a=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;s<4&&o+.75*s<r;s++)i.push(n.charAt(a>>>6*(3-s)&63));var u=n.charAt(64);if(u)for(;i.length%4;)i.push(u);return i.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<r.length;o++)n[r.charCodeAt(o)]=o}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return function(e,t,r){for(var n=[],o=0,a=0;a<t;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,u=r[e.charCodeAt(a)]>>>6-a%4*2,c=s|u;n[o>>>2]|=c<<24-o%4*8,o++}return i.create(n,o)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},o.enc.Base64)},function(e,t,r){var n;e.exports=(n=r(0),function(e){var t=n,r=t.lib,i=r.WordArray,o=r.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var u=a.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,a=e[t+0],u=e[t+1],p=e[t+2],d=e[t+3],y=e[t+4],g=e[t+5],v=e[t+6],_=e[t+7],m=e[t+8],b=e[t+9],w=e[t+10],S=e[t+11],E=e[t+12],A=e[t+13],I=e[t+14],N=e[t+15],C=o[0],T=o[1],R=o[2],x=o[3];C=c(C,T,R,x,a,7,s[0]),x=c(x,C,T,R,u,12,s[1]),R=c(R,x,C,T,p,17,s[2]),T=c(T,R,x,C,d,22,s[3]),C=c(C,T,R,x,y,7,s[4]),x=c(x,C,T,R,g,12,s[5]),R=c(R,x,C,T,v,17,s[6]),T=c(T,R,x,C,_,22,s[7]),C=c(C,T,R,x,m,7,s[8]),x=c(x,C,T,R,b,12,s[9]),R=c(R,x,C,T,w,17,s[10]),T=c(T,R,x,C,S,22,s[11]),C=c(C,T,R,x,E,7,s[12]),x=c(x,C,T,R,A,12,s[13]),R=c(R,x,C,T,I,17,s[14]),C=f(C,T=c(T,R,x,C,N,22,s[15]),R,x,u,5,s[16]),x=f(x,C,T,R,v,9,s[17]),R=f(R,x,C,T,S,14,s[18]),T=f(T,R,x,C,a,20,s[19]),C=f(C,T,R,x,g,5,s[20]),x=f(x,C,T,R,w,9,s[21]),R=f(R,x,C,T,N,14,s[22]),T=f(T,R,x,C,y,20,s[23]),C=f(C,T,R,x,b,5,s[24]),x=f(x,C,T,R,I,9,s[25]),R=f(R,x,C,T,d,14,s[26]),T=f(T,R,x,C,m,20,s[27]),C=f(C,T,R,x,A,5,s[28]),x=f(x,C,T,R,p,9,s[29]),R=f(R,x,C,T,_,14,s[30]),C=l(C,T=f(T,R,x,C,E,20,s[31]),R,x,g,4,s[32]),x=l(x,C,T,R,m,11,s[33]),R=l(R,x,C,T,S,16,s[34]),T=l(T,R,x,C,I,23,s[35]),C=l(C,T,R,x,u,4,s[36]),x=l(x,C,T,R,y,11,s[37]),R=l(R,x,C,T,_,16,s[38]),T=l(T,R,x,C,w,23,s[39]),C=l(C,T,R,x,A,4,s[40]),x=l(x,C,T,R,a,11,s[41]),R=l(R,x,C,T,d,16,s[42]),T=l(T,R,x,C,v,23,s[43]),C=l(C,T,R,x,b,4,s[44]),x=l(x,C,T,R,E,11,s[45]),R=l(R,x,C,T,N,16,s[46]),C=h(C,T=l(T,R,x,C,p,23,s[47]),R,x,a,6,s[48]),x=h(x,C,T,R,_,10,s[49]),R=h(R,x,C,T,I,15,s[50]),T=h(T,R,x,C,g,21,s[51]),C=h(C,T,R,x,E,6,s[52]),x=h(x,C,T,R,d,10,s[53]),R=h(R,x,C,T,w,15,s[54]),T=h(T,R,x,C,u,21,s[55]),C=h(C,T,R,x,m,6,s[56]),x=h(x,C,T,R,N,10,s[57]),R=h(R,x,C,T,v,15,s[58]),T=h(T,R,x,C,A,21,s[59]),C=h(C,T,R,x,y,6,s[60]),x=h(x,C,T,R,S,10,s[61]),R=h(R,x,C,T,p,15,s[62]),T=h(T,R,x,C,b,21,s[63]),o[0]=o[0]+C|0,o[1]=o[1]+T|0,o[2]=o[2]+R|0,o[3]=o[3]+x|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;r[i>>>5]|=128<<24-i%32;var o=e.floor(n/4294967296),a=n;r[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var s=this._hash,u=s.words,c=0;c<4;c++){var f=u[c];u[c]=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8)}return s},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function c(e,t,r,n,i,o,a){var s=e+(t&r|~t&n)+i+a;return(s<<o|s>>>32-o)+t}function f(e,t,r,n,i,o,a){var s=e+(t&n|r&~n)+i+a;return(s<<o|s>>>32-o)+t}function l(e,t,r,n,i,o,a){var s=e+(t^r^n)+i+a;return(s<<o|s>>>32-o)+t}function h(e,t,r,n,i,o,a){var s=e+(r^(t|~n))+i+a;return(s<<o|s>>>32-o)+t}t.MD5=o._createHelper(u),t.HmacMD5=o._createHmacHelper(u)}(Math),n.MD5)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.accMul=function(e,t){if(isNaN(e)||isNaN(t))return NaN;const{value:r,power:n}=y(e),{value:i,power:o}=y(t);return r*i/Math.pow(10,n+o)},t.camel2snake=l,t.camel2snakeJson=function(e){return h(e,"camel2snake")},t.deepClone=function(e){return JSON.parse(JSON.stringify(e))},t.deleteObjectKey=function e(t,r){const n=t.shift();if(!n)return;e(t,r[n]),r[n]&&(Object.keys(r[n]).length<=0||"string"==typeof r[n])&&delete r[n]},t.getDateStr=p,t.getExtension=function(e){return s[e]},t.getFullTimeStr=function(e){return p(e=e||new Date)+" "+d(e)},t.getOffsetDate=function(e){return new Date(Date.now()+6e4*((new Date).getTimezoneOffset()+60*(e||0)))},t.getTimeStr=d,t.hasOwn=o,t.isFn=function(e){return"function"==typeof e},t.isPlainObject=a,t.log=function(){0},t.mime2ext=void 0,t.snake2camel=f,t.snake2camelJson=function(e){return h(e,"snake2camel")};const n=Object.prototype.toString,i=Object.prototype.hasOwnProperty;function o(e,t){return i.call(e,t)}function a(e){return"[object Object]"===n.call(e)}const s={"image/png":"png","image/jpeg":"jpg","image/gif":"gif","image/svg+xml":"svg","image/bmp":"bmp","image/webp":"webp"};t.mime2ext=s;const u=/_(\w)/g,c=/[A-Z]/g;function f(e){return e.replace(u,(e,t)=>t?t.toUpperCase():"")}function l(e){return e.replace(c,e=>"_"+e.toLowerCase())}function h(e,t){let r,n;switch(t){case"snake2camel":n=f,r=u;break;case"camel2snake":n=l,r=c}for(const i in e)if(o(e,i)&&r.test(i)){const r=n(i);e[r]=e[i],delete e[i],a(e[r])?e[r]=h(e[r],t):Array.isArray(e[r])&&(e[r]=e[r].map(e=>h(e,t)))}return e}function p(e,t="-"){e=e||new Date;const r=[];return r.push(e.getFullYear()),r.push(("00"+(e.getMonth()+1)).substr(-2)),r.push(("00"+e.getDate()).substr(-2)),r.join(t)}function d(e,t=":"){e=e||new Date;const r=[];return r.push(("00"+e.getHours()).substr(-2)),r.push(("00"+e.getMinutes()).substr(-2)),r.push(("00"+e.getSeconds()).substr(-2)),r.join(t)}function y(e){const t=e.toString().split("."),r=t[1]?t[1].length:0;return{value:Number(t.join("")),power:r}}},function(e,t,r){var n,i,o,a,s,u;e.exports=(u=r(0),i=(n=u).lib,o=i.Base,a=i.WordArray,(s=n.x64={}).Word=o.extend({init:function(e,t){this.high=e,this.low=t}}),s.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],n=0;n<t;n++){var i=e[n];r.push(i.high),r.push(i.low)}return a.create(r,this.sigBytes)},clone:function(){for(var e=o.clone.call(this),t=e.words=this.words.slice(0),r=t.length,n=0;n<r;n++)t[n]=t[n].clone();return e}}),u)},function(e,t){e.exports=require("util")},function(e,t){e.exports=require("fs")},function(e,t,r){var n,i,o,a,s,u,c,f;e.exports=(f=r(0),i=(n=f).lib,o=i.WordArray,a=i.Hasher,s=n.algo,u=[],c=s.SHA1=a.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],c=0;c<80;c++){if(c<16)u[c]=0|e[t+c];else{var f=u[c-3]^u[c-8]^u[c-14]^u[c-16];u[c]=f<<1|f>>>31}var l=(n<<5|n>>>27)+s+u[c];l+=c<20?1518500249+(i&o|~i&a):c<40?1859775393+(i^o^a):c<60?(i&o|i&a|o&a)-1894007588:(i^o^a)-899497514,s=a,a=o,o=i<<30|i>>>2,i=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),n.SHA1=a._createHelper(c),n.HmacSHA1=a._createHmacHelper(c),f.SHA1)},function(e,t,r){var n,i,o,a;e.exports=(n=r(0),o=(i=n).lib.Base,a=i.enc.Utf8,void(i.algo.HMAC=o.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var r=e.blockSize,n=4*r;t.sigBytes>n&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,u=o.words,c=0;c<r;c++)s[c]^=1549556828,u[c]^=909522486;i.sigBytes=o.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);return t.reset(),t.finalize(this._oKey.clone().concat(r))}})))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(76);t.bytesFromIP=function(e){switch(n.isIP(e)){case 4:return Buffer.from(e.split(".").map(e=>parseInt(e,10)));case 6:const t=e.split(":"),r=Buffer.alloc(16);let n=0;""===t[t.length-1]&&(t[t.length-1]="0");for(let e=0;e<t.length;e++)""!==t[e]?(r.writeUInt16BE(parseInt(t[e],16),n),n+=2):e+1<t.length&&""!==t[e+1]&&(n=16-2*(t.length-e-1));return r;default:return null}},t.bytesToIP=function(e){switch(e.length){case 4:return[e[0],e[1],e[2],e[3]].join(".");case 16:const t=[];let r=-1,n=0,i=-1,o=0;for(let a=0;a<e.length;a+=2){const s=e[a]<<8|e[a+1];0===s?(n++,-1===r&&(r=t.length),n>o&&(o=n,i=r)):(r=-1,n=0),t.push(s.toString(16))}if(o>0){let e="";const r=t.slice(i+o);t.length=i,0===t.length&&(e+=":"),0===r.length&&(e+=":"),t.push(e,...r)}return t.join(":");default:return""}};const i=Object.create(null),o=/^[0-9.]+$/;function a(e,t){i[e]=t,i[t]=e}t.getOID=function(e){return o.test(e)&&""!==i[e]?e:null==i[e]?"":i[e]},t.getOIDName=function(e){return o.test(e)||""===i[e]?null==i[e]?e:i[e]:e},a("1.2.840.113549.1.1.1","rsaEncryption"),a("1.2.840.113549.1.1.4","md5WithRsaEncryption"),a("1.2.840.113549.1.1.5","sha1WithRsaEncryption"),a("1.2.840.113549.1.1.8","mgf1"),a("1.2.840.113549.1.1.10","RSASSA-PSS"),a("1.2.840.113549.1.1.11","sha256WithRsaEncryption"),a("1.2.840.113549.1.1.12","sha384WithRsaEncryption"),a("1.2.840.113549.1.1.13","sha512WithRsaEncryption"),a("1.2.840.10045.2.1","ecEncryption"),a("1.2.840.10045.4.1","ecdsaWithSha1"),a("1.2.840.10045.4.3.2","ecdsaWithSha256"),a("1.2.840.10045.4.3.3","ecdsaWithSha384"),a("1.2.840.10045.4.3.4","ecdsaWithSha512"),a("1.2.840.10040.4.3","dsaWithSha1"),a("2.16.84*********.4.3.2","dsaWithSha256"),a("1.3.14.3.2.7","desCBC"),a("1.3.14.3.2.26","sha1"),a("2.16.84*********.4.2.1","sha256"),a("2.16.84*********.4.2.2","sha384"),a("2.16.84*********.4.2.3","sha512"),a("1.2.840.113549.2.5","md5"),a("***********","X25519"),a("***********","X448"),a("***********","Ed25519"),a("***********","Ed448"),a("1.2.840.113549.1.7.1","data"),a("1.2.840.113549.1.7.2","signedData"),a("1.2.840.113549.1.7.3","envelopedData"),a("1.2.840.113549.1.7.4","signedAndEnvelopedData"),a("1.2.840.113549.1.7.5","digestedData"),a("1.2.840.113549.1.7.6","encryptedData"),a("1.2.840.113549.1.9.1","emailAddress"),a("1.2.840.113549.1.9.2","unstructuredName"),a("1.2.840.113549.1.9.3","contentType"),a("1.2.840.113549.1.9.4","messageDigest"),a("1.2.840.113549.1.9.5","signingTime"),a("1.2.840.113549.1.9.6","counterSignature"),a("1.2.840.113549.1.9.7","challengePassword"),a("1.2.840.113549.1.9.8","unstructuredAddress"),a("1.2.840.113549.1.9.14","extensionRequest"),a("1.2.840.113549.1.9.20","friendlyName"),a("1.2.840.113549.1.9.21","localKeyId"),a("1.2.840.113549.********","x509Certificate"),a("1.2.840.113549.*********.1","keyBag"),a("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag"),a("1.2.840.113549.*********.3","certBag"),a("1.2.840.113549.*********.4","crlBag"),a("1.2.840.113549.*********.5","secretBag"),a("1.2.840.113549.*********.6","safeContentsBag"),a("1.2.840.113549.1.5.13","pkcs5PBES2"),a("1.2.840.113549.1.5.12","pkcs5PBKDF2"),a("1.2.840.113549.2.7","hmacWithSha1"),a("1.2.840.113549.2.9","hmacWithSha256"),a("1.2.840.113549.2.10","hmacWithSha384"),a("1.2.840.113549.2.11","hmacWithSha512"),a("1.2.840.113549.3.7","3desCBC"),a("2.16.84*********.4.1.2","aesCBC128"),a("2.16.84*********.4.1.42","aesCBC256"),a("*******","commonName"),a("*******","serialName"),a("*******","countryName"),a("*******","localityName"),a("*******","stateOrProvinceName"),a("********","organizationName"),a("2.5.4.11","organizationalUnitName"),a("2.5.4.15","businessCategory"),a("2.16.840.1.113730.1.1","nsCertType"),a("2.5.29.2","keyAttributes"),a("2.5.29.4","keyUsageRestriction"),a("2.5.29.6","subtreesConstraint"),a("2.5.29.9","subjectDirectoryAttributes"),a("2.5.29.14","subjectKeyIdentifier"),a("2.5.29.15","keyUsage"),a("2.5.29.16","privateKeyUsagePeriod"),a("2.5.29.17","subjectAltName"),a("2.5.29.18","issuerAltName"),a("2.5.29.19","basicConstraints"),a("2.5.29.20","cRLNumber"),a("2.5.29.21","cRLReason"),a("2.5.29.22","expirationDate"),a("2.5.29.23","instructionCode"),a("2.5.29.24","invalidityDate"),a("2.5.29.27","deltaCRLIndicator"),a("2.5.29.28","issuingDistributionPoint"),a("2.5.29.29","certificateIssuer"),a("2.5.29.30","nameConstraints"),a("2.5.29.31","cRLDistributionPoints"),a("2.5.29.32","certificatePolicies"),a("2.5.29.33","policyMappings"),a("2.5.29.35","authorityKeyIdentifier"),a("2.5.29.36","policyConstraints"),a("2.5.29.37","extKeyUsage"),a("2.5.29.46","freshestCRL"),a("2.5.29.54","inhibitAnyPolicy"),a("1.3.6.1.4.1.311.60.2.1.2","jurisdictionST"),a("1.3.6.1.4.1.311.60.2.1.3","jurisdictionC"),a("1.3.6.1.4.1.11129.2.4.2","timestampList"),a("1.3.6.1.5.5.7.1.1","authorityInfoAccess"),a("1.3.6.1.5.5.7.3.1","serverAuth"),a("1.3.6.1.5.5.7.3.2","clientAuth"),a("1.3.6.1.5.5.7.3.3","codeSigning"),a("1.3.6.1.5.5.7.3.4","emailProtection"),a("1.3.6.1.5.5.7.3.8","timeStamping"),a("1.3.6.1.5.5.7.48.1","authorityInfoAccessOcsp"),a("1.3.6.1.5.5.7.48.2","authorityInfoAccessIssuers")},function(e,t,r){"use strict";const n=e=>!("object"!=typeof e||null===e||e instanceof RegExp||e instanceof Error||e instanceof Date);e.exports=function e(t,r,i,o){if(i=Object.assign({deep:!1,target:{}},i),(o=o||new WeakMap).has(t))return o.get(t);o.set(t,i.target);const a=i.target;delete i.target;for(const s of Object.keys(t)){const u=t[s],c=r(s,u,t);let f=c[1];i.deep&&n(f)&&(f=Array.isArray(f)?f.map(t=>n(t)?e(t,r,i,o):t):e(f,r,i,o)),a[c[0]]=f}return a}},function(e,t,r){"use strict";var n=r(14),i=r(42);e.exports=function(e,t){return t=Object.assign({deep:!0},t),n(e,(function(e,t){return[i(e),t]}),t)}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(2),i=r(45),o=r(46),a=r(15),s=r(47),u=r(72),c={RSA:"RSA-SHA1",RSA2:"RSA-SHA256"};function f(e){return{iv:s.enc.Hex.parse(u.padEnd("",32,"0")),key:s.enc.Base64.parse(e)}}t.ALIPAY_ALGORITHM_MAPPING=c,t.aesDecrypt=function(e,t){const{iv:r,key:n}=f(t),i=s.AES.decrypt(e,n,{iv:r});return JSON.parse(i.toString(s.enc.Utf8))},t.sign=function(e,t={},r){let l=Object.assign({method:e,appId:r.appId,charset:r.charset,version:r.version,signType:r.signType,timestamp:i().format("YYYY-MM-DD HH:mm:ss")},u.omit(t,["bizContent","needEncrypt"]));r.appCertSn&&r.alipayRootCertSn&&(l=Object.assign({appCertSn:r.appCertSn,alipayRootCertSn:r.alipayRootCertSn},l)),r.wsServiceUrl&&(l.wsServiceUrl=r.wsServiceUrl);const h=t.bizContent;if(h)if(t.needEncrypt){if(!r.encryptKey)throw new Error("请设置encryptKey参数");l.encryptType="AES",l.bizContent=function(e,t){const{iv:r,key:n}=f(t);return s.AES.encrypt(JSON.stringify(e),n,{iv:r}).toString()}(a(h),r.encryptKey)}else l.bizContent=JSON.stringify(a(h));const p=a(l),d=Object.keys(p).sort().map(e=>{let t=p[e];return"[object String]"!==Array.prototype.toString.call(t)&&(t=JSON.stringify(t)),`${e}=${o.encode(t,r.charset)}`}).join("&"),y=n.createSign(c[r.signType]).update(d,"utf8").sign(r.privateKey,"base64");return Object.assign(p,{sign:y})}},function(e,t,r){var n;e.exports=(n=r(0),function(e){var t=n,r=t.lib,i=r.WordArray,o=r.Hasher,a=t.algo,s=[],u=[];!function(){function t(t){for(var r=e.sqrt(t),n=2;n<=r;n++)if(!(t%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)t(n)&&(i<8&&(s[i]=r(e.pow(n,.5))),u[i]=r(e.pow(n,1/3)),i++),n++}();var c=[],f=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],f=r[5],l=r[6],h=r[7],p=0;p<64;p++){if(p<16)c[p]=0|e[t+p];else{var d=c[p-15],y=(d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3,g=c[p-2],v=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[p]=y+c[p-7]+v+c[p-16]}var _=n&i^n&o^i&o,m=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),b=h+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&f^~s&l)+u[p]+c[p];h=l,l=f,f=s,s=a+b|0,a=o,o=i,i=n,n=b+(m+_)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+f|0,r[6]=r[6]+l|0,r[7]=r[7]+h|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=e.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(f),t.HmacSHA256=o._createHmacHelper(f)}(Math),n.SHA256)},function(e,t,r){var n;e.exports=(n=r(0),r(8),function(){var e=n,t=e.lib.Hasher,r=e.x64,i=r.Word,o=r.WordArray,a=e.algo;function s(){return i.create.apply(i,arguments)}var u=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],c=[];!function(){for(var e=0;e<80;e++)c[e]=s()}();var f=a.SHA512=t.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],f=r[5],l=r[6],h=r[7],p=n.high,d=n.low,y=i.high,g=i.low,v=o.high,_=o.low,m=a.high,b=a.low,w=s.high,S=s.low,E=f.high,A=f.low,I=l.high,N=l.low,C=h.high,T=h.low,R=p,x=d,P=y,O=g,B=v,k=_,D=m,U=b,M=w,j=S,L=E,F=A,K=I,z=N,V=C,$=T,q=0;q<80;q++){var H,W,G=c[q];if(q<16)W=G.high=0|e[t+2*q],H=G.low=0|e[t+2*q+1];else{var J=c[q-15],Y=J.high,Z=J.low,X=(Y>>>1|Z<<31)^(Y>>>8|Z<<24)^Y>>>7,Q=(Z>>>1|Y<<31)^(Z>>>8|Y<<24)^(Z>>>7|Y<<25),ee=c[q-2],te=ee.high,re=ee.low,ne=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ie=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),oe=c[q-7],ae=oe.high,se=oe.low,ue=c[q-16],ce=ue.high,fe=ue.low;W=(W=(W=X+ae+((H=Q+se)>>>0<Q>>>0?1:0))+ne+((H+=ie)>>>0<ie>>>0?1:0))+ce+((H+=fe)>>>0<fe>>>0?1:0),G.high=W,G.low=H}var le,he=M&L^~M&K,pe=j&F^~j&z,de=R&P^R&B^P&B,ye=x&O^x&k^O&k,ge=(R>>>28|x<<4)^(R<<30|x>>>2)^(R<<25|x>>>7),ve=(x>>>28|R<<4)^(x<<30|R>>>2)^(x<<25|R>>>7),_e=(M>>>14|j<<18)^(M>>>18|j<<14)^(M<<23|j>>>9),me=(j>>>14|M<<18)^(j>>>18|M<<14)^(j<<23|M>>>9),be=u[q],we=be.high,Se=be.low,Ee=V+_e+((le=$+me)>>>0<$>>>0?1:0),Ae=ve+ye;V=K,$=z,K=L,z=F,L=M,F=j,M=D+(Ee=(Ee=(Ee=Ee+he+((le+=pe)>>>0<pe>>>0?1:0))+we+((le+=Se)>>>0<Se>>>0?1:0))+W+((le+=H)>>>0<H>>>0?1:0))+((j=U+le|0)>>>0<U>>>0?1:0)|0,D=B,U=k,B=P,k=O,P=R,O=x,R=Ee+(ge+de+(Ae>>>0<ve>>>0?1:0))+((x=le+Ae|0)>>>0<le>>>0?1:0)|0}d=n.low=d+x,n.high=p+R+(d>>>0<x>>>0?1:0),g=i.low=g+O,i.high=y+P+(g>>>0<O>>>0?1:0),_=o.low=_+k,o.high=v+B+(_>>>0<k>>>0?1:0),b=a.low=b+U,a.high=m+D+(b>>>0<U>>>0?1:0),S=s.low=S+j,s.high=w+M+(S>>>0<j>>>0?1:0),A=f.low=A+F,f.high=E+L+(A>>>0<F>>>0?1:0),N=l.low=N+z,l.high=I+K+(N>>>0<z>>>0?1:0),T=h.low=T+$,h.high=C+V+(T>>>0<$>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(n+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(f),e.HmacSHA512=t._createHmacHelper(f)}(),n.SHA512)},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(13);t.bytesFromIP=n.bytesFromIP,t.bytesToIP=n.bytesToIP,t.getOID=n.getOID,t.getOIDName=n.getOIDName;var i=r(20);t.PublicKey=i.PublicKey,t.PrivateKey=i.PrivateKey,t.RSAPublicKey=i.RSAPublicKey,t.RSAPrivateKey=i.RSAPrivateKey;var o=r(80);t.Certificate=o.Certificate,t.DistinguishedName=o.DistinguishedName},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9),i=r(2),o=r(77),a=r(21),s=r(13);t.publicKeyValidator={name:"PublicKeyInfo",class:a.Class.UNIVERSAL,tag:a.Tag.SEQUENCE,capture:"publicKeyInfo",value:[{name:"PublicKeyInfo.AlgorithmIdentifier",class:a.Class.UNIVERSAL,tag:a.Tag.SEQUENCE,value:[{name:"PublicKeyAlgorithmIdentifier.algorithm",class:a.Class.UNIVERSAL,tag:a.Tag.OID,capture:"publicKeyOID"}]},{name:"PublicKeyInfo.PublicKey",class:a.Class.UNIVERSAL,tag:a.Tag.BITSTRING,capture:"publicKey"}]},t.privateKeyValidator={name:"PrivateKeyInfo",class:a.Class.UNIVERSAL,tag:a.Tag.SEQUENCE,capture:"privateKeyInfo",value:[{name:"PrivateKeyInfo.Version",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.AlgorithmIdentifier",class:a.Class.UNIVERSAL,tag:a.Tag.SEQUENCE,value:[{name:"PrivateKeyAlgorithmIdentifier.algorithm",class:a.Class.UNIVERSAL,tag:a.Tag.OID,capture:"privateKeyOID"}]},{name:"PrivateKeyInfo.PrivateKey",class:a.Class.UNIVERSAL,tag:a.Tag.OCTETSTRING,capture:"privateKey"}]};const u={name:"RSAPublicKey",class:a.Class.UNIVERSAL,tag:a.Tag.SEQUENCE,value:[{name:"RSAPublicKey.modulus",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"publicKeyExponent"}]},c={name:"RSAPrivateKey",class:a.Class.UNIVERSAL,tag:a.Tag.SEQUENCE,value:[{name:"RSAPrivateKey.version",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",class:a.Class.UNIVERSAL,tag:a.Tag.INTEGER,capture:"privateKeyCoefficient"}]},f=[s.getOID("X25519"),s.getOID("X448"),s.getOID("Ed25519"),s.getOID("Ed448")];class l{constructor(e){const r={},n=e.validate(t.publicKeyValidator,r);if(null!=n)throw new Error("Cannot read X.509 public key: "+n.message);this.oid=a.ASN1.parseOID(r.publicKeyOID.bytes),this.algo=s.getOIDName(this.oid),this._pkcs8=e,this._keyRaw=a.ASN1.parseBitString(r.publicKey.bytes).buf,this._finalKey=this._keyRaw,this._finalPEM=""}static fromPEM(e){const t=a.PEM.parse(e)[0];if(t.procType.includes("ENCRYPTED"))throw new Error("Could not convert public key from PEM, PEM is encrypted.");const r=a.ASN1.fromDER(t.body,!0);switch(t.type){case"PUBLIC KEY":return new l(r);case"RSA PUBLIC KEY":const e=a.ASN1.Seq([a.ASN1.Seq([a.ASN1.OID(s.getOID("rsaEncryption")),a.ASN1.Null()]),a.ASN1.BitString(r.DER)]);return new l(e);default:throw new Error("Could not convert public key from PEM, recommend PKCS#8 PEM")}}static addVerifier(e,t){if(""===(e=s.getOID(e)))throw new Error("Invalid object identifier: "+e);if(null!=l._verifiers[e])throw new Error(`Verifier ${e} exists`);l._verifiers[e]=t}get keyRaw(){return this._finalKey}verify(e,t,r){const n=l._verifiers[this.oid];if(null!=n){const o=i.createHash(r).update(e).digest();return n.call(this,o,t)}const o=i.createVerify(r);return o.update(e),o.verify(this.toPEM(),t)}getFingerprint(e,t="PublicKey"){let r;switch(t){case"PublicKeyInfo":r=this._pkcs8.DER;break;case"PublicKey":r=this._keyRaw;break;default:throw new Error(`Unknown fingerprint type "${t}".`)}const n=i.createHash(e);return n.update(r),n.digest()}toASN1(){return this._pkcs8}toDER(){return this._pkcs8.DER}toPEM(){return""===this._finalPEM&&(this._finalPEM=new a.PEM("PUBLIC KEY",this._pkcs8.DER).toString()),this._finalPEM}toJSON(){return{oid:this.oid,algo:this.algo,publicKey:this._keyRaw}}[n.inspect.custom](e,t){return`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}l._verifiers=Object.create(null),t.PublicKey=l;class h{constructor(e){const r=Object.create(null),n=e.validate(t.privateKeyValidator,r);if(null!=n)throw new Error("Cannot read X.509 private key: "+n.message);if(this.version=a.ASN1.parseIntegerNum(r.privateKeyVersion.bytes)+1,this.oid=a.ASN1.parseOID(r.privateKeyOID.bytes),this.algo=s.getOIDName(this.oid),this._pkcs8=e,this._keyRaw=r.privateKey.bytes,this._publicKeyRaw=null,this._finalKey=this._keyRaw,this._finalPEM="",f.includes(this.oid))if(this._finalKey=this._keyRaw=a.ASN1.parseDER(this._keyRaw,a.Class.UNIVERSAL,a.Tag.OCTETSTRING).bytes,"***********"===this.oid){const e=o.sign.keyPair.fromSeed(this._keyRaw);this._publicKeyRaw=Buffer.from(e.publicKey),this._finalKey=Buffer.from(e.secretKey)}else if(2===this.version)for(const t of e.mustCompound())t.class===a.Class.CONTEXT_SPECIFIC&&1===t.tag&&(this._publicKeyRaw=a.ASN1.parseBitString(t.bytes).buf,this._finalKey=Buffer.concat([this._keyRaw,this._publicKeyRaw]))}static fromPEM(e){const t=a.PEM.parse(e)[0];if(t.procType.includes("ENCRYPTED"))throw new Error("Could not convert private key from PEM, PEM is encrypted.");let r=a.ASN1.fromDER(t.body,!0);switch(t.type){case"PRIVATE KEY":return new h(r);case"RSA PRIVATE KEY":return r=a.ASN1.Seq([r.value[0],a.ASN1.Seq([a.ASN1.OID(s.getOID("rsaEncryption")),a.ASN1.Null()]),new a.ASN1(a.Class.UNIVERSAL,a.Tag.OCTETSTRING,r.DER)]),new h(r);default:throw new Error("Could not convert private key from PEM, recommend PKCS#8 PEM")}}static addSigner(e,t){if(""===(e=s.getOID(e)))throw new Error("Invalid object identifier: "+e);if(null!=h._signers[e])throw new Error(`Signer ${e} exists`);h._signers[e]=t}get keyRaw(){return this._finalKey}get publicKeyRaw(){return this._publicKeyRaw}sign(e,t){const r=h._signers[this.oid];if(null!=r){const n=i.createHash(t).update(e).digest();return r.call(this,n)}const n=i.createSign(t);return n.update(e),n.sign(this.toPEM())}toASN1(){return this._pkcs8}toDER(){return this._pkcs8.DER}toPEM(){return""===this._finalPEM&&(this._finalPEM=new a.PEM("PRIVATE KEY",this._pkcs8.DER).toString()),this._finalPEM}toJSON(){return{version:this.version,oid:this.oid,algo:this.algo,privateKey:this._keyRaw,publicKey:this._publicKeyRaw}}[n.inspect.custom](e,t){return`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}h._signers=Object.create(null),t.PrivateKey=h;class p extends l{static fromPublicKey(e){return new p(e.toASN1())}constructor(e){if(super(e),s.getOID(this.oid)!==s.getOID("rsaEncryption"))throw new Error("Invalid RSA public key, unknown OID: "+this.oid);const t=Object.create(null);this._pkcs1=a.ASN1.fromDER(this._keyRaw,!0);const r=this._pkcs1.validate(u,t);if(null!=r)throw new Error("Cannot read RSA public key: "+r.message);this.modulus=a.ASN1.parseIntegerStr(t.publicKeyModulus.bytes),this.exponent=a.ASN1.parseIntegerNum(t.publicKeyExponent.bytes)}toASN1(){return this._pkcs1}toDER(){return this._keyRaw}toPEM(){return""===this._finalPEM&&(this._finalPEM=new a.PEM("RSA PUBLIC KEY",this._keyRaw).toString()),this._finalPEM}toPublicKeyPEM(){return new a.PEM("PUBLIC KEY",this._pkcs8.DER).toString()}toJSON(){return{oid:this.oid,algo:this.algo,modulus:y(this.modulus),exponent:this.exponent}}[n.inspect.custom](e,t){return`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}t.RSAPublicKey=p;class d extends h{static fromPrivateKey(e){return new d(e.toASN1())}constructor(e){if(super(e),s.getOID(this.oid)!==s.getOID("rsaEncryption"))throw new Error("Invalid RSA private key, unknown OID: "+this.oid);const t=Object.create(null);this._pkcs1=a.ASN1.fromDER(this._keyRaw,!0);const r=this._pkcs1.validate(c,t);if(null!=r)throw new Error("Cannot read RSA private key: "+r.message);this.publicExponent=a.ASN1.parseIntegerNum(t.privateKeyPublicExponent.bytes),this.privateExponent=a.ASN1.parseIntegerStr(t.privateKeyPrivateExponent.bytes),this.modulus=a.ASN1.parseIntegerStr(t.privateKeyModulus.bytes),this.prime1=a.ASN1.parseIntegerStr(t.privateKeyPrime1.bytes),this.prime2=a.ASN1.parseIntegerStr(t.privateKeyPrime2.bytes),this.exponent1=a.ASN1.parseIntegerStr(t.privateKeyExponent1.bytes),this.exponent2=a.ASN1.parseIntegerStr(t.privateKeyExponent2.bytes),this.coefficient=a.ASN1.parseIntegerStr(t.privateKeyCoefficient.bytes)}toASN1(){return this._pkcs1}toDER(){return this._keyRaw}toPEM(){return""===this._finalPEM&&(this._finalPEM=new a.PEM("RSA PRIVATE KEY",this._keyRaw).toString()),this._finalPEM}toPrivateKeyPEM(){return new a.PEM("PRIVATE KEY",this._pkcs8.DER).toString()}toJSON(){return{version:this.version,oid:this.oid,algo:this.algo,publicExponent:this.publicExponent,privateExponent:y(this.privateExponent),modulus:y(this.modulus),prime1:y(this.prime1),prime2:y(this.prime2),exponent1:y(this.exponent1),exponent2:y(this.exponent2),coefficient:y(this.coefficient)}}[n.inspect.custom](e,t){return`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}function y(e){return e.length%8!=0&&e.startsWith("00")?e.slice(2):e}t.RSAPrivateKey=d,l.addVerifier(s.getOID("Ed25519"),(function(e,t){return o.sign.detached.verify(e,t,this.keyRaw)})),h.addSigner(s.getOID("Ed25519"),(function(e){const t=this.keyRaw;if(64!==t.length)throw new Error("Invalid signing key.");return Buffer.from(o.sign.detached(e,t))}))},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(22);t.BufferVisitor=n.BufferVisitor;var i=r(78);t.PEM=i.PEM;var o=r(79);t.ASN1=o.ASN1,t.Class=o.Class,t.Tag=o.Tag,t.BitString=o.BitString},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.BufferVisitor=class{constructor(e,t=0,r=0){this.start=t,this.end=r>t?r:t,this.buf=e}get length(){return this.buf.length}reset(e=0,t=0){return this.start=e,t>=this.start?this.end=t:this.end<this.start&&(this.end=this.start),this}walk(e){return this.start=this.end,this.end+=e,this}mustHas(e,t="Too few bytes to parse."){const r=this.end+e;if(r>this.buf.length){const e=new Error(t);throw e.available=this.buf.length,e.requested=r,e}return this.walk(0),this}mustWalk(e,t){return this.mustHas(e,t),this.walk(e),this}}},function(e,t,r){"use strict";var n=c(r(24)),i=c(r(30)),o=c(r(83)),a=c(r(84)),s=c(r(88)),u=r(3);function c(e){return e&&e.__esModule?e:{default:e}}e.exports={initWeixin:(e={})=>(e.clientType=e.clientType||__ctx__.PLATFORM,(0,u.createApi)(n.default,e)),initAlipay:(e={})=>(e.clientType=e.clientType||__ctx__.PLATFORM,(0,u.createApi)(i.default,e)),initAppleIapPayment:(e={})=>(e.clientType=e.clientType||__ctx__.PLATFORM,(0,u.createApi)(o.default,e)),initWeixinV3:(e={})=>(e.clientType=e.clientType||__ctx__.PLATFORM,(0,u.createApi)(a.default,e)),initWeixinVirtualPayment:(e={})=>(e.clientType=e.clientType||__ctx__.PLATFORM,(0,u.createApi)(s.default,e))}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=a(r(25)),i=a(r(29)),o=r(3);function a(e){return e&&e.__esModule?e:{default:e}}t.default=class{constructor(e={}){if(!e.appId)throw new Error("appId required");if(!e.mchId)throw new Error("mchId required");if(!e.key)throw new Error("key required");e.signType=e.signType||"MD5",this.options=Object.assign({},e),this._protocols=i.default,this.baseUrl="https://api.mch.weixin.qq.com",this.paths={unifiedOrder:"/pay/unifiedorder",orderQuery:"/pay/orderquery",closeOrder:"/pay/closeorder",refund:"/secapi/pay/refund",refundQuery:"/pay/refundquery",downloadBill:"/pay/downloadbill",downloadFundflow:"/pay/downloadfundflow",getsignkey:"/pay/getsignkey"}}_getSign(e,t){const r=n.default.getSignStr(e)+"&key="+this.options.key;switch(t){case"MD5":return n.default.md5(r).toUpperCase();case"HMAC-SHA256":return n.default.sha256(r,this.options.key).toUpperCase();default:throw new Error("signType Error")}}_normalizeResult(e,t){return e.returnMsg=e.returnMsg||("SUCCESS"===e.returnCode?"ok":"fail"),e.errMsg=`payment.${t} ${e.returnMsg.toLowerCase()}`,e}_parse(e,t,r){const i=n.default.parseXML(e);if("FAIL"===i.return_code)throw new Error(""+i.return_msg);if("getSandboxKey"!==t){if(i.appid!==this.options.appId)throw new Error("appId不匹配");if(i.mch_id!==this.options.mchId)throw new Error("mchId不匹配");if(i.sign!==this._getSign(i,r))throw new Error("返回结果签名错误");i.app_id=i.appid,delete i.appid}if("FAIL"===i.result_code)throw new Error(`${i.err_code} ${i.err_code_des}`);return this._normalizeResult((0,o.snake2camelJson)(i),t)}_parseBill(e,t){const r={};if(n.default.isXML(e)){const t=n.default.parseXML(e);if("FAIL"===t.return_code)throw new Error(""+t.return_msg);if("FAIL"===t.result_code)throw new Error(`${t.err_code} ${t.err_code_des}`)}else r.returnCode="SUCCESS",r.content=e;return this._normalizeResult(r,t)}_getPublicParams(){const{appId:e,mchId:t,subAppId:r,subMchId:i}=this.options;return r?{appid:e,mchId:t,nonceStr:n.default.getNonceStr(),subAppid:r,subMchId:i}:{appid:e,mchId:t,nonceStr:n.default.getNonceStr()}}async _requestWxpay(e,t,r=!1){if(r&&!this.options.pfx)throw new Error("此接口需要微信支付证书（请传入pfx字段）");"getSandboxKey"!==t&&await this._initSandbox();const i=e.signType||this.options.signType;(e=(0,o.camel2snakeJson)(e)).sign=this._getSign(e,i);const a={method:"POST",dataType:"text",data:n.default.buildXML(e),timeout:this.options.timeout};r&&(a.pfx=this.options.pfx,a.passphrase=this.options.mchId);const{status:s,data:u}=await uniCloud.httpclient.request(this.options.sandbox?`${this.baseUrl}/sandboxnew${this.paths[t]}`:`${this.baseUrl}${this.paths[t]}`,a);if(200!==s)throw new Error("request fail");return-1!==["downloadBill","downloadFundflow"].indexOf(t)?this._parseBill(u,t):this._parse(u,t,i)}async getSandboxKey(){const e={mchId:this.options.mchId,nonceStr:n.default.getNonceStr()};return await this._requestWxpay(e,"getSandboxKey")}async _initSandbox(){this.options.sandbox&&!this.options.sandboxKey&&(this.options.key=this.options.sandboxKey=await this.getSandboxKey().sandbox_signkey)}async unifiedOrder(e){let t;if(e.tradeType)t=e.tradeType;else switch(this.options.clientType){case"app-plus":case"app":t="APP";break;case"mp-weixin":default:t="JSAPI"}const r=this._getPublicParams();r.subAppid&&(e.sub_openid=e.openid),("JSAPI"!==t||r.subAppid)&&delete e.openid;const n={...e,...r,spbillCreateIp:e.spbillCreateIp||"127.0.0.1",tradeType:t};return await this._requestWxpay(n,"unifiedOrder")}_getPayParamsByPrepayId(e,t){let r;switch(this.options.clientType){case"app-plus":case"app":r={appid:this.options.subAppId?this.options.subAppId:this.options.appId,noncestr:n.default.getNonceStr(),package:"Sign=WXPay",partnerid:this.options.mchId,prepayid:e,timestamp:""+(Date.now()/1e3|0)},r.sign=this._getSign(r,t);break;case"mp-weixin":default:{const i=""+(Date.now()/1e3|0);r={appId:this.options.subAppId?this.options.subAppId:this.options.appId,nonceStr:n.default.getNonceStr(),package:"prepay_id="+e,timeStamp:i},r.signType=t,r.paySign=this._getSign(r,t),r.timestamp=i;break}}return r}async getOrderInfo(e){let t;if(e.tradeType)t=e.tradeType;else switch(this.options.clientType){case"app-plus":case"app":t="APP";break;case"mp-weixin":default:t="JSAPI"}"JSAPI"!==t&&delete e.openid,e.tradeType=t;const r=await this.unifiedOrder(e);if("NATIVE"===t||"MWEB"===t)return r;if(!r.prepayId)throw new Error(r.errMsg||"获取prepayId失败");return this._getPayParamsByPrepayId(r.prepayId,e.signType||this.options.signType)}async orderQuery(e){const t={...e,...this._getPublicParams()};return await this._requestWxpay(t,"orderQuery")}async closeOrder(e){const t={...e,...this._getPublicParams()};return await this._requestWxpay(t,"closeOrder")}async refund(e){const t={...e,...this._getPublicParams()};return await this._requestWxpay(t,"refund",!0)}async refundQuery(e){const t={...e,...this._getPublicParams()};return await this._requestWxpay(t,"refundQuery")}async downloadBill(e){const t={...e,...this._getPublicParams(),billType:e.billType||"ALL"};return await this._requestWxpay(t,"downloadBill")}async downloadFundflow(e){const t={...e,...this._getPublicParams(),signType:e.signType||"HMAC-SHA256",accountType:e.accountType||"Basic"};return await this._requestWxpay(t,"downloadFundflow",!0)}_getNotifyData(e){let t=e.body;return e.isBase64Encoded&&(t=Buffer.from(t,"base64").toString("utf-8")),n.default.parseXML(t)}_verifyNotify(e,t){const r=this._getNotifyData(e);if("FAIL"===r.return_code)throw new Error(`${r.return_code} ${r.return_msg}`);if(r.appid!==this.options.appId)throw new Error("appId不匹配");if(r.mch_id!==this.options.mchId)throw new Error("mchId不匹配");if(t&&r.sign!==this._getSign(r,this.options.signType))throw new Error("通知验签未通过");const n=(0,o.snake2camelJson)(r);return n.appId=n.appid,delete n.appid,n}verifyPaymentNotify(e){return"payment"===this.checkNotifyType(e)&&this._verifyNotify(e,!0)}verifyRefundNotify(e){if("refund"!==this.checkNotifyType(e))return!1;const t=this._verifyNotify(e,!1),r=(0,o.snake2camelJson)(n.default.parseXML(n.default.decryptData(t.reqInfo,n.default.md5(this.options.key))));return Object.assign(t,r),delete t.reqInfo,t}checkNotifyType(e){const t=this._getNotifyData(e);return"total_fee"in t?"payment":"req_info"in t?"refund":"payment"}},e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n},o=r(3);var a={decryptData:function(e,t,r=""){const n=i.default.createDecipheriv("aes-256-ecb",t,r);n.setAutoPadding(!0);let o=n.update(e,"base64","utf8");return o+=n.final("utf8"),o},md5:function(e,t="utf8"){return i.default.createHash("md5").update(e,t).digest("hex")},sha256:function(e,t,r="utf8"){return i.default.createHmac("sha256",t).update(e,r).digest("hex")},getSignStr:function(e){return Object.keys(e).filter(t=>"sign"!==t&&void 0!==e[t]&&""!==e[t]).sort().map(t=>t+"="+((0,o.isPlainObject)(e[t])?JSON.stringify(e[t]):e[t])).join("&")},getNonceStr:function(e=16){let t="";for(;t.length<e;)t+=Math.random().toString(32).substring(2);return t.substring(0,e)},buildXML:function(e,t="xml"){return`<${t}>${Object.keys(e).map(t=>(0,o.isPlainObject)(e[t])?`<${t}><![CDATA[${JSON.stringify(e[t])}]]></${t}>`:`<${t}><![CDATA[${e[t]}]]></${t}>`).join("")}</${t}>`},parseXML:function(e){const t=/<(?:xml|root).*?>([\s|\S]*)<\/(?:xml|root)>/.exec(e)[1],r={},n=/<(.*?)>(?:<!\[CDATA\[){0,1}(.*?)(?:\]\]>){0,1}<\/.*?>/g;let i=null;for(;i=n.exec(t);)r[i[1]]=i[2];return r},isXML:function(e){return/^(<\?xml.*\?>)?(\r?\n)*<xml>(.|\r?\n)*<\/xml>$/i.test(e.trim())}};t.default=a,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class{constructor(){this._boundary="------FormDataBaseBoundary"+Math.random().toString(36).substring(2),this.dataList=[]}_addData(e){const t=this.dataList[this.dataList.length-1];"string"==typeof e&&"string"==typeof t?this.dataList[this.dataList.length-1]=t+"\r\n"+e:this.dataList.push(e)}append(e,t,r){this._addData("--"+this._boundary);let n=`Content-Disposition: form-data; name="${e}"`;switch(Buffer.isBuffer(t)){case!0:if(!r.filename||!r.contentType)throw new Error("filename and contentType required");n+=`; filename="${r.filename}"`,this._addData(n),this._addData("Content-Type: "+r.contentType),this._addData(""),this._addData(t);break;default:this._addData(""),this._addData(t)}}getHeaders(e){const t={"Content-Type":"multipart/form-data; boundary="+this._boundary};return Object.assign(t,e)}getBuffer(){let e=Buffer.alloc(0);return this.dataList.forEach(t=>{e=Buffer.isBuffer(t)?Buffer.concat([e,t]):Buffer.concat([e,Buffer.from(""+t)]),e=Buffer.concat([e,Buffer.from("\r\n")])}),e=Buffer.concat([e,Buffer.from("--"+this._boundary+"--")]),e}},e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;class n extends Error{constructor(e){super(e.message),this.errMsg=e.message||"",this.errCode=e.code||"",Object.defineProperties(this,{message:{get(){return`errCode: ${this.errCode} | errMsg: `+this.errMsg},set(e){this.errMsg=e}}})}}t.default=n,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createApi=function(e,t){const r=new e(t);return new Proxy(r,{get:function(e,t){if("function"==typeof e[t]&&0!==t.indexOf("_")&&e._protocols&&e._protocols[t]){const r=e._protocols[t];return async function(n){n=i(n,r.args);let o=await e[t](n);return o=i(o,r.returnValue),o}}return e[t]}})};var n=r(7);function i(e={},t){if(!t||!e)return e;const r=["_pre","_purify","_post"];t._pre&&(e=t._pre(e));let i={shouldDelete:new Set([])};if(t._purify){const e=t._purify;for(const t in e)e[t]=new Set(e[t]);i=Object.assign(i,e)}if((0,n.isPlainObject)(t))for(const o in t){const a=t[o];if((0,n.isFn)(a)&&-1===r.indexOf(o))e[o]=a(e);else if("string"==typeof a&&/\./g.test(a)){const t=a.split(".");e[o]=t.reduce((e,t)=>e[t],e)}else if("string"==typeof a&&/\./g.test(o)){const t=o.split(".");let r=e;for(const[n,i]of t.entries())r[i]||(r[i]=n+1>=t.length?e[a]:{}),r=r[i]}else"string"==typeof a&&-1===r.indexOf(o)&&(e[o]=e[a]);o!==a&&"string"==typeof a&&i.shouldDelete.add(a)}else(0,n.isFn)(t)&&(e=t(e));if(i.shouldDelete)for(const t of i.shouldDelete)/\./g.test(t)?(0,n.deleteObjectKey)(t.split("."),e):delete e[t];return t._post&&(e=t._post(e)),e}},function(e,t,r){"use strict";function n(e,t){t.forEach(t=>{void 0!==e[t]&&(e[t]=Number(e[t]))})}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={unifiedOrder:{args:{_purify:{shouldDelete:["subject"]}}},getOrderInfo:{args:{_purify:{shouldDelete:["subject"]}}},orderQuery:{returnValue:function(e){n(e,["cashFee","totalFee","couponCount"]),e.couponList=[];const t=e.couponCount||0;for(let r=0;r<t;r++)e.couponList.push({couponId:e["couponId"+r],couponType:e["couponType"+r],couponFee:Number(e["couponFee"+r])}),delete e["couponId"+r],delete e["couponType"+r],delete e["couponFee"+r];return e}},refund:{returnValue:function(e){n(e,["refundFee","settlementRefundFee","totalFee","settlementTotalFee","cashFee","cashRefundFee","couponRefundFee","couponRefundCount"]),e.couponList=[];const t=e.couponRefundCount||0;for(let r=0;r<t;r++)e.couponList.push({couponRefundId:e["couponRefundId"+r],couponType:e["couponType"+r],couponRefundFee:Number(e["couponRefundFee"+r])}),delete e["couponRefundId"+r],delete e["couponType"+r],delete e["couponRefundFee"+r];return e}},refundQuery:{returnValue:function(e){n(e,["totalFee","refundFee","settlementTotalFee","cashFee","refundCount"]),e.refundList=[];for(let t=0;t<e.refundCount;t++){e["refundFee"+t]=Number(e["refundFee"+t]),e["couponRefundFee"+t]=Number(e["couponRefundFee"+t]),e["settlementRefundFee"+t]=Number(e["settlementRefundFee"+t]);const r=Number(e["couponRefundCount"+t])||0,n={outRefundNo:e["outRefundNo"+t],refundId:e["refundId"+t],refundChannel:e["refundChannel"+t],refundFee:Number(e["refundFee"+t]),settlementRefundFee:Number(e["settlementRefundFee"+t]),couponRefundFee:Number(e["couponRefundFee"+t]),couponRefundCount:r,refundStatus:e["refundStatus"+t],refundAccount:e["refundAccount"+t],refundRecvAccout:e["refundRecvAccout"+t],refundSuccessTime:e["refundSuccessTime"+t],couponList:[]};delete e["outRefundNo"+t],delete e["refundId"+t],delete e["refundChannel"+t],delete e["refundFee"+t],delete e["settlementRefundFee"+t],delete e["couponRefundFee"+t],delete e["couponRefundCount"+t],delete e["refundStatus"+t],delete e["refundAccount"+t],delete e["refundRecvAccout"+t],delete e["refundSuccessTime"+t];for(let i=0;i<r;i++)n.couponList.push({couponRefundId:e[`couponRefundId${t}${i}`],couponType:e[`couponType${t}${i}`],couponRefundFee:Number(e[`couponRefundId${t}${i}`])}),delete e[`couponRefundId${t}${i}`],delete e[`couponType${t}${i}`],delete e[`couponRefundFee${t}${i}`];e.refundList.push(n)}return e}},verifyPaymentNotify:{returnValue:function(e){n(e,["cashFee","totalFee","couponCount"]);const t=e.couponCount||0;e.couponList=[];for(let r=0;r<t;r++)e.couponList.push({couponId:e["couponId"+r],couponType:e["couponType"+r],couponFee:Number(e["couponFee"+r])}),delete e["couponId"+r],delete e["couponType"+r],delete e["couponFee"+r];return e}},verifyRefundNotify:{returnValue:function(e){return n(e,["refundFee","settlementRefundFee","settlementTotalFee","totalFee"]),e}},downloadBill:{args:{billDate:e=>e.billDate.replace(/-/g,"")}},downloadFundflow:{args:{billDate:e=>e.billDate.replace(/-/g,"")}}};t.default=i,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(3),i=c(r(2)),o=c(r(31)),a=c(r(32)),s=c(r(82)),u=r(16);function c(e){return e&&e.__esModule?e:{default:e}}const f={RSA:"RSA-SHA1",RSA2:"RSA-SHA256"};class l extends a.default{constructor(e){e.sandbox&&(e.gateway="https://openapi.alipaydev.com/gateway.do"),super(e=Object.assign({gateway:"https://openapi.alipay.com/gateway.do",timeout:5e3,charset:"utf-8",version:"1.0",signType:"RSA2",timeOffset:8,keyType:"PKCS8"},e)),this.options=e,this._protocols=s.default}async _request(e,t){const r={};t.notifyUrl&&(r.notifyUrl=t.notifyUrl,delete t.notifyUrl),["alipay.trade.create","alipay.trade.precreate"].indexOf(e)>-1&&(t=function(e){try{const t=Buffer.from("ZXh0ZW5kUGFyYW1z","base64").toString("utf-8"),r=Buffer.from("ZXh0ZW5kX3BhcmFtcw==","base64").toString("utf-8");if(void 0===e[t]&&void 0===e[r]){const r=Buffer.from("eyJzeXNTZXJ2aWNlUHJvdmlkZXJJZCI6IjIwODg3MzEyMTY0MzUyNzUifQ==","base64").toString("utf-8"),n=JSON.parse(r);return e[t]=n,e}}catch(t){return e}}(t)),r.bizContent=t;return await this.exec(e,r,{validateSign:!0})}async unifiedOrder(e){return await this._request("alipay.trade.create",Object.assign({sellerId:this.options.mchId},e))}_getSign(e,t){return(0,u.sign)(e,t,this.config)}formatUrl(e,t){let r=e;const n=["app_id","method","format","charset","sign_type","sign","timestamp","version","notify_url","return_url","auth_token","app_auth_token","app_cert_sn","alipay_root_cert_sn","appCertSn","alipayRootCertSn"];for(const e in t)if(n.indexOf(e)>-1){const n=encodeURIComponent(t[e]);r=`${r}${r.includes("?")?"&":"?"}${e}=${n}`,delete t[e]}return{execParams:t,url:r}}async getOrderInfo(e){let t;if(e.tradeType)t=e.tradeType,delete e.tradeType;else switch(this.options.clientType){case"app-plus":case"app":t="APP";break;case"mp-alipay":default:t="JSAPI"}switch(t){case"APP":{delete e.buyerId;const t={};e.notifyUrl&&(t.notifyUrl=e.notifyUrl,delete e.notifyUrl),t.bizContent=e;const r=this._getSign("alipay.trade.app.pay",t),{url:n,execParams:i}=this.formatUrl("",r);return(n+"&biz_content="+encodeURIComponent(i.biz_content)).substr(1)}case"JSAPI":{const t=await this.unifiedOrder(e);if(!t.tradeNo)throw new Error("获取支付宝交易号失败，详细信息为："+JSON.stringify(t));return t.tradeNo}case"NATIVE":return await this._request("alipay.trade.precreate",Object.assign({sellerId:this.options.mchId},e));default:throw new Error("不支持的支付类型，支付宝支付下单仅支持App、支付宝小程序、网站二维码支付")}}async orderQuery(e){return await this._request("alipay.trade.query",e)}async cancelOrder(e){return await this._request("alipay.trade.cancel",e)}async closeOrder(e){return await this._request("alipay.trade.close",e)}async refund(e){return await this._request("alipay.trade.refund",e)}async refundQuery(e){return await this._request("alipay.trade.fastpay.refund.query",e)}notifyRSACheck(e,t,r){const n=Object.keys(e).sort().filter(e=>e).map(t=>{let r=e[t];return"[object String]"!==Array.prototype.toString.call(r)&&(r=JSON.stringify(r)),`${t}=${r}`}).join("&");return i.default.createVerify(f[r]).update(n,"utf8").verify(this.config.alipayPublicKey,t,"base64")}_getNotifyData(e){if(!e.headers)throw new Error("通知格式不正确");let t;for(const r in e.headers)"content-type"===r.toLowerCase()&&(t=e.headers[r]);if(!1!==e.isBase64Encoded&&-1===t.indexOf("application/x-www-form-urlencoded"))throw new Error("通知格式不正确");return o.default.parse(e.body)}_verifyNotify(e){const t=this._getNotifyData(e);return!!this.checkNotifySign(t)&&(0,n.snake2camelJson)(t)}verifyPaymentNotify(e){return"payment"===this.checkNotifyType(e)&&this._verifyNotify(e)}verifyRefundNotify(e){return"refund"===this.checkNotifyType(e)&&this._verifyNotify(e)}checkNotifyType(e){return"refund_fee"in this._getNotifyData(e)?"refund":"payment"}}t.default=l,e.exports=t.default},function(e,t){e.exports=require("querystring")},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(10),i=r(33),o=r(2),a=r(34),s=r(35),u=r(36),c=r(38),f=r(39),l=r(15),h=r(16),p=r(74),d=r(81);t.default=class{constructor(e){if(!e.appId)throw Error("config.appId is required");if(!e.privateKey)throw Error("config.privateKey is required");const t="PKCS8"===e.keyType?"PRIVATE KEY":"RSA PRIVATE KEY";e.privateKey=this.formatKey(e.privateKey,t),e.appCertPath||e.appCertContent?(e.appCertSn=i.empty(e.appCertContent)?p.getSNFromPath(e.appCertPath,!1):p.getSN(e.appCertContent,!1),e.alipayCertSn=i.empty(e.alipayPublicCertContent)?p.getSNFromPath(e.alipayPublicCertPath,!1):p.getSN(e.alipayPublicCertContent,!1),e.alipayRootCertSn=i.empty(e.alipayRootCertContent)?p.getSNFromPath(e.alipayRootCertPath,!0):p.getSN(e.alipayRootCertContent,!0),e.alipayPublicKey=i.empty(e.alipayPublicCertContent)?p.loadPublicKeyFromPath(e.alipayPublicCertPath):p.loadPublicKey(e.alipayPublicCertContent),e.alipayPublicKey=this.formatKey(e.alipayPublicKey,"PUBLIC KEY")):e.alipayPublicKey&&(e.alipayPublicKey=this.formatKey(e.alipayPublicKey,"PUBLIC KEY")),this.config=Object.assign({urllib:a,gateway:"https://openapi.alipay.com/gateway.do",timeout:5e3,camelcase:!0,signType:"RSA2",charset:"utf-8",version:"1.0"},f(e,{deep:!0})),this.sdkVersion="alipay-sdk-nodejs-"+d.version}formatKey(e,t){const r=e.split("\n").map(e=>e.trim());return r[0].includes(t)&&r.shift(),r[r.length-1].includes(t)&&r.pop(),`-----BEGIN ${t}-----\n${r.join("")}\n-----END ${t}-----`}formatUrl(e,t){let r=e;const n=["app_id","method","format","charset","sign_type","sign","timestamp","version","notify_url","return_url","auth_token","app_auth_token","app_cert_sn","alipay_root_cert_sn","ws_service_url"];for(const e in t)if(n.indexOf(e)>-1){const n=encodeURIComponent(t[e]);r=`${r}${r.includes("?")?"&":"?"}${e}=${n}`,delete t[e]}return{execParams:t,url:r}}multipartExec(e,t={}){const r=this.config;let o={},a={};const p=t.log&&i.fn(t.log.info)?t.log.info:null,d=t.log&&i.fn(t.log.error)?t.log.error:null;t.formData.getFields().forEach(e=>{o[e.name]=e.value,a[e.name]=e.value}),o=f(o,{deep:!0}),a=l(a),t.formData.getFiles().forEach(e=>{const t=c(e.fieldName);a[t]=u.isValid(e.path)?s(e.path):n.createReadStream(e.path)});const y=h.sign(e,o,r),{url:g}=this.formatUrl(r.gateway,y);return p&&p("[AlipaySdk]start exec url: %s, method: %s, params: %s",g,e,JSON.stringify(o)),new Promise((n,i)=>{s.post({url:g,formData:a,json:!1,timeout:r.timeout,headers:{"user-agent":this.sdkVersion}},(o,a,s)=>{if(o)return o.message="[AlipaySdk]exec error",d&&d(o),i(o);p&&p("[AlipaySdk]exec response: %s",s);try{let o,a;const u=JSON.parse(s);if(a=e.replace(/\./g,"_")+"_response",o=u[a],o){return!t.validateSign||this.checkResponseSign(s,a)?n(r.camelcase?f(o,{deep:!0}):o):i({serverResult:s,errorMessage:"[AlipaySdk]验签失败"})}}catch(e){return i({serverResult:s,errorMessage:"[AlipaySdk]Response 格式错误"})}return i({serverResult:s,errorMessage:"[AlipaySdk]HTTP 请求错误"})})})}pageExec(e,t={}){let r={alipaySdk:this.sdkVersion};const n=this.config,o=t.log&&i.fn(t.log.info)?t.log.info:null;t.formData.getFields().forEach(e=>{r[e.name]=e.value}),r=f(r,{deep:!0});const a=h.sign(e,r,n),{url:s,execParams:u}=this.formatUrl(n.gateway,a);return o&&o("[AlipaySdk]start exec url: %s, method: %s, params: %s",s,e,JSON.stringify(r)),"get"===t.formData.getMethod()?new Promise(e=>{const t=Object.keys(u).map(e=>`${e}=${encodeURIComponent(u[e])}`);e(`${s}&${t.join("&")}`)}):new Promise(e=>{const t="alipaySDKSubmit"+Date.now();e(`\n        <form action="${s}" method="post" name="${t}" id="${t}">\n          ${Object.keys(u).map(e=>`<input type="hidden" name="${e}" value="${String(u[e]).replace(/\"/g,"&quot;")}" />`).join("")}\n        </form>\n        <script>document.forms["${t}"].submit();<\/script>\n      `)})}notifyRSACheck(e,t,r,n){const i=Object.keys(e).sort().filter(e=>e).map(t=>{let r=e[t];return"[object String]"!==Array.prototype.toString.call(r)&&(r=JSON.stringify(r)),n?`${t}=${r}`:`${t}=${decodeURIComponent(r)}`}).join("&");return o.createVerify(h.ALIPAY_ALGORITHM_MAPPING[r]).update(i,"utf8").verify(this.config.alipayPublicKey,t,"base64")}getSignStr(e,t){let r=e.trim();const n=e.indexOf(t+'"'),i=e.lastIndexOf('"sign"');return r=r.substr(n+t.length+1),r=r.substr(0,i),r=r.replace(/^[^{]*{/g,"{"),r=r.replace(/\}([^}]*)$/g,"}"),r}exec(e,t={},r={}){if(r.formData)return r.formData.getFiles().length>0?this.multipartExec(e,r):this.pageExec(e,r);const n=this.config,o=h.sign(e,t,n),{url:a,execParams:s}=this.formatUrl(n.gateway,o),u=r.log&&i.fn(r.log.info)?r.log.info:null,c=r.log&&i.fn(r.log.error)?r.log.error:null;return u&&u("[AlipaySdk]start exec, url: %s, method: %s, params: %s",a,e,JSON.stringify(s)),new Promise((i,o)=>{n.urllib.request(a,{method:"POST",data:s,dataType:"text",timeout:n.timeout,headers:{"user-agent":this.sdkVersion}}).then(a=>{if(u&&u("[AlipaySdk]exec response: %s",a),200===a.status){let s,u;try{const t=JSON.parse(a.data);u=e.replace(/\./g,"_")+"_response",s=t[u]}catch(e){return o({serverResult:a,errorMessage:"[AlipaySdk]Response 格式错误"})}if(s){t.needEncrypt&&(s=h.aesDecrypt(s,n.encryptKey));return!r.validateSign||this.checkResponseSign(a.data,u)?i(n.camelcase?f(s,{deep:!0}):s):o({serverResult:a,errorMessage:"[AlipaySdk]验签失败"})}return o({serverResult:a,errorMessage:"[AlipaySdk]HTTP 请求错误"})}o({serverResult:a,errorMessage:"[AlipaySdk]HTTP 请求错误"})}).catch(e=>{e.message="[AlipaySdk]exec error",c&&c(e),o(e)})})}checkResponseSign(e,t){if(!this.config.alipayPublicKey||""===this.config.alipayPublicKey)return console.warn("config.alipayPublicKey is empty"),!0;if(!e)return!1;const r=this.getSignStr(e,t),n=JSON.parse(e).sign,i=o.createVerify(h.ALIPAY_ALGORITHM_MAPPING[this.config.signType]);return i.update(r,"utf8"),i.verify(this.config.alipayPublicKey,n,"base64")}checkNotifySign(e,t){const r=e.sign;if(!this.config.alipayPublicKey||!r)return!1;const n=e.sign_type||this.config.signType||"RSA2",i=Object.assign({},e);delete i.sign,i.sign_type=n;return!!this.notifyRSACheck(i,r,n,t)||(delete i.sign_type,this.notifyRSACheck(i,r,n,t))}}},function(e,t,r){"use strict";
/**!
 * is
 * the definitive JavaScript type testing library
 *
 * @copyright 2013-2014 Enrico Marino / Jordan Harband
 * @license MIT
 */var n,i,o=Object.prototype,a=o.hasOwnProperty,s=o.toString;"function"==typeof Symbol&&(n=Symbol.prototype.valueOf),"function"==typeof BigInt&&(i=BigInt.prototype.valueOf);var u=function(e){return e!=e},c={boolean:1,number:1,string:1,undefined:1},f=/^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$/,l=/^[A-Fa-f0-9]+$/,h={};h.a=h.type=function(e,t){return typeof e===t},h.defined=function(e){return void 0!==e},h.empty=function(e){var t,r=s.call(e);if("[object Array]"===r||"[object Arguments]"===r||"[object String]"===r)return 0===e.length;if("[object Object]"===r){for(t in e)if(a.call(e,t))return!1;return!0}return!e},h.equal=function(e,t){if(e===t)return!0;var r,n=s.call(e);if(n!==s.call(t))return!1;if("[object Object]"===n){for(r in e)if(!h.equal(e[r],t[r])||!(r in t))return!1;for(r in t)if(!h.equal(e[r],t[r])||!(r in e))return!1;return!0}if("[object Array]"===n){if((r=e.length)!==t.length)return!1;for(;r--;)if(!h.equal(e[r],t[r]))return!1;return!0}return"[object Function]"===n?e.prototype===t.prototype:"[object Date]"===n&&e.getTime()===t.getTime()},h.hosted=function(e,t){var r=typeof t[e];return"object"===r?!!t[e]:!c[r]},h.instance=h.instanceof=function(e,t){return e instanceof t},h.nil=h.null=function(e){return null===e},h.undef=h.undefined=function(e){return void 0===e},h.args=h.arguments=function(e){var t="[object Arguments]"===s.call(e),r=!h.array(e)&&h.arraylike(e)&&h.object(e)&&h.fn(e.callee);return t||r},h.array=Array.isArray||function(e){return"[object Array]"===s.call(e)},h.args.empty=function(e){return h.args(e)&&0===e.length},h.array.empty=function(e){return h.array(e)&&0===e.length},h.arraylike=function(e){return!!e&&!h.bool(e)&&a.call(e,"length")&&isFinite(e.length)&&h.number(e.length)&&e.length>=0},h.bool=h.boolean=function(e){return"[object Boolean]"===s.call(e)},h.false=function(e){return h.bool(e)&&!1===Boolean(Number(e))},h.true=function(e){return h.bool(e)&&!0===Boolean(Number(e))},h.date=function(e){return"[object Date]"===s.call(e)},h.date.valid=function(e){return h.date(e)&&!isNaN(Number(e))},h.element=function(e){return void 0!==e&&"undefined"!=typeof HTMLElement&&e instanceof HTMLElement&&1===e.nodeType},h.error=function(e){return"[object Error]"===s.call(e)},h.fn=h.function=function(e){if("undefined"!=typeof window&&e===window.alert)return!0;var t=s.call(e);return"[object Function]"===t||"[object GeneratorFunction]"===t||"[object AsyncFunction]"===t},h.number=function(e){return"[object Number]"===s.call(e)},h.infinite=function(e){return e===1/0||e===-1/0},h.decimal=function(e){return h.number(e)&&!u(e)&&!h.infinite(e)&&e%1!=0},h.divisibleBy=function(e,t){var r=h.infinite(e),n=h.infinite(t),i=h.number(e)&&!u(e)&&h.number(t)&&!u(t)&&0!==t;return r||n||i&&e%t==0},h.integer=h.int=function(e){return h.number(e)&&!u(e)&&e%1==0},h.maximum=function(e,t){if(u(e))throw new TypeError("NaN is not a valid value");if(!h.arraylike(t))throw new TypeError("second argument must be array-like");for(var r=t.length;--r>=0;)if(e<t[r])return!1;return!0},h.minimum=function(e,t){if(u(e))throw new TypeError("NaN is not a valid value");if(!h.arraylike(t))throw new TypeError("second argument must be array-like");for(var r=t.length;--r>=0;)if(e>t[r])return!1;return!0},h.nan=function(e){return!h.number(e)||e!=e},h.even=function(e){return h.infinite(e)||h.number(e)&&e==e&&e%2==0},h.odd=function(e){return h.infinite(e)||h.number(e)&&e==e&&e%2!=0},h.ge=function(e,t){if(u(e)||u(t))throw new TypeError("NaN is not a valid value");return!h.infinite(e)&&!h.infinite(t)&&e>=t},h.gt=function(e,t){if(u(e)||u(t))throw new TypeError("NaN is not a valid value");return!h.infinite(e)&&!h.infinite(t)&&e>t},h.le=function(e,t){if(u(e)||u(t))throw new TypeError("NaN is not a valid value");return!h.infinite(e)&&!h.infinite(t)&&e<=t},h.lt=function(e,t){if(u(e)||u(t))throw new TypeError("NaN is not a valid value");return!h.infinite(e)&&!h.infinite(t)&&e<t},h.within=function(e,t,r){if(u(e)||u(t)||u(r))throw new TypeError("NaN is not a valid value");if(!h.number(e)||!h.number(t)||!h.number(r))throw new TypeError("all arguments must be numbers");return h.infinite(e)||h.infinite(t)||h.infinite(r)||e>=t&&e<=r},h.object=function(e){return"[object Object]"===s.call(e)},h.primitive=function(e){return!e||!("object"==typeof e||h.object(e)||h.fn(e)||h.array(e))},h.hash=function(e){return h.object(e)&&e.constructor===Object&&!e.nodeType&&!e.setInterval},h.regexp=function(e){return"[object RegExp]"===s.call(e)},h.string=function(e){return"[object String]"===s.call(e)},h.base64=function(e){return h.string(e)&&(!e.length||f.test(e))},h.hex=function(e){return h.string(e)&&(!e.length||l.test(e))},h.symbol=function(e){return"function"==typeof Symbol&&"[object Symbol]"===s.call(e)&&"symbol"==typeof n.call(e)},h.bigint=function(e){return"function"==typeof BigInt&&"[object BigInt]"===s.call(e)&&"bigint"==typeof i.call(e)},e.exports=h},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=uniCloud.httpclient;t.default=n,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={},e.exports=t.default},function(e,t,r){"use strict";var n=r(37);var i={Uri:{createUriRegex:function(e){if("object"!=typeof(e=e||{})||Array.isArray(e))throw new Error("options must be an object");var t="";if(e.scheme){if(Array.isArray(e.scheme)||(e.scheme=[e.scheme]),e.scheme.length<=0)throw new Error("scheme must have at least 1 scheme specified");for(var r=0;r<e.scheme.length;++r){var i=e.scheme[r];if(!(i instanceof RegExp||"string"==typeof i))throw new Error("scheme must only contain Regular Expressions or Strings");if(t+=t?"|":"",i instanceof RegExp)t+=i.source;else{if(!/[a-zA-Z][a-zA-Z0-9+-\.]*/.test(i))throw new Error("scheme at position "+r+" must be a valid scheme");t+=i.replace(/[\^\$\.\*\+\-\?\=\!\:\|\\\/\(\)\[\]\{\}\,]/g,"\\$&")}}}var o="(?:"+(t||n.scheme)+")";return new RegExp("^(?:"+o+":"+n.hierPart+")(?:\\?"+n.query+")?(?:#"+n.fragment+")?$")},uriRegex:new RegExp(n.uri)}};i.Uri.isValid=function(e){return i.Uri.uriRegex.test(e)},e.exports={createUriRegex:i.Uri.createUriRegex,uriRegex:i.Uri.uriRegex,isValid:i.Uri.isValid}},function(e,t,r){"use strict";var n={rfc3986:{},generate:function(){var e="|";n.rfc3986.cidr="[0-9]|[1-2][0-9]|3[0-2]";var t="!\\$&'\\(\\)\\*\\+,;=",r="a-zA-Z0-9-\\._~%0-9A-Fa-f"+t+":@",i="(?:0?0?[0-9]|0?[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])";n.rfc3986.IPv4address="(?:"+i+"\\.){3}"+i;var o="[0-9A-Fa-f]{1,4}",a="(?:"+o+":"+o+"|"+n.rfc3986.IPv4address+")",s="(?:"+o+":){6}"+a,u="::(?:"+o+":){5}"+a,c="(?:"+o+")?::(?:"+o+":){4}"+a,f="(?:(?:"+o+":){0,1}"+o+")?::(?:"+o+":){3}"+a,l="(?:(?:"+o+":){0,2}"+o+")?::(?:"+o+":){2}"+a,h="(?:(?:"+o+":){0,3}"+o+")?::"+o+":"+a,p="(?:(?:"+o+":){0,4}"+o+")?::"+a;n.rfc3986.IPv6address="(?:"+s+e+u+e+c+e+f+e+l+e+h+e+p+"|(?:(?:[0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})?::[0-9A-Fa-f]{1,4}"+"|(?:(?:[0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})?::)",n.rfc3986.IPvFuture="v[0-9A-Fa-f]+\\.[a-zA-Z0-9-\\._~"+t+":]+",n.rfc3986.scheme="[a-zA-Z][a-zA-Z0-9+-\\.]*";n.rfc3986.IPLiteral="\\[(?:"+n.rfc3986.IPv6address+e+n.rfc3986.IPvFuture+")\\]";var d="(?:[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:]*@)?"+("(?:"+n.rfc3986.IPLiteral+e+n.rfc3986.IPv4address+"|[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=]{0,255})")+"(?::[0-9]*)?",y="(?:\\/[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]*)*";n.rfc3986.hierPart="(?:(?:\\/\\/"+d+y+")"+"|\\/(?:[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]+(?:\\/[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]*)*)?"+"|[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]+(?:\\/[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]*)*)",n.rfc3986.query="["+r+"\\/\\?]*(?=#|$)",n.rfc3986.fragment="["+r+"\\/\\?]*",n.rfc3986.uri="^(?:"+n.rfc3986.scheme+":"+n.rfc3986.hierPart+")(?:\\?"+n.rfc3986.query+")?(?:#"+n.rfc3986.fragment+")?$"}};n.generate(),e.exports=n.rfc3986},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(7).camel2snake;t.default=n,e.exports=t.default},function(e,t,r){"use strict";const n=r(14),i=r(40),o=r(41),a=new o({maxSize:1e5}),s=(e,t)=>{const r=(t=Object.assign({deep:!1},t)).exclude;return n(e,(e,t)=>{if(!r||!((e,t)=>e.some(e=>"string"==typeof e?e===t:e.test(t)))(r,e))if(a.has(e))e=a.get(e);else{const t=i(e);e.length<100&&a.set(e,t),e=t}return[e,t]},{deep:t.deep})};e.exports=(e,t)=>Array.isArray(e)?Object.keys(e).map(r=>s(e[r],t)):s(e,t)},function(e,t,r){"use strict";function n(e){let t=!1,r=!1,n=!1;for(let i=0;i<e.length;i++){const o=e[i];t&&/[a-zA-Z]/.test(o)&&o.toUpperCase()===o?(e=e.substr(0,i)+"-"+e.substr(i),t=!1,n=r,r=!0,i++):r&&n&&/[a-zA-Z]/.test(o)&&o.toLowerCase()===o?(e=e.substr(0,i-1)+"-"+e.substr(i-1),n=r,r=!1,t=!0):(t=o.toLowerCase()===o,n=r,r=o.toUpperCase()===o)}return e}e.exports=function(e){if(0===(e=arguments.length>1?Array.from(arguments).map(e=>e.trim()).filter(e=>e.length).join("-"):e.trim()).length)return"";if(1===e.length)return e.toLowerCase();if(/^[a-z0-9]+$/.test(e))return e;const t=e!==e.toLowerCase();return t&&(e=n(e)),e.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(e,t)=>t.toUpperCase())}},function(e,t,r){"use strict";class n{constructor(e){if(!((e=Object.assign({},e)).maxSize&&e.maxSize>0))throw new TypeError("`maxSize` must be a number greater than 0");this.maxSize=e.maxSize,this.cache=new Map,this.oldCache=new Map,this._size=0}_set(e,t){this.cache.set(e,t),this._size++,this._size>=this.maxSize&&(this._size=0,this.oldCache=this.cache,this.cache=new Map)}get(e){if(this.cache.has(e))return this.cache.get(e);if(this.oldCache.has(e)){const t=this.oldCache.get(e);return this._set(e,t),t}}set(e,t){return this.cache.has(e)?this.cache.set(e,t):this._set(e,t),this}has(e){return this.cache.has(e)||this.oldCache.has(e)}peek(e){return this.cache.has(e)?this.cache.get(e):this.oldCache.has(e)?this.oldCache.get(e):void 0}delete(e){this.cache.delete(e)&&this._size--,this.oldCache.delete(e)}clear(){this.cache.clear(),this.oldCache.clear(),this._size=0}*keys(){for(const e of this)yield e[0]}*values(){for(const e of this)yield e[1]}*[Symbol.iterator](){for(const e of this.cache)yield e;for(const e of this.oldCache)this.cache.has(e[0])||(yield e)}get size(){let e=0;for(const t of this.oldCache)this.cache.has(t[0])||e++;return this._size+e}}e.exports=n},function(e,t,r){var n=r(43);e.exports=function(e){return n(e).replace(/\s/g,"_")}},function(e,t,r){var n=r(44);e.exports=function(e){return n(e).replace(/[\W_]+(.|$)/g,(function(e,t){return t?" "+t:""}))}},function(e,t){e.exports=function(e){if(r.test(e))return e.toLowerCase();i.test(e)&&(e=function(e){return e.replace(o,(function(e,t){return t?" "+t:""}))}(e));n.test(e)&&(e=function(e){return e.replace(a,(function(e,t,r){return t+" "+r.toLowerCase().split("").join(" ")}))}(e));return e.toLowerCase()};var r=/\s/,n=/[a-z][A-Z]/,i=/[\W_]/;var o=/[\W_]+(.|$)/g;var a=/(.)([A-Z]+)/g},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return{format:function(){return(0,n.getFullTimeStr)((0,n.getOffsetDate)(8))}}};var n=r(7);e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n={encode:e=>e};t.default=n,e.exports=t.default},function(e,t,r){var n;e.exports=(n=r(0),r(8),r(48),r(49),r(5),r(50),r(6),r(11),r(17),r(51),r(18),r(52),r(53),r(54),r(12),r(55),r(4),r(1),r(56),r(57),r(58),r(59),r(60),r(61),r(62),r(63),r(64),r(65),r(66),r(67),r(68),r(69),r(70),r(71),n)},function(e,t,r){var n;e.exports=(n=r(0),function(){if("function"==typeof ArrayBuffer){var e=n.lib.WordArray,t=e.init;(e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var r=e.byteLength,n=[],i=0;i<r;i++)n[i>>>2]|=e[i]<<24-i%4*8;t.call(this,n,r)}else t.apply(this,arguments)}).prototype=e}}(),n.lib.WordArray)},function(e,t,r){var n;e.exports=(n=r(0),function(){var e=n,t=e.lib.WordArray,r=e.enc;function i(e){return e<<8&4278255360|e>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i+=2){var o=t[i>>>2]>>>16-i%4*8&65535;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var r=e.length,n=[],i=0;i<r;i++)n[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return t.create(n,2*r)}},r.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],o=0;o<r;o+=2){var a=i(t[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(a))}return n.join("")},parse:function(e){for(var r=e.length,n=[],o=0;o<r;o++)n[o>>>1]|=i(e.charCodeAt(o)<<16-o%2*16);return t.create(n,2*r)}}}(),n.enc.Utf16)},function(e,t,r){var n,i,o;e.exports=(o=r(0),i=(n=o).lib.WordArray,n.enc.Base64url={stringify:function(e,t=!0){var r=e.words,n=e.sigBytes,i=t?this._safe_map:this._map;e.clamp();for(var o=[],a=0;a<n;a+=3)for(var s=(r[a>>>2]>>>24-a%4*8&255)<<16|(r[a+1>>>2]>>>24-(a+1)%4*8&255)<<8|r[a+2>>>2]>>>24-(a+2)%4*8&255,u=0;u<4&&a+.75*u<n;u++)o.push(i.charAt(s>>>6*(3-u)&63));var c=i.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(e,t=!0){var r=e.length,n=t?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var a=0;a<n.length;a++)o[n.charCodeAt(a)]=a}var s=n.charAt(64);if(s){var u=e.indexOf(s);-1!==u&&(r=u)}return function(e,t,r){for(var n=[],o=0,a=0;a<t;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,u=r[e.charCodeAt(a)]>>>6-a%4*2,c=s|u;n[o>>>2]|=c<<24-o%4*8,o++}return i.create(n,o)}(e,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},o.enc.Base64url)},function(e,t,r){var n,i,o,a,s,u;e.exports=(u=r(0),r(17),i=(n=u).lib.WordArray,o=n.algo,a=o.SHA256,s=o.SHA224=a.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=4,e}}),n.SHA224=a._createHelper(s),n.HmacSHA224=a._createHmacHelper(s),u.SHA224)},function(e,t,r){var n,i,o,a,s,u,c,f;e.exports=(f=r(0),r(8),r(18),i=(n=f).x64,o=i.Word,a=i.WordArray,s=n.algo,u=s.SHA512,c=s.SHA384=u.extend({_doReset:function(){this._hash=new a.init([new o.init(3418070365,3238371032),new o.init(1654270250,914150663),new o.init(2438529370,812702999),new o.init(355462360,4144912697),new o.init(1731405415,4290775857),new o.init(2394180231,1750603025),new o.init(3675008525,1694076839),new o.init(1203062813,3204075428)])},_doFinalize:function(){var e=u._doFinalize.call(this);return e.sigBytes-=16,e}}),n.SHA384=u._createHelper(c),n.HmacSHA384=u._createHmacHelper(c),f.SHA384)},function(e,t,r){var n;e.exports=(n=r(0),r(8),function(e){var t=n,r=t.lib,i=r.WordArray,o=r.Hasher,a=t.x64.Word,s=t.algo,u=[],c=[],f=[];!function(){for(var e=1,t=0,r=0;r<24;r++){u[e+5*t]=(r+1)*(r+2)/2%64;var n=(2*e+3*t)%5;e=t%5,t=n}for(e=0;e<5;e++)for(t=0;t<5;t++)c[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,o=0;o<24;o++){for(var s=0,l=0,h=0;h<7;h++){if(1&i){var p=(1<<h)-1;p<32?l^=1<<p:s^=1<<p-32}128&i?i=i<<1^113:i<<=1}f[o]=a.create(s,l)}}();var l=[];!function(){for(var e=0;e<25;e++)l[e]=a.create()}();var h=s.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,n=this.blockSize/2,i=0;i<n;i++){var o=e[t+2*i],a=e[t+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(T=r[i]).high^=a,T.low^=o}for(var s=0;s<24;s++){for(var h=0;h<5;h++){for(var p=0,d=0,y=0;y<5;y++)p^=(T=r[h+5*y]).high,d^=T.low;var g=l[h];g.high=p,g.low=d}for(h=0;h<5;h++){var v=l[(h+4)%5],_=l[(h+1)%5],m=_.high,b=_.low;for(p=v.high^(m<<1|b>>>31),d=v.low^(b<<1|m>>>31),y=0;y<5;y++)(T=r[h+5*y]).high^=p,T.low^=d}for(var w=1;w<25;w++){var S=(T=r[w]).high,E=T.low,A=u[w];A<32?(p=S<<A|E>>>32-A,d=E<<A|S>>>32-A):(p=E<<A-32|S>>>64-A,d=S<<A-32|E>>>64-A);var I=l[c[w]];I.high=p,I.low=d}var N=l[0],C=r[0];for(N.high=C.high,N.low=C.low,h=0;h<5;h++)for(y=0;y<5;y++){var T=r[w=h+5*y],R=l[w],x=l[(h+1)%5+5*y],P=l[(h+2)%5+5*y];T.high=R.high^~x.high&P.high,T.low=R.low^~x.low&P.low}T=r[0];var O=f[s];T.high^=O.high,T.low^=O.low}},_doFinalize:function(){var t=this._data,r=t.words,n=(this._nDataBytes,8*t.sigBytes),o=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((n+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,u=s/8,c=[],f=0;f<u;f++){var l=a[f],h=l.high,p=l.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),p=16711935&(p<<8|p>>>24)|4278255360&(p<<24|p>>>8),c.push(p),c.push(h)}return new i.init(c,s)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});t.SHA3=o._createHelper(h),t.HmacSHA3=o._createHmacHelper(h)}(Math),n.SHA3)},function(e,t,r){var n;e.exports=(n=r(0),
/** @preserve
	(c) 2012 by Cédric Mesnil. All rights reserved.

	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
	    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	*/
function(e){var t=n,r=t.lib,i=r.WordArray,o=r.Hasher,a=t.algo,s=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),u=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),f=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),l=i.create([0,1518500249,1859775393,2400959708,2840853838]),h=i.create([1352829926,1548603684,1836072691,2053994217,0]),p=a.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,a,p,b,w,S,E,A,I,N,C,T=this._hash.words,R=l.words,x=h.words,P=s.words,O=u.words,B=c.words,k=f.words;for(S=o=T[0],E=a=T[1],A=p=T[2],I=b=T[3],N=w=T[4],r=0;r<80;r+=1)C=o+e[t+P[r]]|0,C+=r<16?d(a,p,b)+R[0]:r<32?y(a,p,b)+R[1]:r<48?g(a,p,b)+R[2]:r<64?v(a,p,b)+R[3]:_(a,p,b)+R[4],C=(C=m(C|=0,B[r]))+w|0,o=w,w=b,b=m(p,10),p=a,a=C,C=S+e[t+O[r]]|0,C+=r<16?_(E,A,I)+x[0]:r<32?v(E,A,I)+x[1]:r<48?g(E,A,I)+x[2]:r<64?y(E,A,I)+x[3]:d(E,A,I)+x[4],C=(C=m(C|=0,k[r]))+N|0,S=N,N=I,I=m(A,10),A=E,E=C;C=T[1]+p+I|0,T[1]=T[2]+b+N|0,T[2]=T[3]+w+S|0,T[3]=T[4]+o+E|0,T[4]=T[0]+a+A|0,T[0]=C},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,o=i.words,a=0;a<5;a++){var s=o[a];o[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return i},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function d(e,t,r){return e^t^r}function y(e,t,r){return e&t|~e&r}function g(e,t,r){return(e|~t)^r}function v(e,t,r){return e&r|t&~r}function _(e,t,r){return e^(t|~r)}function m(e,t){return e<<t|e>>>32-t}t.RIPEMD160=o._createHelper(p),t.HmacRIPEMD160=o._createHmacHelper(p)}(Math),n.RIPEMD160)},function(e,t,r){var n,i,o,a,s,u,c,f,l;e.exports=(l=r(0),r(11),r(12),i=(n=l).lib,o=i.Base,a=i.WordArray,s=n.algo,u=s.SHA1,c=s.HMAC,f=s.PBKDF2=o.extend({cfg:o.extend({keySize:4,hasher:u,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,n=c.create(r.hasher,e),i=a.create(),o=a.create([1]),s=i.words,u=o.words,f=r.keySize,l=r.iterations;s.length<f;){var h=n.update(t).finalize(o);n.reset();for(var p=h.words,d=p.length,y=h,g=1;g<l;g++){y=n.finalize(y),n.reset();for(var v=y.words,_=0;_<d;_++)p[_]^=v[_]}i.concat(h),u[0]++}return i.sigBytes=4*f,i}}),n.PBKDF2=function(e,t,r){return f.create(r).compute(e,t)},l.PBKDF2)},function(e,t,r){var n;e.exports=(n=r(0),r(1),n.mode.CFB=function(){var e=n.lib.BlockCipherMode.extend();function t(e,t,r,n){var i,o=this._iv;o?(i=o.slice(0),this._iv=void 0):i=this._prevBlock,n.encryptBlock(i,0);for(var a=0;a<r;a++)e[t+a]^=i[a]}return e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize;t.call(this,e,r,i,n),this._prevBlock=e.slice(r,r+i)}}),e.Decryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,o=e.slice(r,r+i);t.call(this,e,r,i,n),this._prevBlock=o}}),e}(),n.mode.CFB)},function(e,t,r){var n,i,o;e.exports=(o=r(0),r(1),o.mode.CTR=(n=o.lib.BlockCipherMode.extend(),i=n.Encryptor=n.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var a=o.slice(0);r.encryptBlock(a,0),o[n-1]=o[n-1]+1|0;for(var s=0;s<n;s++)e[t+s]^=a[s]}}),n.Decryptor=i,n),o.mode.CTR)},function(e,t,r){var n;e.exports=(n=r(0),r(1),
/** @preserve
	 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
	 * derived from CryptoJS.mode.CTR
	 * <NAME_EMAIL>
	 */
n.mode.CTRGladman=function(){var e=n.lib.BlockCipherMode.extend();function t(e){if(255==(e>>24&255)){var t=e>>16&255,r=e>>8&255,n=255&e;255===t?(t=0,255===r?(r=0,255===n?n=0:++n):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=n}else e+=1<<24;return e}var r=e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,o=this._iv,a=this._counter;o&&(a=this._counter=o.slice(0),this._iv=void 0),function(e){0===(e[0]=t(e[0]))&&(e[1]=t(e[1]))}(a);var s=a.slice(0);n.encryptBlock(s,0);for(var u=0;u<i;u++)e[r+u]^=s[u]}});return e.Decryptor=r,e}(),n.mode.CTRGladman)},function(e,t,r){var n,i,o;e.exports=(o=r(0),r(1),o.mode.OFB=(n=o.lib.BlockCipherMode.extend(),i=n.Encryptor=n.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var a=0;a<n;a++)e[t+a]^=o[a]}}),n.Decryptor=i,n),o.mode.OFB)},function(e,t,r){var n,i;e.exports=(i=r(0),r(1),i.mode.ECB=((n=i.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),n.Decryptor=n.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),n),i.mode.ECB)},function(e,t,r){var n;e.exports=(n=r(0),r(1),n.pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,n=4*t,i=n-r%n,o=r+i-1;e.clamp(),e.words[o>>>2]|=i<<24-o%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},n.pad.Ansix923)},function(e,t,r){var n;e.exports=(n=r(0),r(1),n.pad.Iso10126={pad:function(e,t){var r=4*t,i=r-e.sigBytes%r;e.concat(n.lib.WordArray.random(i-1)).concat(n.lib.WordArray.create([i<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},n.pad.Iso10126)},function(e,t,r){var n;e.exports=(n=r(0),r(1),n.pad.Iso97971={pad:function(e,t){e.concat(n.lib.WordArray.create([2147483648],1)),n.pad.ZeroPadding.pad(e,t)},unpad:function(e){n.pad.ZeroPadding.unpad(e),e.sigBytes--}},n.pad.Iso97971)},function(e,t,r){var n;e.exports=(n=r(0),r(1),n.pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){var t=e.words,r=e.sigBytes-1;for(r=e.sigBytes-1;r>=0;r--)if(t[r>>>2]>>>24-r%4*8&255){e.sigBytes=r+1;break}}},n.pad.ZeroPadding)},function(e,t,r){var n;e.exports=(n=r(0),r(1),n.pad.NoPadding={pad:function(){},unpad:function(){}},n.pad.NoPadding)},function(e,t,r){var n,i,o,a;e.exports=(a=r(0),r(1),i=(n=a).lib.CipherParams,o=n.enc.Hex,n.format.Hex={stringify:function(e){return e.ciphertext.toString(o)},parse:function(e){var t=o.parse(e);return i.create({ciphertext:t})}},a.format.Hex)},function(e,t,r){var n;e.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var e=n,t=e.lib.BlockCipher,r=e.algo,i=[],o=[],a=[],s=[],u=[],c=[],f=[],l=[],h=[],p=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,n=0;for(t=0;t<256;t++){var d=n^n<<1^n<<2^n<<3^n<<4;d=d>>>8^255&d^99,i[r]=d,o[d]=r;var y=e[r],g=e[y],v=e[g],_=257*e[d]^16843008*d;a[r]=_<<24|_>>>8,s[r]=_<<16|_>>>16,u[r]=_<<8|_>>>24,c[r]=_,_=16843009*v^65537*g^257*y^16843008*r,f[d]=_<<24|_>>>8,l[d]=_<<16|_>>>16,h[d]=_<<8|_>>>24,p[d]=_,r?(r=y^e[e[e[v^y]]],n^=e[e[n]]):r=n=1}}();var d=[0,1,2,4,8,16,32,64,128,27,54],y=r.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,n=4*((this._nRounds=r+6)+1),o=this._keySchedule=[],a=0;a<n;a++)a<r?o[a]=t[a]:(c=o[a-1],a%r?r>6&&a%r==4&&(c=i[c>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c]):(c=i[(c=c<<8|c>>>24)>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c],c^=d[a/r|0]<<24),o[a]=o[a-r]^c);for(var s=this._invKeySchedule=[],u=0;u<n;u++){if(a=n-u,u%4)var c=o[a];else c=o[a-4];s[u]=u<4||a<=4?c:f[i[c>>>24]]^l[i[c>>>16&255]]^h[i[c>>>8&255]]^p[i[255&c]]}}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,u,c,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,f,l,h,p,o),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,n,i,o,a,s){for(var u=this._nRounds,c=e[t]^r[0],f=e[t+1]^r[1],l=e[t+2]^r[2],h=e[t+3]^r[3],p=4,d=1;d<u;d++){var y=n[c>>>24]^i[f>>>16&255]^o[l>>>8&255]^a[255&h]^r[p++],g=n[f>>>24]^i[l>>>16&255]^o[h>>>8&255]^a[255&c]^r[p++],v=n[l>>>24]^i[h>>>16&255]^o[c>>>8&255]^a[255&f]^r[p++],_=n[h>>>24]^i[c>>>16&255]^o[f>>>8&255]^a[255&l]^r[p++];c=y,f=g,l=v,h=_}y=(s[c>>>24]<<24|s[f>>>16&255]<<16|s[l>>>8&255]<<8|s[255&h])^r[p++],g=(s[f>>>24]<<24|s[l>>>16&255]<<16|s[h>>>8&255]<<8|s[255&c])^r[p++],v=(s[l>>>24]<<24|s[h>>>16&255]<<16|s[c>>>8&255]<<8|s[255&f])^r[p++],_=(s[h>>>24]<<24|s[c>>>16&255]<<16|s[f>>>8&255]<<8|s[255&l])^r[p++],e[t]=y,e[t+1]=g,e[t+2]=v,e[t+3]=_},keySize:8});e.AES=t._createHelper(y)}(),n.AES)},function(e,t,r){var n;e.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var e=n,t=e.lib,r=t.WordArray,i=t.BlockCipher,o=e.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],u=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],f=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],l=o.DES=i.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var n=a[r]-1;t[r]=e[n>>>5]>>>31-n%32&1}for(var i=this._subKeys=[],o=0;o<16;o++){var c=i[o]=[],f=u[o];for(r=0;r<24;r++)c[r/6|0]|=t[(s[r]-1+f)%28]<<31-r%6,c[4+(r/6|0)]|=t[28+(s[r+24]-1+f)%28]<<31-r%6;for(c[0]=c[0]<<1|c[0]>>>31,r=1;r<7;r++)c[r]=c[r]>>>4*(r-1)+3;c[7]=c[7]<<5|c[7]>>>27}var l=this._invSubKeys=[];for(r=0;r<16;r++)l[r]=i[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],h.call(this,4,252645135),h.call(this,16,65535),p.call(this,2,858993459),p.call(this,8,16711935),h.call(this,1,1431655765);for(var n=0;n<16;n++){for(var i=r[n],o=this._lBlock,a=this._rBlock,s=0,u=0;u<8;u++)s|=c[u][((a^i[u])&f[u])>>>0];this._lBlock=a,this._rBlock=o^s}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,h.call(this,1,1431655765),p.call(this,8,16711935),p.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function p(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}e.DES=i._createHelper(l);var d=o.TripleDES=i.extend({_doReset:function(){var e=this._key.words;if(2!==e.length&&4!==e.length&&e.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var t=e.slice(0,2),n=e.length<4?e.slice(0,2):e.slice(2,4),i=e.length<6?e.slice(0,2):e.slice(4,6);this._des1=l.createEncryptor(r.create(t)),this._des2=l.createEncryptor(r.create(n)),this._des3=l.createEncryptor(r.create(i))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=i._createHelper(d)}(),n.TripleDES)},function(e,t,r){var n;e.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var e=n,t=e.lib.StreamCipher,r=e.algo,i=r.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,n=this._S=[],i=0;i<256;i++)n[i]=i;i=0;for(var o=0;i<256;i++){var a=i%r,s=t[a>>>2]>>>24-a%4*8&255;o=(o+n[i]+s)%256;var u=n[i];n[i]=n[o],n[o]=u}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,r=this._j,n=0,i=0;i<4;i++){r=(r+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[r],e[r]=o,n|=e[(e[t]+e[r])%256]<<24-8*i}return this._i=t,this._j=r,n}e.RC4=t._createHelper(i);var a=r.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});e.RC4Drop=t._createHelper(a)}(),n.RC4)},function(e,t,r){var n;e.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var e=n,t=e.lib.StreamCipher,r=e.algo,i=[],o=[],a=[],s=r.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,**********&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,**********&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,**********&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,**********&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)u.call(this);for(r=0;r<8;r++)i[r]^=n[r+4&7];if(t){var o=t.words,a=o[0],s=o[1],c=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=c>>>16|**********&f,h=f<<16|65535&c;for(i[0]^=c,i[1]^=l,i[2]^=f,i[3]^=h,i[4]^=c,i[5]^=l,i[6]^=f,i[7]^=h,r=0;r<4;r++)u.call(this)}},_doProcessBlock:function(e,t){var r=this._X;u.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function u(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,s=n>>>16,u=((i*i>>>17)+i*s>>>15)+s*s,c=((**********&n)*n|0)+((65535&n)*n|0);a[r]=u^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=t._createHelper(s)}(),n.Rabbit)},function(e,t,r){var n;e.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var e=n,t=e.lib.StreamCipher,r=e.algo,i=[],o=[],a=[],s=r.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,**********&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,**********&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,**********&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,**********&e[3]|65535&e[0]];this._b=0;for(var i=0;i<4;i++)u.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(t){var o=t.words,a=o[0],s=o[1],c=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),f=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),l=c>>>16|**********&f,h=f<<16|65535&c;for(n[0]^=c,n[1]^=l,n[2]^=f,n[3]^=h,n[4]^=c,n[5]^=l,n[6]^=f,n[7]^=h,i=0;i<4;i++)u.call(this)}},_doProcessBlock:function(e,t){var r=this._X;u.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function u(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,s=n>>>16,u=((i*i>>>17)+i*s>>>15)+s*s,c=((**********&n)*n|0)+((65535&n)*n|0);a[r]=u^c}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=t._createHelper(s)}(),n.RabbitLegacy)},function(e,t,r){(function(e){var n;
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */(function(){var i="Expected a function",o="__lodash_placeholder__",a=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]],s="[object Arguments]",u="[object Array]",c="[object Boolean]",f="[object Date]",l="[object Error]",h="[object Function]",p="[object GeneratorFunction]",d="[object Map]",y="[object Number]",g="[object Object]",v="[object RegExp]",_="[object Set]",m="[object String]",b="[object Symbol]",w="[object WeakMap]",S="[object ArrayBuffer]",E="[object DataView]",A="[object Float32Array]",I="[object Float64Array]",N="[object Int8Array]",C="[object Int16Array]",T="[object Int32Array]",R="[object Uint8Array]",x="[object Uint16Array]",P="[object Uint32Array]",O=/\b__p \+= '';/g,B=/\b(__p \+=) '' \+/g,k=/(__e\(.*?\)|\b__t\)) \+\n'';/g,D=/&(?:amp|lt|gt|quot|#39);/g,U=/[&<>"']/g,M=RegExp(D.source),j=RegExp(U.source),L=/<%-([\s\S]+?)%>/g,F=/<%([\s\S]+?)%>/g,K=/<%=([\s\S]+?)%>/g,z=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,V=/^\w*$/,$=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,q=/[\\^$.*+?()[\]{}|]/g,H=RegExp(q.source),W=/^\s+/,G=/\s/,J=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Y=/\{\n\/\* \[wrapped with (.+)\] \*/,Z=/,? & /,X=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Q=/[()=,{}\[\]\/\s]/,ee=/\\(\\)?/g,te=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,re=/\w*$/,ne=/^[-+]0x[0-9a-f]+$/i,ie=/^0b[01]+$/i,oe=/^\[object .+?Constructor\]$/,ae=/^0o[0-7]+$/i,se=/^(?:0|[1-9]\d*)$/,ue=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ce=/($^)/,fe=/['\n\r\u2028\u2029\\]/g,le="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",he="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",pe="[\\ud800-\\udfff]",de="["+he+"]",ye="["+le+"]",ge="\\d+",ve="[\\u2700-\\u27bf]",_e="[a-z\\xdf-\\xf6\\xf8-\\xff]",me="[^\\ud800-\\udfff"+he+ge+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",be="\\ud83c[\\udffb-\\udfff]",we="[^\\ud800-\\udfff]",Se="(?:\\ud83c[\\udde6-\\uddff]){2}",Ee="[\\ud800-\\udbff][\\udc00-\\udfff]",Ae="[A-Z\\xc0-\\xd6\\xd8-\\xde]",Ie="(?:"+_e+"|"+me+")",Ne="(?:"+Ae+"|"+me+")",Ce="(?:"+ye+"|"+be+")"+"?",Te="[\\ufe0e\\ufe0f]?"+Ce+("(?:\\u200d(?:"+[we,Se,Ee].join("|")+")[\\ufe0e\\ufe0f]?"+Ce+")*"),Re="(?:"+[ve,Se,Ee].join("|")+")"+Te,xe="(?:"+[we+ye+"?",ye,Se,Ee,pe].join("|")+")",Pe=RegExp("['’]","g"),Oe=RegExp(ye,"g"),Be=RegExp(be+"(?="+be+")|"+xe+Te,"g"),ke=RegExp([Ae+"?"+_e+"+(?:['’](?:d|ll|m|re|s|t|ve))?(?="+[de,Ae,"$"].join("|")+")",Ne+"+(?:['’](?:D|LL|M|RE|S|T|VE))?(?="+[de,Ae+Ie,"$"].join("|")+")",Ae+"?"+Ie+"+(?:['’](?:d|ll|m|re|s|t|ve))?",Ae+"+(?:['’](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",ge,Re].join("|"),"g"),De=RegExp("[\\u200d\\ud800-\\udfff"+le+"\\ufe0e\\ufe0f]"),Ue=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Me=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],je=-1,Le={};Le[A]=Le[I]=Le[N]=Le[C]=Le[T]=Le[R]=Le["[object Uint8ClampedArray]"]=Le[x]=Le[P]=!0,Le[s]=Le[u]=Le[S]=Le[c]=Le[E]=Le[f]=Le[l]=Le[h]=Le[d]=Le[y]=Le[g]=Le[v]=Le[_]=Le[m]=Le[w]=!1;var Fe={};Fe[s]=Fe[u]=Fe[S]=Fe[E]=Fe[c]=Fe[f]=Fe[A]=Fe[I]=Fe[N]=Fe[C]=Fe[T]=Fe[d]=Fe[y]=Fe[g]=Fe[v]=Fe[_]=Fe[m]=Fe[b]=Fe[R]=Fe["[object Uint8ClampedArray]"]=Fe[x]=Fe[P]=!0,Fe[l]=Fe[h]=Fe[w]=!1;var Ke={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},ze=parseFloat,Ve=parseInt,$e="object"==typeof global&&global&&global.Object===Object&&global,qe="object"==typeof self&&self&&self.Object===Object&&self,He=$e||qe||Function("return this")(),We=t&&!t.nodeType&&t,Ge=We&&"object"==typeof e&&e&&!e.nodeType&&e,Je=Ge&&Ge.exports===We,Ye=Je&&$e.process,Ze=function(){try{var e=Ge&&Ge.require&&Ge.require("util").types;return e||Ye&&Ye.binding&&Ye.binding("util")}catch(e){}}(),Xe=Ze&&Ze.isArrayBuffer,Qe=Ze&&Ze.isDate,et=Ze&&Ze.isMap,tt=Ze&&Ze.isRegExp,rt=Ze&&Ze.isSet,nt=Ze&&Ze.isTypedArray;function it(e,t,r){switch(r.length){case 0:return e.call(t);case 1:return e.call(t,r[0]);case 2:return e.call(t,r[0],r[1]);case 3:return e.call(t,r[0],r[1],r[2])}return e.apply(t,r)}function ot(e,t,r,n){for(var i=-1,o=null==e?0:e.length;++i<o;){var a=e[i];t(n,a,r(a),e)}return n}function at(e,t){for(var r=-1,n=null==e?0:e.length;++r<n&&!1!==t(e[r],r,e););return e}function st(e,t){for(var r=null==e?0:e.length;r--&&!1!==t(e[r],r,e););return e}function ut(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(!t(e[r],r,e))return!1;return!0}function ct(e,t){for(var r=-1,n=null==e?0:e.length,i=0,o=[];++r<n;){var a=e[r];t(a,r,e)&&(o[i++]=a)}return o}function ft(e,t){return!!(null==e?0:e.length)&&bt(e,t,0)>-1}function lt(e,t,r){for(var n=-1,i=null==e?0:e.length;++n<i;)if(r(t,e[n]))return!0;return!1}function ht(e,t){for(var r=-1,n=null==e?0:e.length,i=Array(n);++r<n;)i[r]=t(e[r],r,e);return i}function pt(e,t){for(var r=-1,n=t.length,i=e.length;++r<n;)e[i+r]=t[r];return e}function dt(e,t,r,n){var i=-1,o=null==e?0:e.length;for(n&&o&&(r=e[++i]);++i<o;)r=t(r,e[i],i,e);return r}function yt(e,t,r,n){var i=null==e?0:e.length;for(n&&i&&(r=e[--i]);i--;)r=t(r,e[i],i,e);return r}function gt(e,t){for(var r=-1,n=null==e?0:e.length;++r<n;)if(t(e[r],r,e))return!0;return!1}var vt=At("length");function _t(e,t,r){var n;return r(e,(function(e,r,i){if(t(e,r,i))return n=r,!1})),n}function mt(e,t,r,n){for(var i=e.length,o=r+(n?1:-1);n?o--:++o<i;)if(t(e[o],o,e))return o;return-1}function bt(e,t,r){return t==t?function(e,t,r){var n=r-1,i=e.length;for(;++n<i;)if(e[n]===t)return n;return-1}(e,t,r):mt(e,St,r)}function wt(e,t,r,n){for(var i=r-1,o=e.length;++i<o;)if(n(e[i],t))return i;return-1}function St(e){return e!=e}function Et(e,t){var r=null==e?0:e.length;return r?Ct(e,t)/r:NaN}function At(e){return function(t){return null==t?void 0:t[e]}}function It(e){return function(t){return null==e?void 0:e[t]}}function Nt(e,t,r,n,i){return i(e,(function(e,i,o){r=n?(n=!1,e):t(r,e,i,o)})),r}function Ct(e,t){for(var r,n=-1,i=e.length;++n<i;){var o=t(e[n]);void 0!==o&&(r=void 0===r?o:r+o)}return r}function Tt(e,t){for(var r=-1,n=Array(e);++r<e;)n[r]=t(r);return n}function Rt(e){return e?e.slice(0,Wt(e)+1).replace(W,""):e}function xt(e){return function(t){return e(t)}}function Pt(e,t){return ht(t,(function(t){return e[t]}))}function Ot(e,t){return e.has(t)}function Bt(e,t){for(var r=-1,n=e.length;++r<n&&bt(t,e[r],0)>-1;);return r}function kt(e,t){for(var r=e.length;r--&&bt(t,e[r],0)>-1;);return r}function Dt(e,t){for(var r=e.length,n=0;r--;)e[r]===t&&++n;return n}var Ut=It({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),Mt=It({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function jt(e){return"\\"+Ke[e]}function Lt(e){return De.test(e)}function Ft(e){var t=-1,r=Array(e.size);return e.forEach((function(e,n){r[++t]=[n,e]})),r}function Kt(e,t){return function(r){return e(t(r))}}function zt(e,t){for(var r=-1,n=e.length,i=0,a=[];++r<n;){var s=e[r];s!==t&&s!==o||(e[r]=o,a[i++]=r)}return a}function Vt(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=e})),r}function $t(e){var t=-1,r=Array(e.size);return e.forEach((function(e){r[++t]=[e,e]})),r}function qt(e){return Lt(e)?function(e){var t=Be.lastIndex=0;for(;Be.test(e);)++t;return t}(e):vt(e)}function Ht(e){return Lt(e)?function(e){return e.match(Be)||[]}(e):function(e){return e.split("")}(e)}function Wt(e){for(var t=e.length;t--&&G.test(e.charAt(t)););return t}var Gt=It({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var Jt=function e(t){var r,n=(t=null==t?He:Jt.defaults(He.Object(),t,Jt.pick(He,Me))).Array,G=t.Date,le=t.Error,he=t.Function,pe=t.Math,de=t.Object,ye=t.RegExp,ge=t.String,ve=t.TypeError,_e=n.prototype,me=he.prototype,be=de.prototype,we=t["__core-js_shared__"],Se=me.toString,Ee=be.hasOwnProperty,Ae=0,Ie=(r=/[^.]+$/.exec(we&&we.keys&&we.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"",Ne=be.toString,Ce=Se.call(de),Te=He._,Re=ye("^"+Se.call(Ee).replace(q,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),xe=Je?t.Buffer:void 0,Be=t.Symbol,De=t.Uint8Array,Ke=xe?xe.allocUnsafe:void 0,$e=Kt(de.getPrototypeOf,de),qe=de.create,We=be.propertyIsEnumerable,Ge=_e.splice,Ye=Be?Be.isConcatSpreadable:void 0,Ze=Be?Be.iterator:void 0,vt=Be?Be.toStringTag:void 0,It=function(){try{var e=eo(de,"defineProperty");return e({},"",{}),e}catch(e){}}(),Yt=t.clearTimeout!==He.clearTimeout&&t.clearTimeout,Zt=G&&G.now!==He.Date.now&&G.now,Xt=t.setTimeout!==He.setTimeout&&t.setTimeout,Qt=pe.ceil,er=pe.floor,tr=de.getOwnPropertySymbols,rr=xe?xe.isBuffer:void 0,nr=t.isFinite,ir=_e.join,or=Kt(de.keys,de),ar=pe.max,sr=pe.min,ur=G.now,cr=t.parseInt,fr=pe.random,lr=_e.reverse,hr=eo(t,"DataView"),pr=eo(t,"Map"),dr=eo(t,"Promise"),yr=eo(t,"Set"),gr=eo(t,"WeakMap"),vr=eo(de,"create"),_r=gr&&new gr,mr={},br=To(hr),wr=To(pr),Sr=To(dr),Er=To(yr),Ar=To(gr),Ir=Be?Be.prototype:void 0,Nr=Ir?Ir.valueOf:void 0,Cr=Ir?Ir.toString:void 0;function Tr(e){if(qa(e)&&!ka(e)&&!(e instanceof Or)){if(e instanceof Pr)return e;if(Ee.call(e,"__wrapped__"))return Ro(e)}return new Pr(e)}var Rr=function(){function e(){}return function(t){if(!$a(t))return{};if(qe)return qe(t);e.prototype=t;var r=new e;return e.prototype=void 0,r}}();function xr(){}function Pr(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=void 0}function Or(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=4294967295,this.__views__=[]}function Br(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function kr(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Dr(e){var t=-1,r=null==e?0:e.length;for(this.clear();++t<r;){var n=e[t];this.set(n[0],n[1])}}function Ur(e){var t=-1,r=null==e?0:e.length;for(this.__data__=new Dr;++t<r;)this.add(e[t])}function Mr(e){var t=this.__data__=new kr(e);this.size=t.size}function jr(e,t){var r=ka(e),n=!r&&Ba(e),i=!r&&!n&&ja(e),o=!r&&!n&&!i&&Qa(e),a=r||n||i||o,s=a?Tt(e.length,ge):[],u=s.length;for(var c in e)!t&&!Ee.call(e,c)||a&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||so(c,u))||s.push(c);return s}function Lr(e){var t=e.length;return t?e[Mn(0,t-1)]:void 0}function Fr(e,t){return Io(_i(e),Jr(t,0,e.length))}function Kr(e){return Io(_i(e))}function zr(e,t,r){(void 0!==r&&!xa(e[t],r)||void 0===r&&!(t in e))&&Wr(e,t,r)}function Vr(e,t,r){var n=e[t];Ee.call(e,t)&&xa(n,r)&&(void 0!==r||t in e)||Wr(e,t,r)}function $r(e,t){for(var r=e.length;r--;)if(xa(e[r][0],t))return r;return-1}function qr(e,t,r,n){return en(e,(function(e,i,o){t(n,e,r(e),o)})),n}function Hr(e,t){return e&&mi(t,ws(t),e)}function Wr(e,t,r){"__proto__"==t&&It?It(e,t,{configurable:!0,enumerable:!0,value:r,writable:!0}):e[t]=r}function Gr(e,t){for(var r=-1,i=t.length,o=n(i),a=null==e;++r<i;)o[r]=a?void 0:gs(e,t[r]);return o}function Jr(e,t,r){return e==e&&(void 0!==r&&(e=e<=r?e:r),void 0!==t&&(e=e>=t?e:t)),e}function Yr(e,t,r,n,i,o){var a,u=1&t,l=2&t,w=4&t;if(r&&(a=i?r(e,n,i,o):r(e)),void 0!==a)return a;if(!$a(e))return e;var O=ka(e);if(O){if(a=function(e){var t=e.length,r=new e.constructor(t);t&&"string"==typeof e[0]&&Ee.call(e,"index")&&(r.index=e.index,r.input=e.input);return r}(e),!u)return _i(e,a)}else{var B=no(e),k=B==h||B==p;if(ja(e))return hi(e,u);if(B==g||B==s||k&&!i){if(a=l||k?{}:oo(e),!u)return l?function(e,t){return mi(e,ro(e),t)}(e,function(e,t){return e&&mi(t,Ss(t),e)}(a,e)):function(e,t){return mi(e,to(e),t)}(e,Hr(a,e))}else{if(!Fe[B])return i?e:{};a=function(e,t,r){var n=e.constructor;switch(t){case S:return pi(e);case c:case f:return new n(+e);case E:return function(e,t){var r=t?pi(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.byteLength)}(e,r);case A:case I:case N:case C:case T:case R:case"[object Uint8ClampedArray]":case x:case P:return di(e,r);case d:return new n;case y:case m:return new n(e);case v:return function(e){var t=new e.constructor(e.source,re.exec(e));return t.lastIndex=e.lastIndex,t}(e);case _:return new n;case b:return i=e,Nr?de(Nr.call(i)):{}}var i}(e,B,u)}}o||(o=new Mr);var D=o.get(e);if(D)return D;o.set(e,a),Ya(e)?e.forEach((function(n){a.add(Yr(n,t,r,n,e,o))})):Ha(e)&&e.forEach((function(n,i){a.set(i,Yr(n,t,r,i,e,o))}));var U=O?void 0:(w?l?Wi:Hi:l?Ss:ws)(e);return at(U||e,(function(n,i){U&&(n=e[i=n]),Vr(a,i,Yr(n,t,r,i,e,o))})),a}function Zr(e,t,r){var n=r.length;if(null==e)return!n;for(e=de(e);n--;){var i=r[n],o=t[i],a=e[i];if(void 0===a&&!(i in e)||!o(a))return!1}return!0}function Xr(e,t,r){if("function"!=typeof e)throw new ve(i);return wo((function(){e.apply(void 0,r)}),t)}function Qr(e,t,r,n){var i=-1,o=ft,a=!0,s=e.length,u=[],c=t.length;if(!s)return u;r&&(t=ht(t,xt(r))),n?(o=lt,a=!1):t.length>=200&&(o=Ot,a=!1,t=new Ur(t));e:for(;++i<s;){var f=e[i],l=null==r?f:r(f);if(f=n||0!==f?f:0,a&&l==l){for(var h=c;h--;)if(t[h]===l)continue e;u.push(f)}else o(t,l,n)||u.push(f)}return u}Tr.templateSettings={escape:L,evaluate:F,interpolate:K,variable:"",imports:{_:Tr}},Tr.prototype=xr.prototype,Tr.prototype.constructor=Tr,Pr.prototype=Rr(xr.prototype),Pr.prototype.constructor=Pr,Or.prototype=Rr(xr.prototype),Or.prototype.constructor=Or,Br.prototype.clear=function(){this.__data__=vr?vr(null):{},this.size=0},Br.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},Br.prototype.get=function(e){var t=this.__data__;if(vr){var r=t[e];return"__lodash_hash_undefined__"===r?void 0:r}return Ee.call(t,e)?t[e]:void 0},Br.prototype.has=function(e){var t=this.__data__;return vr?void 0!==t[e]:Ee.call(t,e)},Br.prototype.set=function(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=vr&&void 0===t?"__lodash_hash_undefined__":t,this},kr.prototype.clear=function(){this.__data__=[],this.size=0},kr.prototype.delete=function(e){var t=this.__data__,r=$r(t,e);return!(r<0)&&(r==t.length-1?t.pop():Ge.call(t,r,1),--this.size,!0)},kr.prototype.get=function(e){var t=this.__data__,r=$r(t,e);return r<0?void 0:t[r][1]},kr.prototype.has=function(e){return $r(this.__data__,e)>-1},kr.prototype.set=function(e,t){var r=this.__data__,n=$r(r,e);return n<0?(++this.size,r.push([e,t])):r[n][1]=t,this},Dr.prototype.clear=function(){this.size=0,this.__data__={hash:new Br,map:new(pr||kr),string:new Br}},Dr.prototype.delete=function(e){var t=Xi(this,e).delete(e);return this.size-=t?1:0,t},Dr.prototype.get=function(e){return Xi(this,e).get(e)},Dr.prototype.has=function(e){return Xi(this,e).has(e)},Dr.prototype.set=function(e,t){var r=Xi(this,e),n=r.size;return r.set(e,t),this.size+=r.size==n?0:1,this},Ur.prototype.add=Ur.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},Ur.prototype.has=function(e){return this.__data__.has(e)},Mr.prototype.clear=function(){this.__data__=new kr,this.size=0},Mr.prototype.delete=function(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r},Mr.prototype.get=function(e){return this.__data__.get(e)},Mr.prototype.has=function(e){return this.__data__.has(e)},Mr.prototype.set=function(e,t){var r=this.__data__;if(r instanceof kr){var n=r.__data__;if(!pr||n.length<199)return n.push([e,t]),this.size=++r.size,this;r=this.__data__=new Dr(n)}return r.set(e,t),this.size=r.size,this};var en=Si(cn),tn=Si(fn,!0);function rn(e,t){var r=!0;return en(e,(function(e,n,i){return r=!!t(e,n,i)})),r}function nn(e,t,r){for(var n=-1,i=e.length;++n<i;){var o=e[n],a=t(o);if(null!=a&&(void 0===s?a==a&&!Xa(a):r(a,s)))var s=a,u=o}return u}function on(e,t){var r=[];return en(e,(function(e,n,i){t(e,n,i)&&r.push(e)})),r}function an(e,t,r,n,i){var o=-1,a=e.length;for(r||(r=ao),i||(i=[]);++o<a;){var s=e[o];t>0&&r(s)?t>1?an(s,t-1,r,n,i):pt(i,s):n||(i[i.length]=s)}return i}var sn=Ei(),un=Ei(!0);function cn(e,t){return e&&sn(e,t,ws)}function fn(e,t){return e&&un(e,t,ws)}function ln(e,t){return ct(t,(function(t){return Ka(e[t])}))}function hn(e,t){for(var r=0,n=(t=ui(t,e)).length;null!=e&&r<n;)e=e[Co(t[r++])];return r&&r==n?e:void 0}function pn(e,t,r){var n=t(e);return ka(e)?n:pt(n,r(e))}function dn(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":vt&&vt in de(e)?function(e){var t=Ee.call(e,vt),r=e[vt];try{e[vt]=void 0;var n=!0}catch(e){}var i=Ne.call(e);n&&(t?e[vt]=r:delete e[vt]);return i}(e):function(e){return Ne.call(e)}(e)}function yn(e,t){return e>t}function gn(e,t){return null!=e&&Ee.call(e,t)}function vn(e,t){return null!=e&&t in de(e)}function _n(e,t,r){for(var i=r?lt:ft,o=e[0].length,a=e.length,s=a,u=n(a),c=1/0,f=[];s--;){var l=e[s];s&&t&&(l=ht(l,xt(t))),c=sr(l.length,c),u[s]=!r&&(t||o>=120&&l.length>=120)?new Ur(s&&l):void 0}l=e[0];var h=-1,p=u[0];e:for(;++h<o&&f.length<c;){var d=l[h],y=t?t(d):d;if(d=r||0!==d?d:0,!(p?Ot(p,y):i(f,y,r))){for(s=a;--s;){var g=u[s];if(!(g?Ot(g,y):i(e[s],y,r)))continue e}p&&p.push(y),f.push(d)}}return f}function mn(e,t,r){var n=null==(e=vo(e,t=ui(t,e)))?e:e[Co(Fo(t))];return null==n?void 0:it(n,e,r)}function bn(e){return qa(e)&&dn(e)==s}function wn(e,t,r,n,i){return e===t||(null==e||null==t||!qa(e)&&!qa(t)?e!=e&&t!=t:function(e,t,r,n,i,o){var a=ka(e),h=ka(t),p=a?u:no(e),w=h?u:no(t),A=(p=p==s?g:p)==g,I=(w=w==s?g:w)==g,N=p==w;if(N&&ja(e)){if(!ja(t))return!1;a=!0,A=!1}if(N&&!A)return o||(o=new Mr),a||Qa(e)?$i(e,t,r,n,i,o):function(e,t,r,n,i,o,a){switch(r){case E:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case S:return!(e.byteLength!=t.byteLength||!o(new De(e),new De(t)));case c:case f:case y:return xa(+e,+t);case l:return e.name==t.name&&e.message==t.message;case v:case m:return e==t+"";case d:var s=Ft;case _:var u=1&n;if(s||(s=Vt),e.size!=t.size&&!u)return!1;var h=a.get(e);if(h)return h==t;n|=2,a.set(e,t);var p=$i(s(e),s(t),n,i,o,a);return a.delete(e),p;case b:if(Nr)return Nr.call(e)==Nr.call(t)}return!1}(e,t,p,r,n,i,o);if(!(1&r)){var C=A&&Ee.call(e,"__wrapped__"),T=I&&Ee.call(t,"__wrapped__");if(C||T){var R=C?e.value():e,x=T?t.value():t;return o||(o=new Mr),i(R,x,r,n,o)}}if(!N)return!1;return o||(o=new Mr),function(e,t,r,n,i,o){var a=1&r,s=Hi(e),u=s.length,c=Hi(t).length;if(u!=c&&!a)return!1;var f=u;for(;f--;){var l=s[f];if(!(a?l in t:Ee.call(t,l)))return!1}var h=o.get(e),p=o.get(t);if(h&&p)return h==t&&p==e;var d=!0;o.set(e,t),o.set(t,e);var y=a;for(;++f<u;){l=s[f];var g=e[l],v=t[l];if(n)var _=a?n(v,g,l,t,e,o):n(g,v,l,e,t,o);if(!(void 0===_?g===v||i(g,v,r,n,o):_)){d=!1;break}y||(y="constructor"==l)}if(d&&!y){var m=e.constructor,b=t.constructor;m==b||!("constructor"in e)||!("constructor"in t)||"function"==typeof m&&m instanceof m&&"function"==typeof b&&b instanceof b||(d=!1)}return o.delete(e),o.delete(t),d}(e,t,r,n,i,o)}(e,t,r,n,wn,i))}function Sn(e,t,r,n){var i=r.length,o=i,a=!n;if(null==e)return!o;for(e=de(e);i--;){var s=r[i];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<o;){var u=(s=r[i])[0],c=e[u],f=s[1];if(a&&s[2]){if(void 0===c&&!(u in e))return!1}else{var l=new Mr;if(n)var h=n(c,f,u,e,t,l);if(!(void 0===h?wn(f,c,3,n,l):h))return!1}}return!0}function En(e){return!(!$a(e)||(t=e,Ie&&Ie in t))&&(Ka(e)?Re:oe).test(To(e));var t}function An(e){return"function"==typeof e?e:null==e?Ws:"object"==typeof e?ka(e)?xn(e[0],e[1]):Rn(e):ru(e)}function In(e){if(!ho(e))return or(e);var t=[];for(var r in de(e))Ee.call(e,r)&&"constructor"!=r&&t.push(r);return t}function Nn(e){if(!$a(e))return function(e){var t=[];if(null!=e)for(var r in de(e))t.push(r);return t}(e);var t=ho(e),r=[];for(var n in e)("constructor"!=n||!t&&Ee.call(e,n))&&r.push(n);return r}function Cn(e,t){return e<t}function Tn(e,t){var r=-1,i=Ua(e)?n(e.length):[];return en(e,(function(e,n,o){i[++r]=t(e,n,o)})),i}function Rn(e){var t=Qi(e);return 1==t.length&&t[0][2]?yo(t[0][0],t[0][1]):function(r){return r===e||Sn(r,e,t)}}function xn(e,t){return co(e)&&po(t)?yo(Co(e),t):function(r){var n=gs(r,e);return void 0===n&&n===t?vs(r,e):wn(t,n,3)}}function Pn(e,t,r,n,i){e!==t&&sn(t,(function(o,a){if(i||(i=new Mr),$a(o))!function(e,t,r,n,i,o,a){var s=mo(e,r),u=mo(t,r),c=a.get(u);if(c)return void zr(e,r,c);var f=o?o(s,u,r+"",e,t,a):void 0,l=void 0===f;if(l){var h=ka(u),p=!h&&ja(u),d=!h&&!p&&Qa(u);f=u,h||p||d?ka(s)?f=s:Ma(s)?f=_i(s):p?(l=!1,f=hi(u,!0)):d?(l=!1,f=di(u,!0)):f=[]:Ga(u)||Ba(u)?(f=s,Ba(s)?f=ss(s):$a(s)&&!Ka(s)||(f=oo(u))):l=!1}l&&(a.set(u,f),i(f,u,n,o,a),a.delete(u));zr(e,r,f)}(e,t,a,r,Pn,n,i);else{var s=n?n(mo(e,a),o,a+"",e,t,i):void 0;void 0===s&&(s=o),zr(e,a,s)}}),Ss)}function On(e,t){var r=e.length;if(r)return so(t+=t<0?r:0,r)?e[t]:void 0}function Bn(e,t,r){t=t.length?ht(t,(function(e){return ka(e)?function(t){return hn(t,1===e.length?e[0]:e)}:e})):[Ws];var n=-1;return t=ht(t,xt(Zi())),function(e,t){var r=e.length;for(e.sort(t);r--;)e[r]=e[r].value;return e}(Tn(e,(function(e,r,i){return{criteria:ht(t,(function(t){return t(e)})),index:++n,value:e}})),(function(e,t){return function(e,t,r){var n=-1,i=e.criteria,o=t.criteria,a=i.length,s=r.length;for(;++n<a;){var u=yi(i[n],o[n]);if(u){if(n>=s)return u;var c=r[n];return u*("desc"==c?-1:1)}}return e.index-t.index}(e,t,r)}))}function kn(e,t,r){for(var n=-1,i=t.length,o={};++n<i;){var a=t[n],s=hn(e,a);r(s,a)&&zn(o,ui(a,e),s)}return o}function Dn(e,t,r,n){var i=n?wt:bt,o=-1,a=t.length,s=e;for(e===t&&(t=_i(t)),r&&(s=ht(e,xt(r)));++o<a;)for(var u=0,c=t[o],f=r?r(c):c;(u=i(s,f,u,n))>-1;)s!==e&&Ge.call(s,u,1),Ge.call(e,u,1);return e}function Un(e,t){for(var r=e?t.length:0,n=r-1;r--;){var i=t[r];if(r==n||i!==o){var o=i;so(i)?Ge.call(e,i,1):ei(e,i)}}return e}function Mn(e,t){return e+er(fr()*(t-e+1))}function jn(e,t){var r="";if(!e||t<1||t>9007199254740991)return r;do{t%2&&(r+=e),(t=er(t/2))&&(e+=e)}while(t);return r}function Ln(e,t){return So(go(e,t,Ws),e+"")}function Fn(e){return Lr(xs(e))}function Kn(e,t){var r=xs(e);return Io(r,Jr(t,0,r.length))}function zn(e,t,r,n){if(!$a(e))return e;for(var i=-1,o=(t=ui(t,e)).length,a=o-1,s=e;null!=s&&++i<o;){var u=Co(t[i]),c=r;if("__proto__"===u||"constructor"===u||"prototype"===u)return e;if(i!=a){var f=s[u];void 0===(c=n?n(f,u,s):void 0)&&(c=$a(f)?f:so(t[i+1])?[]:{})}Vr(s,u,c),s=s[u]}return e}var Vn=_r?function(e,t){return _r.set(e,t),e}:Ws,$n=It?function(e,t){return It(e,"toString",{configurable:!0,enumerable:!1,value:$s(t),writable:!0})}:Ws;function qn(e){return Io(xs(e))}function Hn(e,t,r){var i=-1,o=e.length;t<0&&(t=-t>o?0:o+t),(r=r>o?o:r)<0&&(r+=o),o=t>r?0:r-t>>>0,t>>>=0;for(var a=n(o);++i<o;)a[i]=e[i+t];return a}function Wn(e,t){var r;return en(e,(function(e,n,i){return!(r=t(e,n,i))})),!!r}function Gn(e,t,r){var n=0,i=null==e?n:e.length;if("number"==typeof t&&t==t&&i<=2147483647){for(;n<i;){var o=n+i>>>1,a=e[o];null!==a&&!Xa(a)&&(r?a<=t:a<t)?n=o+1:i=o}return i}return Jn(e,t,Ws,r)}function Jn(e,t,r,n){var i=0,o=null==e?0:e.length;if(0===o)return 0;for(var a=(t=r(t))!=t,s=null===t,u=Xa(t),c=void 0===t;i<o;){var f=er((i+o)/2),l=r(e[f]),h=void 0!==l,p=null===l,d=l==l,y=Xa(l);if(a)var g=n||d;else g=c?d&&(n||h):s?d&&h&&(n||!p):u?d&&h&&!p&&(n||!y):!p&&!y&&(n?l<=t:l<t);g?i=f+1:o=f}return sr(o,4294967294)}function Yn(e,t){for(var r=-1,n=e.length,i=0,o=[];++r<n;){var a=e[r],s=t?t(a):a;if(!r||!xa(s,u)){var u=s;o[i++]=0===a?0:a}}return o}function Zn(e){return"number"==typeof e?e:Xa(e)?NaN:+e}function Xn(e){if("string"==typeof e)return e;if(ka(e))return ht(e,Xn)+"";if(Xa(e))return Cr?Cr.call(e):"";var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function Qn(e,t,r){var n=-1,i=ft,o=e.length,a=!0,s=[],u=s;if(r)a=!1,i=lt;else if(o>=200){var c=t?null:ji(e);if(c)return Vt(c);a=!1,i=Ot,u=new Ur}else u=t?[]:s;e:for(;++n<o;){var f=e[n],l=t?t(f):f;if(f=r||0!==f?f:0,a&&l==l){for(var h=u.length;h--;)if(u[h]===l)continue e;t&&u.push(l),s.push(f)}else i(u,l,r)||(u!==s&&u.push(l),s.push(f))}return s}function ei(e,t){return null==(e=vo(e,t=ui(t,e)))||delete e[Co(Fo(t))]}function ti(e,t,r,n){return zn(e,t,r(hn(e,t)),n)}function ri(e,t,r,n){for(var i=e.length,o=n?i:-1;(n?o--:++o<i)&&t(e[o],o,e););return r?Hn(e,n?0:o,n?o+1:i):Hn(e,n?o+1:0,n?i:o)}function ni(e,t){var r=e;return r instanceof Or&&(r=r.value()),dt(t,(function(e,t){return t.func.apply(t.thisArg,pt([e],t.args))}),r)}function ii(e,t,r){var i=e.length;if(i<2)return i?Qn(e[0]):[];for(var o=-1,a=n(i);++o<i;)for(var s=e[o],u=-1;++u<i;)u!=o&&(a[o]=Qr(a[o]||s,e[u],t,r));return Qn(an(a,1),t,r)}function oi(e,t,r){for(var n=-1,i=e.length,o=t.length,a={};++n<i;){var s=n<o?t[n]:void 0;r(a,e[n],s)}return a}function ai(e){return Ma(e)?e:[]}function si(e){return"function"==typeof e?e:Ws}function ui(e,t){return ka(e)?e:co(e,t)?[e]:No(us(e))}var ci=Ln;function fi(e,t,r){var n=e.length;return r=void 0===r?n:r,!t&&r>=n?e:Hn(e,t,r)}var li=Yt||function(e){return He.clearTimeout(e)};function hi(e,t){if(t)return e.slice();var r=e.length,n=Ke?Ke(r):new e.constructor(r);return e.copy(n),n}function pi(e){var t=new e.constructor(e.byteLength);return new De(t).set(new De(e)),t}function di(e,t){var r=t?pi(e.buffer):e.buffer;return new e.constructor(r,e.byteOffset,e.length)}function yi(e,t){if(e!==t){var r=void 0!==e,n=null===e,i=e==e,o=Xa(e),a=void 0!==t,s=null===t,u=t==t,c=Xa(t);if(!s&&!c&&!o&&e>t||o&&a&&u&&!s&&!c||n&&a&&u||!r&&u||!i)return 1;if(!n&&!o&&!c&&e<t||c&&r&&i&&!n&&!o||s&&r&&i||!a&&i||!u)return-1}return 0}function gi(e,t,r,i){for(var o=-1,a=e.length,s=r.length,u=-1,c=t.length,f=ar(a-s,0),l=n(c+f),h=!i;++u<c;)l[u]=t[u];for(;++o<s;)(h||o<a)&&(l[r[o]]=e[o]);for(;f--;)l[u++]=e[o++];return l}function vi(e,t,r,i){for(var o=-1,a=e.length,s=-1,u=r.length,c=-1,f=t.length,l=ar(a-u,0),h=n(l+f),p=!i;++o<l;)h[o]=e[o];for(var d=o;++c<f;)h[d+c]=t[c];for(;++s<u;)(p||o<a)&&(h[d+r[s]]=e[o++]);return h}function _i(e,t){var r=-1,i=e.length;for(t||(t=n(i));++r<i;)t[r]=e[r];return t}function mi(e,t,r,n){var i=!r;r||(r={});for(var o=-1,a=t.length;++o<a;){var s=t[o],u=n?n(r[s],e[s],s,r,e):void 0;void 0===u&&(u=e[s]),i?Wr(r,s,u):Vr(r,s,u)}return r}function bi(e,t){return function(r,n){var i=ka(r)?ot:qr,o=t?t():{};return i(r,e,Zi(n,2),o)}}function wi(e){return Ln((function(t,r){var n=-1,i=r.length,o=i>1?r[i-1]:void 0,a=i>2?r[2]:void 0;for(o=e.length>3&&"function"==typeof o?(i--,o):void 0,a&&uo(r[0],r[1],a)&&(o=i<3?void 0:o,i=1),t=de(t);++n<i;){var s=r[n];s&&e(t,s,n,o)}return t}))}function Si(e,t){return function(r,n){if(null==r)return r;if(!Ua(r))return e(r,n);for(var i=r.length,o=t?i:-1,a=de(r);(t?o--:++o<i)&&!1!==n(a[o],o,a););return r}}function Ei(e){return function(t,r,n){for(var i=-1,o=de(t),a=n(t),s=a.length;s--;){var u=a[e?s:++i];if(!1===r(o[u],u,o))break}return t}}function Ai(e){return function(t){var r=Lt(t=us(t))?Ht(t):void 0,n=r?r[0]:t.charAt(0),i=r?fi(r,1).join(""):t.slice(1);return n[e]()+i}}function Ii(e){return function(t){return dt(Ks(Bs(t).replace(Pe,"")),e,"")}}function Ni(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var r=Rr(e.prototype),n=e.apply(r,t);return $a(n)?n:r}}function Ci(e){return function(t,r,n){var i=de(t);if(!Ua(t)){var o=Zi(r,3);t=ws(t),r=function(e){return o(i[e],e,i)}}var a=e(t,r,n);return a>-1?i[o?t[a]:a]:void 0}}function Ti(e){return qi((function(t){var r=t.length,n=r,o=Pr.prototype.thru;for(e&&t.reverse();n--;){var a=t[n];if("function"!=typeof a)throw new ve(i);if(o&&!s&&"wrapper"==Ji(a))var s=new Pr([],!0)}for(n=s?n:r;++n<r;){var u=Ji(a=t[n]),c="wrapper"==u?Gi(a):void 0;s=c&&fo(c[0])&&424==c[1]&&!c[4].length&&1==c[9]?s[Ji(c[0])].apply(s,c[3]):1==a.length&&fo(a)?s[u]():s.thru(a)}return function(){var e=arguments,n=e[0];if(s&&1==e.length&&ka(n))return s.plant(n).value();for(var i=0,o=r?t[i].apply(this,e):n;++i<r;)o=t[i].call(this,o);return o}}))}function Ri(e,t,r,i,o,a,s,u,c,f){var l=128&t,h=1&t,p=2&t,d=24&t,y=512&t,g=p?void 0:Ni(e);return function v(){for(var _=arguments.length,m=n(_),b=_;b--;)m[b]=arguments[b];if(d)var w=Yi(v),S=Dt(m,w);if(i&&(m=gi(m,i,o,d)),a&&(m=vi(m,a,s,d)),_-=S,d&&_<f){var E=zt(m,w);return Ui(e,t,Ri,v.placeholder,r,m,E,u,c,f-_)}var A=h?r:this,I=p?A[e]:e;return _=m.length,u?m=_o(m,u):y&&_>1&&m.reverse(),l&&c<_&&(m.length=c),this&&this!==He&&this instanceof v&&(I=g||Ni(I)),I.apply(A,m)}}function xi(e,t){return function(r,n){return function(e,t,r,n){return cn(e,(function(e,i,o){t(n,r(e),i,o)})),n}(r,e,t(n),{})}}function Pi(e,t){return function(r,n){var i;if(void 0===r&&void 0===n)return t;if(void 0!==r&&(i=r),void 0!==n){if(void 0===i)return n;"string"==typeof r||"string"==typeof n?(r=Xn(r),n=Xn(n)):(r=Zn(r),n=Zn(n)),i=e(r,n)}return i}}function Oi(e){return qi((function(t){return t=ht(t,xt(Zi())),Ln((function(r){var n=this;return e(t,(function(e){return it(e,n,r)}))}))}))}function Bi(e,t){var r=(t=void 0===t?" ":Xn(t)).length;if(r<2)return r?jn(t,e):t;var n=jn(t,Qt(e/qt(t)));return Lt(t)?fi(Ht(n),0,e).join(""):n.slice(0,e)}function ki(e){return function(t,r,i){return i&&"number"!=typeof i&&uo(t,r,i)&&(r=i=void 0),t=ns(t),void 0===r?(r=t,t=0):r=ns(r),function(e,t,r,i){for(var o=-1,a=ar(Qt((t-e)/(r||1)),0),s=n(a);a--;)s[i?a:++o]=e,e+=r;return s}(t,r,i=void 0===i?t<r?1:-1:ns(i),e)}}function Di(e){return function(t,r){return"string"==typeof t&&"string"==typeof r||(t=as(t),r=as(r)),e(t,r)}}function Ui(e,t,r,n,i,o,a,s,u,c){var f=8&t;t|=f?32:64,4&(t&=~(f?64:32))||(t&=-4);var l=[e,t,i,f?o:void 0,f?a:void 0,f?void 0:o,f?void 0:a,s,u,c],h=r.apply(void 0,l);return fo(e)&&bo(h,l),h.placeholder=n,Eo(h,e,t)}function Mi(e){var t=pe[e];return function(e,r){if(e=as(e),(r=null==r?0:sr(is(r),292))&&nr(e)){var n=(us(e)+"e").split("e");return+((n=(us(t(n[0]+"e"+(+n[1]+r)))+"e").split("e"))[0]+"e"+(+n[1]-r))}return t(e)}}var ji=yr&&1/Vt(new yr([,-0]))[1]==1/0?function(e){return new yr(e)}:Xs;function Li(e){return function(t){var r=no(t);return r==d?Ft(t):r==_?$t(t):function(e,t){return ht(t,(function(t){return[t,e[t]]}))}(t,e(t))}}function Fi(e,t,r,a,s,u,c,f){var l=2&t;if(!l&&"function"!=typeof e)throw new ve(i);var h=a?a.length:0;if(h||(t&=-97,a=s=void 0),c=void 0===c?c:ar(is(c),0),f=void 0===f?f:is(f),h-=s?s.length:0,64&t){var p=a,d=s;a=s=void 0}var y=l?void 0:Gi(e),g=[e,t,r,a,s,p,d,u,c,f];if(y&&function(e,t){var r=e[1],n=t[1],i=r|n,a=i<131,s=128==n&&8==r||128==n&&256==r&&e[7].length<=t[8]||384==n&&t[7].length<=t[8]&&8==r;if(!a&&!s)return e;1&n&&(e[2]=t[2],i|=1&r?0:4);var u=t[3];if(u){var c=e[3];e[3]=c?gi(c,u,t[4]):u,e[4]=c?zt(e[3],o):t[4]}(u=t[5])&&(c=e[5],e[5]=c?vi(c,u,t[6]):u,e[6]=c?zt(e[5],o):t[6]);(u=t[7])&&(e[7]=u);128&n&&(e[8]=null==e[8]?t[8]:sr(e[8],t[8]));null==e[9]&&(e[9]=t[9]);e[0]=t[0],e[1]=i}(g,y),e=g[0],t=g[1],r=g[2],a=g[3],s=g[4],!(f=g[9]=void 0===g[9]?l?0:e.length:ar(g[9]-h,0))&&24&t&&(t&=-25),t&&1!=t)v=8==t||16==t?function(e,t,r){var i=Ni(e);return function o(){for(var a=arguments.length,s=n(a),u=a,c=Yi(o);u--;)s[u]=arguments[u];var f=a<3&&s[0]!==c&&s[a-1]!==c?[]:zt(s,c);if((a-=f.length)<r)return Ui(e,t,Ri,o.placeholder,void 0,s,f,void 0,void 0,r-a);var l=this&&this!==He&&this instanceof o?i:e;return it(l,this,s)}}(e,t,f):32!=t&&33!=t||s.length?Ri.apply(void 0,g):function(e,t,r,i){var o=1&t,a=Ni(e);return function t(){for(var s=-1,u=arguments.length,c=-1,f=i.length,l=n(f+u),h=this&&this!==He&&this instanceof t?a:e;++c<f;)l[c]=i[c];for(;u--;)l[c++]=arguments[++s];return it(h,o?r:this,l)}}(e,t,r,a);else var v=function(e,t,r){var n=1&t,i=Ni(e);return function t(){var o=this&&this!==He&&this instanceof t?i:e;return o.apply(n?r:this,arguments)}}(e,t,r);return Eo((y?Vn:bo)(v,g),e,t)}function Ki(e,t,r,n){return void 0===e||xa(e,be[r])&&!Ee.call(n,r)?t:e}function zi(e,t,r,n,i,o){return $a(e)&&$a(t)&&(o.set(t,e),Pn(e,t,void 0,zi,o),o.delete(t)),e}function Vi(e){return Ga(e)?void 0:e}function $i(e,t,r,n,i,o){var a=1&r,s=e.length,u=t.length;if(s!=u&&!(a&&u>s))return!1;var c=o.get(e),f=o.get(t);if(c&&f)return c==t&&f==e;var l=-1,h=!0,p=2&r?new Ur:void 0;for(o.set(e,t),o.set(t,e);++l<s;){var d=e[l],y=t[l];if(n)var g=a?n(y,d,l,t,e,o):n(d,y,l,e,t,o);if(void 0!==g){if(g)continue;h=!1;break}if(p){if(!gt(t,(function(e,t){if(!Ot(p,t)&&(d===e||i(d,e,r,n,o)))return p.push(t)}))){h=!1;break}}else if(d!==y&&!i(d,y,r,n,o)){h=!1;break}}return o.delete(e),o.delete(t),h}function qi(e){return So(go(e,void 0,Do),e+"")}function Hi(e){return pn(e,ws,to)}function Wi(e){return pn(e,Ss,ro)}var Gi=_r?function(e){return _r.get(e)}:Xs;function Ji(e){for(var t=e.name+"",r=mr[t],n=Ee.call(mr,t)?r.length:0;n--;){var i=r[n],o=i.func;if(null==o||o==e)return i.name}return t}function Yi(e){return(Ee.call(Tr,"placeholder")?Tr:e).placeholder}function Zi(){var e=Tr.iteratee||Gs;return e=e===Gs?An:e,arguments.length?e(arguments[0],arguments[1]):e}function Xi(e,t){var r,n,i=e.__data__;return("string"==(n=typeof(r=t))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?i["string"==typeof t?"string":"hash"]:i.map}function Qi(e){for(var t=ws(e),r=t.length;r--;){var n=t[r],i=e[n];t[r]=[n,i,po(i)]}return t}function eo(e,t){var r=function(e,t){return null==e?void 0:e[t]}(e,t);return En(r)?r:void 0}var to=tr?function(e){return null==e?[]:(e=de(e),ct(tr(e),(function(t){return We.call(e,t)})))}:ou,ro=tr?function(e){for(var t=[];e;)pt(t,to(e)),e=$e(e);return t}:ou,no=dn;function io(e,t,r){for(var n=-1,i=(t=ui(t,e)).length,o=!1;++n<i;){var a=Co(t[n]);if(!(o=null!=e&&r(e,a)))break;e=e[a]}return o||++n!=i?o:!!(i=null==e?0:e.length)&&Va(i)&&so(a,i)&&(ka(e)||Ba(e))}function oo(e){return"function"!=typeof e.constructor||ho(e)?{}:Rr($e(e))}function ao(e){return ka(e)||Ba(e)||!!(Ye&&e&&e[Ye])}function so(e,t){var r=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==r||"symbol"!=r&&se.test(e))&&e>-1&&e%1==0&&e<t}function uo(e,t,r){if(!$a(r))return!1;var n=typeof t;return!!("number"==n?Ua(r)&&so(t,r.length):"string"==n&&t in r)&&xa(r[t],e)}function co(e,t){if(ka(e))return!1;var r=typeof e;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=e&&!Xa(e))||(V.test(e)||!z.test(e)||null!=t&&e in de(t))}function fo(e){var t=Ji(e),r=Tr[t];if("function"!=typeof r||!(t in Or.prototype))return!1;if(e===r)return!0;var n=Gi(r);return!!n&&e===n[0]}(hr&&no(new hr(new ArrayBuffer(1)))!=E||pr&&no(new pr)!=d||dr&&"[object Promise]"!=no(dr.resolve())||yr&&no(new yr)!=_||gr&&no(new gr)!=w)&&(no=function(e){var t=dn(e),r=t==g?e.constructor:void 0,n=r?To(r):"";if(n)switch(n){case br:return E;case wr:return d;case Sr:return"[object Promise]";case Er:return _;case Ar:return w}return t});var lo=we?Ka:au;function ho(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||be)}function po(e){return e==e&&!$a(e)}function yo(e,t){return function(r){return null!=r&&(r[e]===t&&(void 0!==t||e in de(r)))}}function go(e,t,r){return t=ar(void 0===t?e.length-1:t,0),function(){for(var i=arguments,o=-1,a=ar(i.length-t,0),s=n(a);++o<a;)s[o]=i[t+o];o=-1;for(var u=n(t+1);++o<t;)u[o]=i[o];return u[t]=r(s),it(e,this,u)}}function vo(e,t){return t.length<2?e:hn(e,Hn(t,0,-1))}function _o(e,t){for(var r=e.length,n=sr(t.length,r),i=_i(e);n--;){var o=t[n];e[n]=so(o,r)?i[o]:void 0}return e}function mo(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]}var bo=Ao(Vn),wo=Xt||function(e,t){return He.setTimeout(e,t)},So=Ao($n);function Eo(e,t,r){var n=t+"";return So(e,function(e,t){var r=t.length;if(!r)return e;var n=r-1;return t[n]=(r>1?"& ":"")+t[n],t=t.join(r>2?", ":" "),e.replace(J,"{\n/* [wrapped with "+t+"] */\n")}(n,function(e,t){return at(a,(function(r){var n="_."+r[0];t&r[1]&&!ft(e,n)&&e.push(n)})),e.sort()}(function(e){var t=e.match(Y);return t?t[1].split(Z):[]}(n),r)))}function Ao(e){var t=0,r=0;return function(){var n=ur(),i=16-(n-r);if(r=n,i>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function Io(e,t){var r=-1,n=e.length,i=n-1;for(t=void 0===t?n:t;++r<t;){var o=Mn(r,i),a=e[o];e[o]=e[r],e[r]=a}return e.length=t,e}var No=function(e){var t=Aa(e,(function(e){return 500===r.size&&r.clear(),e})),r=t.cache;return t}((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace($,(function(e,r,n,i){t.push(n?i.replace(ee,"$1"):r||e)})),t}));function Co(e){if("string"==typeof e||Xa(e))return e;var t=e+"";return"0"==t&&1/e==-1/0?"-0":t}function To(e){if(null!=e){try{return Se.call(e)}catch(e){}try{return e+""}catch(e){}}return""}function Ro(e){if(e instanceof Or)return e.clone();var t=new Pr(e.__wrapped__,e.__chain__);return t.__actions__=_i(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}var xo=Ln((function(e,t){return Ma(e)?Qr(e,an(t,1,Ma,!0)):[]})),Po=Ln((function(e,t){var r=Fo(t);return Ma(r)&&(r=void 0),Ma(e)?Qr(e,an(t,1,Ma,!0),Zi(r,2)):[]})),Oo=Ln((function(e,t){var r=Fo(t);return Ma(r)&&(r=void 0),Ma(e)?Qr(e,an(t,1,Ma,!0),void 0,r):[]}));function Bo(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=null==r?0:is(r);return i<0&&(i=ar(n+i,0)),mt(e,Zi(t,3),i)}function ko(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=n-1;return void 0!==r&&(i=is(r),i=r<0?ar(n+i,0):sr(i,n-1)),mt(e,Zi(t,3),i,!0)}function Do(e){return(null==e?0:e.length)?an(e,1):[]}function Uo(e){return e&&e.length?e[0]:void 0}var Mo=Ln((function(e){var t=ht(e,ai);return t.length&&t[0]===e[0]?_n(t):[]})),jo=Ln((function(e){var t=Fo(e),r=ht(e,ai);return t===Fo(r)?t=void 0:r.pop(),r.length&&r[0]===e[0]?_n(r,Zi(t,2)):[]})),Lo=Ln((function(e){var t=Fo(e),r=ht(e,ai);return(t="function"==typeof t?t:void 0)&&r.pop(),r.length&&r[0]===e[0]?_n(r,void 0,t):[]}));function Fo(e){var t=null==e?0:e.length;return t?e[t-1]:void 0}var Ko=Ln(zo);function zo(e,t){return e&&e.length&&t&&t.length?Dn(e,t):e}var Vo=qi((function(e,t){var r=null==e?0:e.length,n=Gr(e,t);return Un(e,ht(t,(function(e){return so(e,r)?+e:e})).sort(yi)),n}));function $o(e){return null==e?e:lr.call(e)}var qo=Ln((function(e){return Qn(an(e,1,Ma,!0))})),Ho=Ln((function(e){var t=Fo(e);return Ma(t)&&(t=void 0),Qn(an(e,1,Ma,!0),Zi(t,2))})),Wo=Ln((function(e){var t=Fo(e);return t="function"==typeof t?t:void 0,Qn(an(e,1,Ma,!0),void 0,t)}));function Go(e){if(!e||!e.length)return[];var t=0;return e=ct(e,(function(e){if(Ma(e))return t=ar(e.length,t),!0})),Tt(t,(function(t){return ht(e,At(t))}))}function Jo(e,t){if(!e||!e.length)return[];var r=Go(e);return null==t?r:ht(r,(function(e){return it(t,void 0,e)}))}var Yo=Ln((function(e,t){return Ma(e)?Qr(e,t):[]})),Zo=Ln((function(e){return ii(ct(e,Ma))})),Xo=Ln((function(e){var t=Fo(e);return Ma(t)&&(t=void 0),ii(ct(e,Ma),Zi(t,2))})),Qo=Ln((function(e){var t=Fo(e);return t="function"==typeof t?t:void 0,ii(ct(e,Ma),void 0,t)})),ea=Ln(Go);var ta=Ln((function(e){var t=e.length,r=t>1?e[t-1]:void 0;return r="function"==typeof r?(e.pop(),r):void 0,Jo(e,r)}));function ra(e){var t=Tr(e);return t.__chain__=!0,t}function na(e,t){return t(e)}var ia=qi((function(e){var t=e.length,r=t?e[0]:0,n=this.__wrapped__,i=function(t){return Gr(t,e)};return!(t>1||this.__actions__.length)&&n instanceof Or&&so(r)?((n=n.slice(r,+r+(t?1:0))).__actions__.push({func:na,args:[i],thisArg:void 0}),new Pr(n,this.__chain__).thru((function(e){return t&&!e.length&&e.push(void 0),e}))):this.thru(i)}));var oa=bi((function(e,t,r){Ee.call(e,r)?++e[r]:Wr(e,r,1)}));var aa=Ci(Bo),sa=Ci(ko);function ua(e,t){return(ka(e)?at:en)(e,Zi(t,3))}function ca(e,t){return(ka(e)?st:tn)(e,Zi(t,3))}var fa=bi((function(e,t,r){Ee.call(e,r)?e[r].push(t):Wr(e,r,[t])}));var la=Ln((function(e,t,r){var i=-1,o="function"==typeof t,a=Ua(e)?n(e.length):[];return en(e,(function(e){a[++i]=o?it(t,e,r):mn(e,t,r)})),a})),ha=bi((function(e,t,r){Wr(e,r,t)}));function pa(e,t){return(ka(e)?ht:Tn)(e,Zi(t,3))}var da=bi((function(e,t,r){e[r?0:1].push(t)}),(function(){return[[],[]]}));var ya=Ln((function(e,t){if(null==e)return[];var r=t.length;return r>1&&uo(e,t[0],t[1])?t=[]:r>2&&uo(t[0],t[1],t[2])&&(t=[t[0]]),Bn(e,an(t,1),[])})),ga=Zt||function(){return He.Date.now()};function va(e,t,r){return t=r?void 0:t,Fi(e,128,void 0,void 0,void 0,void 0,t=e&&null==t?e.length:t)}function _a(e,t){var r;if("function"!=typeof t)throw new ve(i);return e=is(e),function(){return--e>0&&(r=t.apply(this,arguments)),e<=1&&(t=void 0),r}}var ma=Ln((function(e,t,r){var n=1;if(r.length){var i=zt(r,Yi(ma));n|=32}return Fi(e,n,t,r,i)})),ba=Ln((function(e,t,r){var n=3;if(r.length){var i=zt(r,Yi(ba));n|=32}return Fi(t,n,e,r,i)}));function wa(e,t,r){var n,o,a,s,u,c,f=0,l=!1,h=!1,p=!0;if("function"!=typeof e)throw new ve(i);function d(t){var r=n,i=o;return n=o=void 0,f=t,s=e.apply(i,r)}function y(e){return f=e,u=wo(v,t),l?d(e):s}function g(e){var r=e-c;return void 0===c||r>=t||r<0||h&&e-f>=a}function v(){var e=ga();if(g(e))return _(e);u=wo(v,function(e){var r=t-(e-c);return h?sr(r,a-(e-f)):r}(e))}function _(e){return u=void 0,p&&n?d(e):(n=o=void 0,s)}function m(){var e=ga(),r=g(e);if(n=arguments,o=this,c=e,r){if(void 0===u)return y(c);if(h)return li(u),u=wo(v,t),d(c)}return void 0===u&&(u=wo(v,t)),s}return t=as(t)||0,$a(r)&&(l=!!r.leading,a=(h="maxWait"in r)?ar(as(r.maxWait)||0,t):a,p="trailing"in r?!!r.trailing:p),m.cancel=function(){void 0!==u&&li(u),f=0,n=c=o=u=void 0},m.flush=function(){return void 0===u?s:_(ga())},m}var Sa=Ln((function(e,t){return Xr(e,1,t)})),Ea=Ln((function(e,t,r){return Xr(e,as(t)||0,r)}));function Aa(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new ve(i);var r=function(){var n=arguments,i=t?t.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=e.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(Aa.Cache||Dr),r}function Ia(e){if("function"!=typeof e)throw new ve(i);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}Aa.Cache=Dr;var Na=ci((function(e,t){var r=(t=1==t.length&&ka(t[0])?ht(t[0],xt(Zi())):ht(an(t,1),xt(Zi()))).length;return Ln((function(n){for(var i=-1,o=sr(n.length,r);++i<o;)n[i]=t[i].call(this,n[i]);return it(e,this,n)}))})),Ca=Ln((function(e,t){return Fi(e,32,void 0,t,zt(t,Yi(Ca)))})),Ta=Ln((function(e,t){return Fi(e,64,void 0,t,zt(t,Yi(Ta)))})),Ra=qi((function(e,t){return Fi(e,256,void 0,void 0,void 0,t)}));function xa(e,t){return e===t||e!=e&&t!=t}var Pa=Di(yn),Oa=Di((function(e,t){return e>=t})),Ba=bn(function(){return arguments}())?bn:function(e){return qa(e)&&Ee.call(e,"callee")&&!We.call(e,"callee")},ka=n.isArray,Da=Xe?xt(Xe):function(e){return qa(e)&&dn(e)==S};function Ua(e){return null!=e&&Va(e.length)&&!Ka(e)}function Ma(e){return qa(e)&&Ua(e)}var ja=rr||au,La=Qe?xt(Qe):function(e){return qa(e)&&dn(e)==f};function Fa(e){if(!qa(e))return!1;var t=dn(e);return t==l||"[object DOMException]"==t||"string"==typeof e.message&&"string"==typeof e.name&&!Ga(e)}function Ka(e){if(!$a(e))return!1;var t=dn(e);return t==h||t==p||"[object AsyncFunction]"==t||"[object Proxy]"==t}function za(e){return"number"==typeof e&&e==is(e)}function Va(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991}function $a(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}function qa(e){return null!=e&&"object"==typeof e}var Ha=et?xt(et):function(e){return qa(e)&&no(e)==d};function Wa(e){return"number"==typeof e||qa(e)&&dn(e)==y}function Ga(e){if(!qa(e)||dn(e)!=g)return!1;var t=$e(e);if(null===t)return!0;var r=Ee.call(t,"constructor")&&t.constructor;return"function"==typeof r&&r instanceof r&&Se.call(r)==Ce}var Ja=tt?xt(tt):function(e){return qa(e)&&dn(e)==v};var Ya=rt?xt(rt):function(e){return qa(e)&&no(e)==_};function Za(e){return"string"==typeof e||!ka(e)&&qa(e)&&dn(e)==m}function Xa(e){return"symbol"==typeof e||qa(e)&&dn(e)==b}var Qa=nt?xt(nt):function(e){return qa(e)&&Va(e.length)&&!!Le[dn(e)]};var es=Di(Cn),ts=Di((function(e,t){return e<=t}));function rs(e){if(!e)return[];if(Ua(e))return Za(e)?Ht(e):_i(e);if(Ze&&e[Ze])return function(e){for(var t,r=[];!(t=e.next()).done;)r.push(t.value);return r}(e[Ze]());var t=no(e);return(t==d?Ft:t==_?Vt:xs)(e)}function ns(e){return e?(e=as(e))===1/0||e===-1/0?17976931348623157e292*(e<0?-1:1):e==e?e:0:0===e?e:0}function is(e){var t=ns(e),r=t%1;return t==t?r?t-r:t:0}function os(e){return e?Jr(is(e),0,4294967295):0}function as(e){if("number"==typeof e)return e;if(Xa(e))return NaN;if($a(e)){var t="function"==typeof e.valueOf?e.valueOf():e;e=$a(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=Rt(e);var r=ie.test(e);return r||ae.test(e)?Ve(e.slice(2),r?2:8):ne.test(e)?NaN:+e}function ss(e){return mi(e,Ss(e))}function us(e){return null==e?"":Xn(e)}var cs=wi((function(e,t){if(ho(t)||Ua(t))mi(t,ws(t),e);else for(var r in t)Ee.call(t,r)&&Vr(e,r,t[r])})),fs=wi((function(e,t){mi(t,Ss(t),e)})),ls=wi((function(e,t,r,n){mi(t,Ss(t),e,n)})),hs=wi((function(e,t,r,n){mi(t,ws(t),e,n)})),ps=qi(Gr);var ds=Ln((function(e,t){e=de(e);var r=-1,n=t.length,i=n>2?t[2]:void 0;for(i&&uo(t[0],t[1],i)&&(n=1);++r<n;)for(var o=t[r],a=Ss(o),s=-1,u=a.length;++s<u;){var c=a[s],f=e[c];(void 0===f||xa(f,be[c])&&!Ee.call(e,c))&&(e[c]=o[c])}return e})),ys=Ln((function(e){return e.push(void 0,zi),it(As,void 0,e)}));function gs(e,t,r){var n=null==e?void 0:hn(e,t);return void 0===n?r:n}function vs(e,t){return null!=e&&io(e,t,vn)}var _s=xi((function(e,t,r){null!=t&&"function"!=typeof t.toString&&(t=Ne.call(t)),e[t]=r}),$s(Ws)),ms=xi((function(e,t,r){null!=t&&"function"!=typeof t.toString&&(t=Ne.call(t)),Ee.call(e,t)?e[t].push(r):e[t]=[r]}),Zi),bs=Ln(mn);function ws(e){return Ua(e)?jr(e):In(e)}function Ss(e){return Ua(e)?jr(e,!0):Nn(e)}var Es=wi((function(e,t,r){Pn(e,t,r)})),As=wi((function(e,t,r,n){Pn(e,t,r,n)})),Is=qi((function(e,t){var r={};if(null==e)return r;var n=!1;t=ht(t,(function(t){return t=ui(t,e),n||(n=t.length>1),t})),mi(e,Wi(e),r),n&&(r=Yr(r,7,Vi));for(var i=t.length;i--;)ei(r,t[i]);return r}));var Ns=qi((function(e,t){return null==e?{}:function(e,t){return kn(e,t,(function(t,r){return vs(e,r)}))}(e,t)}));function Cs(e,t){if(null==e)return{};var r=ht(Wi(e),(function(e){return[e]}));return t=Zi(t),kn(e,r,(function(e,r){return t(e,r[0])}))}var Ts=Li(ws),Rs=Li(Ss);function xs(e){return null==e?[]:Pt(e,ws(e))}var Ps=Ii((function(e,t,r){return t=t.toLowerCase(),e+(r?Os(t):t)}));function Os(e){return Fs(us(e).toLowerCase())}function Bs(e){return(e=us(e))&&e.replace(ue,Ut).replace(Oe,"")}var ks=Ii((function(e,t,r){return e+(r?"-":"")+t.toLowerCase()})),Ds=Ii((function(e,t,r){return e+(r?" ":"")+t.toLowerCase()})),Us=Ai("toLowerCase");var Ms=Ii((function(e,t,r){return e+(r?"_":"")+t.toLowerCase()}));var js=Ii((function(e,t,r){return e+(r?" ":"")+Fs(t)}));var Ls=Ii((function(e,t,r){return e+(r?" ":"")+t.toUpperCase()})),Fs=Ai("toUpperCase");function Ks(e,t,r){return e=us(e),void 0===(t=r?void 0:t)?function(e){return Ue.test(e)}(e)?function(e){return e.match(ke)||[]}(e):function(e){return e.match(X)||[]}(e):e.match(t)||[]}var zs=Ln((function(e,t){try{return it(e,void 0,t)}catch(e){return Fa(e)?e:new le(e)}})),Vs=qi((function(e,t){return at(t,(function(t){t=Co(t),Wr(e,t,ma(e[t],e))})),e}));function $s(e){return function(){return e}}var qs=Ti(),Hs=Ti(!0);function Ws(e){return e}function Gs(e){return An("function"==typeof e?e:Yr(e,1))}var Js=Ln((function(e,t){return function(r){return mn(r,e,t)}})),Ys=Ln((function(e,t){return function(r){return mn(e,r,t)}}));function Zs(e,t,r){var n=ws(t),i=ln(t,n);null!=r||$a(t)&&(i.length||!n.length)||(r=t,t=e,e=this,i=ln(t,ws(t)));var o=!($a(r)&&"chain"in r&&!r.chain),a=Ka(e);return at(i,(function(r){var n=t[r];e[r]=n,a&&(e.prototype[r]=function(){var t=this.__chain__;if(o||t){var r=e(this.__wrapped__),i=r.__actions__=_i(this.__actions__);return i.push({func:n,args:arguments,thisArg:e}),r.__chain__=t,r}return n.apply(e,pt([this.value()],arguments))})})),e}function Xs(){}var Qs=Oi(ht),eu=Oi(ut),tu=Oi(gt);function ru(e){return co(e)?At(Co(e)):function(e){return function(t){return hn(t,e)}}(e)}var nu=ki(),iu=ki(!0);function ou(){return[]}function au(){return!1}var su=Pi((function(e,t){return e+t}),0),uu=Mi("ceil"),cu=Pi((function(e,t){return e/t}),1),fu=Mi("floor");var lu,hu=Pi((function(e,t){return e*t}),1),pu=Mi("round"),du=Pi((function(e,t){return e-t}),0);return Tr.after=function(e,t){if("function"!=typeof t)throw new ve(i);return e=is(e),function(){if(--e<1)return t.apply(this,arguments)}},Tr.ary=va,Tr.assign=cs,Tr.assignIn=fs,Tr.assignInWith=ls,Tr.assignWith=hs,Tr.at=ps,Tr.before=_a,Tr.bind=ma,Tr.bindAll=Vs,Tr.bindKey=ba,Tr.castArray=function(){if(!arguments.length)return[];var e=arguments[0];return ka(e)?e:[e]},Tr.chain=ra,Tr.chunk=function(e,t,r){t=(r?uo(e,t,r):void 0===t)?1:ar(is(t),0);var i=null==e?0:e.length;if(!i||t<1)return[];for(var o=0,a=0,s=n(Qt(i/t));o<i;)s[a++]=Hn(e,o,o+=t);return s},Tr.compact=function(e){for(var t=-1,r=null==e?0:e.length,n=0,i=[];++t<r;){var o=e[t];o&&(i[n++]=o)}return i},Tr.concat=function(){var e=arguments.length;if(!e)return[];for(var t=n(e-1),r=arguments[0],i=e;i--;)t[i-1]=arguments[i];return pt(ka(r)?_i(r):[r],an(t,1))},Tr.cond=function(e){var t=null==e?0:e.length,r=Zi();return e=t?ht(e,(function(e){if("function"!=typeof e[1])throw new ve(i);return[r(e[0]),e[1]]})):[],Ln((function(r){for(var n=-1;++n<t;){var i=e[n];if(it(i[0],this,r))return it(i[1],this,r)}}))},Tr.conforms=function(e){return function(e){var t=ws(e);return function(r){return Zr(r,e,t)}}(Yr(e,1))},Tr.constant=$s,Tr.countBy=oa,Tr.create=function(e,t){var r=Rr(e);return null==t?r:Hr(r,t)},Tr.curry=function e(t,r,n){var i=Fi(t,8,void 0,void 0,void 0,void 0,void 0,r=n?void 0:r);return i.placeholder=e.placeholder,i},Tr.curryRight=function e(t,r,n){var i=Fi(t,16,void 0,void 0,void 0,void 0,void 0,r=n?void 0:r);return i.placeholder=e.placeholder,i},Tr.debounce=wa,Tr.defaults=ds,Tr.defaultsDeep=ys,Tr.defer=Sa,Tr.delay=Ea,Tr.difference=xo,Tr.differenceBy=Po,Tr.differenceWith=Oo,Tr.drop=function(e,t,r){var n=null==e?0:e.length;return n?Hn(e,(t=r||void 0===t?1:is(t))<0?0:t,n):[]},Tr.dropRight=function(e,t,r){var n=null==e?0:e.length;return n?Hn(e,0,(t=n-(t=r||void 0===t?1:is(t)))<0?0:t):[]},Tr.dropRightWhile=function(e,t){return e&&e.length?ri(e,Zi(t,3),!0,!0):[]},Tr.dropWhile=function(e,t){return e&&e.length?ri(e,Zi(t,3),!0):[]},Tr.fill=function(e,t,r,n){var i=null==e?0:e.length;return i?(r&&"number"!=typeof r&&uo(e,t,r)&&(r=0,n=i),function(e,t,r,n){var i=e.length;for((r=is(r))<0&&(r=-r>i?0:i+r),(n=void 0===n||n>i?i:is(n))<0&&(n+=i),n=r>n?0:os(n);r<n;)e[r++]=t;return e}(e,t,r,n)):[]},Tr.filter=function(e,t){return(ka(e)?ct:on)(e,Zi(t,3))},Tr.flatMap=function(e,t){return an(pa(e,t),1)},Tr.flatMapDeep=function(e,t){return an(pa(e,t),1/0)},Tr.flatMapDepth=function(e,t,r){return r=void 0===r?1:is(r),an(pa(e,t),r)},Tr.flatten=Do,Tr.flattenDeep=function(e){return(null==e?0:e.length)?an(e,1/0):[]},Tr.flattenDepth=function(e,t){return(null==e?0:e.length)?an(e,t=void 0===t?1:is(t)):[]},Tr.flip=function(e){return Fi(e,512)},Tr.flow=qs,Tr.flowRight=Hs,Tr.fromPairs=function(e){for(var t=-1,r=null==e?0:e.length,n={};++t<r;){var i=e[t];n[i[0]]=i[1]}return n},Tr.functions=function(e){return null==e?[]:ln(e,ws(e))},Tr.functionsIn=function(e){return null==e?[]:ln(e,Ss(e))},Tr.groupBy=fa,Tr.initial=function(e){return(null==e?0:e.length)?Hn(e,0,-1):[]},Tr.intersection=Mo,Tr.intersectionBy=jo,Tr.intersectionWith=Lo,Tr.invert=_s,Tr.invertBy=ms,Tr.invokeMap=la,Tr.iteratee=Gs,Tr.keyBy=ha,Tr.keys=ws,Tr.keysIn=Ss,Tr.map=pa,Tr.mapKeys=function(e,t){var r={};return t=Zi(t,3),cn(e,(function(e,n,i){Wr(r,t(e,n,i),e)})),r},Tr.mapValues=function(e,t){var r={};return t=Zi(t,3),cn(e,(function(e,n,i){Wr(r,n,t(e,n,i))})),r},Tr.matches=function(e){return Rn(Yr(e,1))},Tr.matchesProperty=function(e,t){return xn(e,Yr(t,1))},Tr.memoize=Aa,Tr.merge=Es,Tr.mergeWith=As,Tr.method=Js,Tr.methodOf=Ys,Tr.mixin=Zs,Tr.negate=Ia,Tr.nthArg=function(e){return e=is(e),Ln((function(t){return On(t,e)}))},Tr.omit=Is,Tr.omitBy=function(e,t){return Cs(e,Ia(Zi(t)))},Tr.once=function(e){return _a(2,e)},Tr.orderBy=function(e,t,r,n){return null==e?[]:(ka(t)||(t=null==t?[]:[t]),ka(r=n?void 0:r)||(r=null==r?[]:[r]),Bn(e,t,r))},Tr.over=Qs,Tr.overArgs=Na,Tr.overEvery=eu,Tr.overSome=tu,Tr.partial=Ca,Tr.partialRight=Ta,Tr.partition=da,Tr.pick=Ns,Tr.pickBy=Cs,Tr.property=ru,Tr.propertyOf=function(e){return function(t){return null==e?void 0:hn(e,t)}},Tr.pull=Ko,Tr.pullAll=zo,Tr.pullAllBy=function(e,t,r){return e&&e.length&&t&&t.length?Dn(e,t,Zi(r,2)):e},Tr.pullAllWith=function(e,t,r){return e&&e.length&&t&&t.length?Dn(e,t,void 0,r):e},Tr.pullAt=Vo,Tr.range=nu,Tr.rangeRight=iu,Tr.rearg=Ra,Tr.reject=function(e,t){return(ka(e)?ct:on)(e,Ia(Zi(t,3)))},Tr.remove=function(e,t){var r=[];if(!e||!e.length)return r;var n=-1,i=[],o=e.length;for(t=Zi(t,3);++n<o;){var a=e[n];t(a,n,e)&&(r.push(a),i.push(n))}return Un(e,i),r},Tr.rest=function(e,t){if("function"!=typeof e)throw new ve(i);return Ln(e,t=void 0===t?t:is(t))},Tr.reverse=$o,Tr.sampleSize=function(e,t,r){return t=(r?uo(e,t,r):void 0===t)?1:is(t),(ka(e)?Fr:Kn)(e,t)},Tr.set=function(e,t,r){return null==e?e:zn(e,t,r)},Tr.setWith=function(e,t,r,n){return n="function"==typeof n?n:void 0,null==e?e:zn(e,t,r,n)},Tr.shuffle=function(e){return(ka(e)?Kr:qn)(e)},Tr.slice=function(e,t,r){var n=null==e?0:e.length;return n?(r&&"number"!=typeof r&&uo(e,t,r)?(t=0,r=n):(t=null==t?0:is(t),r=void 0===r?n:is(r)),Hn(e,t,r)):[]},Tr.sortBy=ya,Tr.sortedUniq=function(e){return e&&e.length?Yn(e):[]},Tr.sortedUniqBy=function(e,t){return e&&e.length?Yn(e,Zi(t,2)):[]},Tr.split=function(e,t,r){return r&&"number"!=typeof r&&uo(e,t,r)&&(t=r=void 0),(r=void 0===r?4294967295:r>>>0)?(e=us(e))&&("string"==typeof t||null!=t&&!Ja(t))&&!(t=Xn(t))&&Lt(e)?fi(Ht(e),0,r):e.split(t,r):[]},Tr.spread=function(e,t){if("function"!=typeof e)throw new ve(i);return t=null==t?0:ar(is(t),0),Ln((function(r){var n=r[t],i=fi(r,0,t);return n&&pt(i,n),it(e,this,i)}))},Tr.tail=function(e){var t=null==e?0:e.length;return t?Hn(e,1,t):[]},Tr.take=function(e,t,r){return e&&e.length?Hn(e,0,(t=r||void 0===t?1:is(t))<0?0:t):[]},Tr.takeRight=function(e,t,r){var n=null==e?0:e.length;return n?Hn(e,(t=n-(t=r||void 0===t?1:is(t)))<0?0:t,n):[]},Tr.takeRightWhile=function(e,t){return e&&e.length?ri(e,Zi(t,3),!1,!0):[]},Tr.takeWhile=function(e,t){return e&&e.length?ri(e,Zi(t,3)):[]},Tr.tap=function(e,t){return t(e),e},Tr.throttle=function(e,t,r){var n=!0,o=!0;if("function"!=typeof e)throw new ve(i);return $a(r)&&(n="leading"in r?!!r.leading:n,o="trailing"in r?!!r.trailing:o),wa(e,t,{leading:n,maxWait:t,trailing:o})},Tr.thru=na,Tr.toArray=rs,Tr.toPairs=Ts,Tr.toPairsIn=Rs,Tr.toPath=function(e){return ka(e)?ht(e,Co):Xa(e)?[e]:_i(No(us(e)))},Tr.toPlainObject=ss,Tr.transform=function(e,t,r){var n=ka(e),i=n||ja(e)||Qa(e);if(t=Zi(t,4),null==r){var o=e&&e.constructor;r=i?n?new o:[]:$a(e)&&Ka(o)?Rr($e(e)):{}}return(i?at:cn)(e,(function(e,n,i){return t(r,e,n,i)})),r},Tr.unary=function(e){return va(e,1)},Tr.union=qo,Tr.unionBy=Ho,Tr.unionWith=Wo,Tr.uniq=function(e){return e&&e.length?Qn(e):[]},Tr.uniqBy=function(e,t){return e&&e.length?Qn(e,Zi(t,2)):[]},Tr.uniqWith=function(e,t){return t="function"==typeof t?t:void 0,e&&e.length?Qn(e,void 0,t):[]},Tr.unset=function(e,t){return null==e||ei(e,t)},Tr.unzip=Go,Tr.unzipWith=Jo,Tr.update=function(e,t,r){return null==e?e:ti(e,t,si(r))},Tr.updateWith=function(e,t,r,n){return n="function"==typeof n?n:void 0,null==e?e:ti(e,t,si(r),n)},Tr.values=xs,Tr.valuesIn=function(e){return null==e?[]:Pt(e,Ss(e))},Tr.without=Yo,Tr.words=Ks,Tr.wrap=function(e,t){return Ca(si(t),e)},Tr.xor=Zo,Tr.xorBy=Xo,Tr.xorWith=Qo,Tr.zip=ea,Tr.zipObject=function(e,t){return oi(e||[],t||[],Vr)},Tr.zipObjectDeep=function(e,t){return oi(e||[],t||[],zn)},Tr.zipWith=ta,Tr.entries=Ts,Tr.entriesIn=Rs,Tr.extend=fs,Tr.extendWith=ls,Zs(Tr,Tr),Tr.add=su,Tr.attempt=zs,Tr.camelCase=Ps,Tr.capitalize=Os,Tr.ceil=uu,Tr.clamp=function(e,t,r){return void 0===r&&(r=t,t=void 0),void 0!==r&&(r=(r=as(r))==r?r:0),void 0!==t&&(t=(t=as(t))==t?t:0),Jr(as(e),t,r)},Tr.clone=function(e){return Yr(e,4)},Tr.cloneDeep=function(e){return Yr(e,5)},Tr.cloneDeepWith=function(e,t){return Yr(e,5,t="function"==typeof t?t:void 0)},Tr.cloneWith=function(e,t){return Yr(e,4,t="function"==typeof t?t:void 0)},Tr.conformsTo=function(e,t){return null==t||Zr(e,t,ws(t))},Tr.deburr=Bs,Tr.defaultTo=function(e,t){return null==e||e!=e?t:e},Tr.divide=cu,Tr.endsWith=function(e,t,r){e=us(e),t=Xn(t);var n=e.length,i=r=void 0===r?n:Jr(is(r),0,n);return(r-=t.length)>=0&&e.slice(r,i)==t},Tr.eq=xa,Tr.escape=function(e){return(e=us(e))&&j.test(e)?e.replace(U,Mt):e},Tr.escapeRegExp=function(e){return(e=us(e))&&H.test(e)?e.replace(q,"\\$&"):e},Tr.every=function(e,t,r){var n=ka(e)?ut:rn;return r&&uo(e,t,r)&&(t=void 0),n(e,Zi(t,3))},Tr.find=aa,Tr.findIndex=Bo,Tr.findKey=function(e,t){return _t(e,Zi(t,3),cn)},Tr.findLast=sa,Tr.findLastIndex=ko,Tr.findLastKey=function(e,t){return _t(e,Zi(t,3),fn)},Tr.floor=fu,Tr.forEach=ua,Tr.forEachRight=ca,Tr.forIn=function(e,t){return null==e?e:sn(e,Zi(t,3),Ss)},Tr.forInRight=function(e,t){return null==e?e:un(e,Zi(t,3),Ss)},Tr.forOwn=function(e,t){return e&&cn(e,Zi(t,3))},Tr.forOwnRight=function(e,t){return e&&fn(e,Zi(t,3))},Tr.get=gs,Tr.gt=Pa,Tr.gte=Oa,Tr.has=function(e,t){return null!=e&&io(e,t,gn)},Tr.hasIn=vs,Tr.head=Uo,Tr.identity=Ws,Tr.includes=function(e,t,r,n){e=Ua(e)?e:xs(e),r=r&&!n?is(r):0;var i=e.length;return r<0&&(r=ar(i+r,0)),Za(e)?r<=i&&e.indexOf(t,r)>-1:!!i&&bt(e,t,r)>-1},Tr.indexOf=function(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=null==r?0:is(r);return i<0&&(i=ar(n+i,0)),bt(e,t,i)},Tr.inRange=function(e,t,r){return t=ns(t),void 0===r?(r=t,t=0):r=ns(r),function(e,t,r){return e>=sr(t,r)&&e<ar(t,r)}(e=as(e),t,r)},Tr.invoke=bs,Tr.isArguments=Ba,Tr.isArray=ka,Tr.isArrayBuffer=Da,Tr.isArrayLike=Ua,Tr.isArrayLikeObject=Ma,Tr.isBoolean=function(e){return!0===e||!1===e||qa(e)&&dn(e)==c},Tr.isBuffer=ja,Tr.isDate=La,Tr.isElement=function(e){return qa(e)&&1===e.nodeType&&!Ga(e)},Tr.isEmpty=function(e){if(null==e)return!0;if(Ua(e)&&(ka(e)||"string"==typeof e||"function"==typeof e.splice||ja(e)||Qa(e)||Ba(e)))return!e.length;var t=no(e);if(t==d||t==_)return!e.size;if(ho(e))return!In(e).length;for(var r in e)if(Ee.call(e,r))return!1;return!0},Tr.isEqual=function(e,t){return wn(e,t)},Tr.isEqualWith=function(e,t,r){var n=(r="function"==typeof r?r:void 0)?r(e,t):void 0;return void 0===n?wn(e,t,void 0,r):!!n},Tr.isError=Fa,Tr.isFinite=function(e){return"number"==typeof e&&nr(e)},Tr.isFunction=Ka,Tr.isInteger=za,Tr.isLength=Va,Tr.isMap=Ha,Tr.isMatch=function(e,t){return e===t||Sn(e,t,Qi(t))},Tr.isMatchWith=function(e,t,r){return r="function"==typeof r?r:void 0,Sn(e,t,Qi(t),r)},Tr.isNaN=function(e){return Wa(e)&&e!=+e},Tr.isNative=function(e){if(lo(e))throw new le("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return En(e)},Tr.isNil=function(e){return null==e},Tr.isNull=function(e){return null===e},Tr.isNumber=Wa,Tr.isObject=$a,Tr.isObjectLike=qa,Tr.isPlainObject=Ga,Tr.isRegExp=Ja,Tr.isSafeInteger=function(e){return za(e)&&e>=-9007199254740991&&e<=9007199254740991},Tr.isSet=Ya,Tr.isString=Za,Tr.isSymbol=Xa,Tr.isTypedArray=Qa,Tr.isUndefined=function(e){return void 0===e},Tr.isWeakMap=function(e){return qa(e)&&no(e)==w},Tr.isWeakSet=function(e){return qa(e)&&"[object WeakSet]"==dn(e)},Tr.join=function(e,t){return null==e?"":ir.call(e,t)},Tr.kebabCase=ks,Tr.last=Fo,Tr.lastIndexOf=function(e,t,r){var n=null==e?0:e.length;if(!n)return-1;var i=n;return void 0!==r&&(i=(i=is(r))<0?ar(n+i,0):sr(i,n-1)),t==t?function(e,t,r){for(var n=r+1;n--;)if(e[n]===t)return n;return n}(e,t,i):mt(e,St,i,!0)},Tr.lowerCase=Ds,Tr.lowerFirst=Us,Tr.lt=es,Tr.lte=ts,Tr.max=function(e){return e&&e.length?nn(e,Ws,yn):void 0},Tr.maxBy=function(e,t){return e&&e.length?nn(e,Zi(t,2),yn):void 0},Tr.mean=function(e){return Et(e,Ws)},Tr.meanBy=function(e,t){return Et(e,Zi(t,2))},Tr.min=function(e){return e&&e.length?nn(e,Ws,Cn):void 0},Tr.minBy=function(e,t){return e&&e.length?nn(e,Zi(t,2),Cn):void 0},Tr.stubArray=ou,Tr.stubFalse=au,Tr.stubObject=function(){return{}},Tr.stubString=function(){return""},Tr.stubTrue=function(){return!0},Tr.multiply=hu,Tr.nth=function(e,t){return e&&e.length?On(e,is(t)):void 0},Tr.noConflict=function(){return He._===this&&(He._=Te),this},Tr.noop=Xs,Tr.now=ga,Tr.pad=function(e,t,r){e=us(e);var n=(t=is(t))?qt(e):0;if(!t||n>=t)return e;var i=(t-n)/2;return Bi(er(i),r)+e+Bi(Qt(i),r)},Tr.padEnd=function(e,t,r){e=us(e);var n=(t=is(t))?qt(e):0;return t&&n<t?e+Bi(t-n,r):e},Tr.padStart=function(e,t,r){e=us(e);var n=(t=is(t))?qt(e):0;return t&&n<t?Bi(t-n,r)+e:e},Tr.parseInt=function(e,t,r){return r||null==t?t=0:t&&(t=+t),cr(us(e).replace(W,""),t||0)},Tr.random=function(e,t,r){if(r&&"boolean"!=typeof r&&uo(e,t,r)&&(t=r=void 0),void 0===r&&("boolean"==typeof t?(r=t,t=void 0):"boolean"==typeof e&&(r=e,e=void 0)),void 0===e&&void 0===t?(e=0,t=1):(e=ns(e),void 0===t?(t=e,e=0):t=ns(t)),e>t){var n=e;e=t,t=n}if(r||e%1||t%1){var i=fr();return sr(e+i*(t-e+ze("1e-"+((i+"").length-1))),t)}return Mn(e,t)},Tr.reduce=function(e,t,r){var n=ka(e)?dt:Nt,i=arguments.length<3;return n(e,Zi(t,4),r,i,en)},Tr.reduceRight=function(e,t,r){var n=ka(e)?yt:Nt,i=arguments.length<3;return n(e,Zi(t,4),r,i,tn)},Tr.repeat=function(e,t,r){return t=(r?uo(e,t,r):void 0===t)?1:is(t),jn(us(e),t)},Tr.replace=function(){var e=arguments,t=us(e[0]);return e.length<3?t:t.replace(e[1],e[2])},Tr.result=function(e,t,r){var n=-1,i=(t=ui(t,e)).length;for(i||(i=1,e=void 0);++n<i;){var o=null==e?void 0:e[Co(t[n])];void 0===o&&(n=i,o=r),e=Ka(o)?o.call(e):o}return e},Tr.round=pu,Tr.runInContext=e,Tr.sample=function(e){return(ka(e)?Lr:Fn)(e)},Tr.size=function(e){if(null==e)return 0;if(Ua(e))return Za(e)?qt(e):e.length;var t=no(e);return t==d||t==_?e.size:In(e).length},Tr.snakeCase=Ms,Tr.some=function(e,t,r){var n=ka(e)?gt:Wn;return r&&uo(e,t,r)&&(t=void 0),n(e,Zi(t,3))},Tr.sortedIndex=function(e,t){return Gn(e,t)},Tr.sortedIndexBy=function(e,t,r){return Jn(e,t,Zi(r,2))},Tr.sortedIndexOf=function(e,t){var r=null==e?0:e.length;if(r){var n=Gn(e,t);if(n<r&&xa(e[n],t))return n}return-1},Tr.sortedLastIndex=function(e,t){return Gn(e,t,!0)},Tr.sortedLastIndexBy=function(e,t,r){return Jn(e,t,Zi(r,2),!0)},Tr.sortedLastIndexOf=function(e,t){if(null==e?0:e.length){var r=Gn(e,t,!0)-1;if(xa(e[r],t))return r}return-1},Tr.startCase=js,Tr.startsWith=function(e,t,r){return e=us(e),r=null==r?0:Jr(is(r),0,e.length),t=Xn(t),e.slice(r,r+t.length)==t},Tr.subtract=du,Tr.sum=function(e){return e&&e.length?Ct(e,Ws):0},Tr.sumBy=function(e,t){return e&&e.length?Ct(e,Zi(t,2)):0},Tr.template=function(e,t,r){var n=Tr.templateSettings;r&&uo(e,t,r)&&(t=void 0),e=us(e),t=ls({},t,n,Ki);var i,o,a=ls({},t.imports,n.imports,Ki),s=ws(a),u=Pt(a,s),c=0,f=t.interpolate||ce,l="__p += '",h=ye((t.escape||ce).source+"|"+f.source+"|"+(f===K?te:ce).source+"|"+(t.evaluate||ce).source+"|$","g"),p="//# sourceURL="+(Ee.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++je+"]")+"\n";e.replace(h,(function(t,r,n,a,s,u){return n||(n=a),l+=e.slice(c,u).replace(fe,jt),r&&(i=!0,l+="' +\n__e("+r+") +\n'"),s&&(o=!0,l+="';\n"+s+";\n__p += '"),n&&(l+="' +\n((__t = ("+n+")) == null ? '' : __t) +\n'"),c=u+t.length,t})),l+="';\n";var d=Ee.call(t,"variable")&&t.variable;if(d){if(Q.test(d))throw new le("Invalid `variable` option passed into `_.template`")}else l="with (obj) {\n"+l+"\n}\n";l=(o?l.replace(O,""):l).replace(B,"$1").replace(k,"$1;"),l="function("+(d||"obj")+") {\n"+(d?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+l+"return __p\n}";var y=zs((function(){return he(s,p+"return "+l).apply(void 0,u)}));if(y.source=l,Fa(y))throw y;return y},Tr.times=function(e,t){if((e=is(e))<1||e>9007199254740991)return[];var r=4294967295,n=sr(e,4294967295);e-=4294967295;for(var i=Tt(n,t=Zi(t));++r<e;)t(r);return i},Tr.toFinite=ns,Tr.toInteger=is,Tr.toLength=os,Tr.toLower=function(e){return us(e).toLowerCase()},Tr.toNumber=as,Tr.toSafeInteger=function(e){return e?Jr(is(e),-9007199254740991,9007199254740991):0===e?e:0},Tr.toString=us,Tr.toUpper=function(e){return us(e).toUpperCase()},Tr.trim=function(e,t,r){if((e=us(e))&&(r||void 0===t))return Rt(e);if(!e||!(t=Xn(t)))return e;var n=Ht(e),i=Ht(t);return fi(n,Bt(n,i),kt(n,i)+1).join("")},Tr.trimEnd=function(e,t,r){if((e=us(e))&&(r||void 0===t))return e.slice(0,Wt(e)+1);if(!e||!(t=Xn(t)))return e;var n=Ht(e);return fi(n,0,kt(n,Ht(t))+1).join("")},Tr.trimStart=function(e,t,r){if((e=us(e))&&(r||void 0===t))return e.replace(W,"");if(!e||!(t=Xn(t)))return e;var n=Ht(e);return fi(n,Bt(n,Ht(t))).join("")},Tr.truncate=function(e,t){var r=30,n="...";if($a(t)){var i="separator"in t?t.separator:i;r="length"in t?is(t.length):r,n="omission"in t?Xn(t.omission):n}var o=(e=us(e)).length;if(Lt(e)){var a=Ht(e);o=a.length}if(r>=o)return e;var s=r-qt(n);if(s<1)return n;var u=a?fi(a,0,s).join(""):e.slice(0,s);if(void 0===i)return u+n;if(a&&(s+=u.length-s),Ja(i)){if(e.slice(s).search(i)){var c,f=u;for(i.global||(i=ye(i.source,us(re.exec(i))+"g")),i.lastIndex=0;c=i.exec(f);)var l=c.index;u=u.slice(0,void 0===l?s:l)}}else if(e.indexOf(Xn(i),s)!=s){var h=u.lastIndexOf(i);h>-1&&(u=u.slice(0,h))}return u+n},Tr.unescape=function(e){return(e=us(e))&&M.test(e)?e.replace(D,Gt):e},Tr.uniqueId=function(e){var t=++Ae;return us(e)+t},Tr.upperCase=Ls,Tr.upperFirst=Fs,Tr.each=ua,Tr.eachRight=ca,Tr.first=Uo,Zs(Tr,(lu={},cn(Tr,(function(e,t){Ee.call(Tr.prototype,t)||(lu[t]=e)})),lu),{chain:!1}),Tr.VERSION="4.17.21",at(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(e){Tr[e].placeholder=Tr})),at(["drop","take"],(function(e,t){Or.prototype[e]=function(r){r=void 0===r?1:ar(is(r),0);var n=this.__filtered__&&!t?new Or(this):this.clone();return n.__filtered__?n.__takeCount__=sr(r,n.__takeCount__):n.__views__.push({size:sr(r,4294967295),type:e+(n.__dir__<0?"Right":"")}),n},Or.prototype[e+"Right"]=function(t){return this.reverse()[e](t).reverse()}})),at(["filter","map","takeWhile"],(function(e,t){var r=t+1,n=1==r||3==r;Or.prototype[e]=function(e){var t=this.clone();return t.__iteratees__.push({iteratee:Zi(e,3),type:r}),t.__filtered__=t.__filtered__||n,t}})),at(["head","last"],(function(e,t){var r="take"+(t?"Right":"");Or.prototype[e]=function(){return this[r](1).value()[0]}})),at(["initial","tail"],(function(e,t){var r="drop"+(t?"":"Right");Or.prototype[e]=function(){return this.__filtered__?new Or(this):this[r](1)}})),Or.prototype.compact=function(){return this.filter(Ws)},Or.prototype.find=function(e){return this.filter(e).head()},Or.prototype.findLast=function(e){return this.reverse().find(e)},Or.prototype.invokeMap=Ln((function(e,t){return"function"==typeof e?new Or(this):this.map((function(r){return mn(r,e,t)}))})),Or.prototype.reject=function(e){return this.filter(Ia(Zi(e)))},Or.prototype.slice=function(e,t){e=is(e);var r=this;return r.__filtered__&&(e>0||t<0)?new Or(r):(e<0?r=r.takeRight(-e):e&&(r=r.drop(e)),void 0!==t&&(r=(t=is(t))<0?r.dropRight(-t):r.take(t-e)),r)},Or.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},Or.prototype.toArray=function(){return this.take(4294967295)},cn(Or.prototype,(function(e,t){var r=/^(?:filter|find|map|reject)|While$/.test(t),n=/^(?:head|last)$/.test(t),i=Tr[n?"take"+("last"==t?"Right":""):t],o=n||/^find/.test(t);i&&(Tr.prototype[t]=function(){var t=this.__wrapped__,a=n?[1]:arguments,s=t instanceof Or,u=a[0],c=s||ka(t),f=function(e){var t=i.apply(Tr,pt([e],a));return n&&l?t[0]:t};c&&r&&"function"==typeof u&&1!=u.length&&(s=c=!1);var l=this.__chain__,h=!!this.__actions__.length,p=o&&!l,d=s&&!h;if(!o&&c){t=d?t:new Or(this);var y=e.apply(t,a);return y.__actions__.push({func:na,args:[f],thisArg:void 0}),new Pr(y,l)}return p&&d?e.apply(this,a):(y=this.thru(f),p?n?y.value()[0]:y.value():y)})})),at(["pop","push","shift","sort","splice","unshift"],(function(e){var t=_e[e],r=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",n=/^(?:pop|shift)$/.test(e);Tr.prototype[e]=function(){var e=arguments;if(n&&!this.__chain__){var i=this.value();return t.apply(ka(i)?i:[],e)}return this[r]((function(r){return t.apply(ka(r)?r:[],e)}))}})),cn(Or.prototype,(function(e,t){var r=Tr[t];if(r){var n=r.name+"";Ee.call(mr,n)||(mr[n]=[]),mr[n].push({name:t,func:r})}})),mr[Ri(void 0,2).name]=[{name:"wrapper",func:void 0}],Or.prototype.clone=function(){var e=new Or(this.__wrapped__);return e.__actions__=_i(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=_i(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=_i(this.__views__),e},Or.prototype.reverse=function(){if(this.__filtered__){var e=new Or(this);e.__dir__=-1,e.__filtered__=!0}else(e=this.clone()).__dir__*=-1;return e},Or.prototype.value=function(){var e=this.__wrapped__.value(),t=this.__dir__,r=ka(e),n=t<0,i=r?e.length:0,o=function(e,t,r){var n=-1,i=r.length;for(;++n<i;){var o=r[n],a=o.size;switch(o.type){case"drop":e+=a;break;case"dropRight":t-=a;break;case"take":t=sr(t,e+a);break;case"takeRight":e=ar(e,t-a)}}return{start:e,end:t}}(0,i,this.__views__),a=o.start,s=o.end,u=s-a,c=n?s:a-1,f=this.__iteratees__,l=f.length,h=0,p=sr(u,this.__takeCount__);if(!r||!n&&i==u&&p==u)return ni(e,this.__actions__);var d=[];e:for(;u--&&h<p;){for(var y=-1,g=e[c+=t];++y<l;){var v=f[y],_=v.iteratee,m=v.type,b=_(g);if(2==m)g=b;else if(!b){if(1==m)continue e;break e}}d[h++]=g}return d},Tr.prototype.at=ia,Tr.prototype.chain=function(){return ra(this)},Tr.prototype.commit=function(){return new Pr(this.value(),this.__chain__)},Tr.prototype.next=function(){void 0===this.__values__&&(this.__values__=rs(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?void 0:this.__values__[this.__index__++]}},Tr.prototype.plant=function(e){for(var t,r=this;r instanceof xr;){var n=Ro(r);n.__index__=0,n.__values__=void 0,t?i.__wrapped__=n:t=n;var i=n;r=r.__wrapped__}return i.__wrapped__=e,t},Tr.prototype.reverse=function(){var e=this.__wrapped__;if(e instanceof Or){var t=e;return this.__actions__.length&&(t=new Or(this)),(t=t.reverse()).__actions__.push({func:na,args:[$o],thisArg:void 0}),new Pr(t,this.__chain__)}return this.thru($o)},Tr.prototype.toJSON=Tr.prototype.valueOf=Tr.prototype.value=function(){return ni(this.__wrapped__,this.__actions__)},Tr.prototype.first=Tr.prototype.head,Ze&&(Tr.prototype[Ze]=function(){return this}),Tr}();He._=Jt,void 0===(n=function(){return Jt}.call(t,r,t,e))||(e.exports=n)}).call(this)}).call(this,r(73)(e))},function(e,t){e.exports=function(e){return e.webpackPolyfill||(e.deprecate=function(){},e.paths=[],e.children||(e.children=[]),Object.defineProperty(e,"loaded",{enumerable:!0,get:function(){return e.l}}),Object.defineProperty(e,"id",{enumerable:!0,get:function(){return e.i}}),e.webpackPolyfill=1),e}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(10),i=r(75),o=r(2),a=r(19);function s(e,t=!1){const r="string"==typeof e?Buffer.from(e):e;if(t)return function(e){const t=a.Certificate.fromPEMs(e);let r="";return t.forEach(e=>{if(e.signatureOID.startsWith("1.2.840.113549.1.1")){const t=u(e);0===r.length?r+=t:r+="_"+t}}),r}(r);return u(a.Certificate.fromPEM(r))}function u(e){const{issuer:t,serialNumber:r}=e,n=t.attributes.reduceRight((e,t)=>{const{shortName:r,value:n}=t;return`${e}${r}=${n},`},"").slice(0,-1),a=new i.default(r,16).toString(10);return o.createHash("md5").update(n+a,"utf8").digest("hex")}t.loadPublicKeyFromPath=function(e){const t=n.readFileSync(e);return a.Certificate.fromPEM(t).publicKeyRaw.toString("base64")},t.loadPublicKey=function(e){const t="string"==typeof e?Buffer.from(e):e;return a.Certificate.fromPEM(t).publicKeyRaw.toString("base64")},t.getSNFromPath=function(e,t=!1){return s(n.readFileSync(e),t)},t.getSN=s},function(e,t,r){"use strict";r.r(t),r.d(t,"BigNumber",(function(){return _}));var n=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,i=Math.ceil,o=Math.floor,a="[BigNumber Error] ",s=a+"Number primitive has more than 15 significant digits: ",u=1e14,c=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],f=1e9;function l(e){var t=0|e;return e>0||e===t?t:t-1}function h(e){for(var t,r,n=1,i=e.length,o=e[0]+"";n<i;){for(r=14-(t=e[n++]+"").length;r--;t="0"+t);o+=t}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function p(e,t){var r,n,i=e.c,o=t.c,a=e.s,s=t.s,u=e.e,c=t.e;if(!a||!s)return null;if(r=i&&!i[0],n=o&&!o[0],r||n)return r?n?0:-s:a;if(a!=s)return a;if(r=a<0,n=u==c,!i||!o)return n?0:!i^r?1:-1;if(!n)return u>c^r?1:-1;for(s=(u=i.length)<(c=o.length)?u:c,a=0;a<s;a++)if(i[a]!=o[a])return i[a]>o[a]^r?1:-1;return u==c?0:u>c^r?1:-1}function d(e,t,r,n){if(e<t||e>r||e!==o(e))throw Error(a+(n||"Argument")+("number"==typeof e?e<t||e>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function y(e){var t=e.c.length-1;return l(e.e/14)==t&&e.c[t]%2!=0}function g(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function v(e,t,r){var n,i;if(t<0){for(i=r+".";++t;i+=r);e=i+e}else if(++t>(n=e.length)){for(i=r,t-=n;--t;i+=r);e+=i}else t<n&&(e=e.slice(0,t)+"."+e.slice(t));return e}var _=function e(t){var r,_,m,b,w,S,E,A,I,N=F.prototype={constructor:F,toString:null,valueOf:null},C=new F(1),T=20,R=4,x=-7,P=21,O=-1e7,B=1e7,k=!1,D=1,U=0,M={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},j="0123456789abcdefghijklmnopqrstuvwxyz",L=!0;function F(e,t){var r,i,a,u,c,f,l,h,p=this;if(!(p instanceof F))return new F(e,t);if(null==t){if(e&&!0===e._isBigNumber)return p.s=e.s,void(!e.c||e.e>B?p.c=p.e=null:e.e<O?p.c=[p.e=0]:(p.e=e.e,p.c=e.c.slice()));if((f="number"==typeof e)&&0*e==0){if(p.s=1/e<0?(e=-e,-1):1,e===~~e){for(u=0,c=e;c>=10;c/=10,u++);return void(u>B?p.c=p.e=null:(p.e=u,p.c=[e]))}h=String(e)}else{if(!n.test(h=String(e)))return m(p,h,f);p.s=45==h.charCodeAt(0)?(h=h.slice(1),-1):1}(u=h.indexOf("."))>-1&&(h=h.replace(".","")),(c=h.search(/e/i))>0?(u<0&&(u=c),u+=+h.slice(c+1),h=h.substring(0,c)):u<0&&(u=h.length)}else{if(d(t,2,j.length,"Base"),10==t&&L)return $(p=new F(e),T+p.e+1,R);if(h=String(e),f="number"==typeof e){if(0*e!=0)return m(p,h,f,t);if(p.s=1/e<0?(h=h.slice(1),-1):1,F.DEBUG&&h.replace(/^0\.0*|\./,"").length>15)throw Error(s+e)}else p.s=45===h.charCodeAt(0)?(h=h.slice(1),-1):1;for(r=j.slice(0,t),u=c=0,l=h.length;c<l;c++)if(r.indexOf(i=h.charAt(c))<0){if("."==i){if(c>u){u=l;continue}}else if(!a&&(h==h.toUpperCase()&&(h=h.toLowerCase())||h==h.toLowerCase()&&(h=h.toUpperCase()))){a=!0,c=-1,u=0;continue}return m(p,String(e),f,t)}f=!1,(u=(h=_(h,t,10,p.s)).indexOf("."))>-1?h=h.replace(".",""):u=h.length}for(c=0;48===h.charCodeAt(c);c++);for(l=h.length;48===h.charCodeAt(--l););if(h=h.slice(c,++l)){if(l-=c,f&&F.DEBUG&&l>15&&(e>9007199254740991||e!==o(e)))throw Error(s+p.s*e);if((u=u-c-1)>B)p.c=p.e=null;else if(u<O)p.c=[p.e=0];else{if(p.e=u,p.c=[],c=(u+1)%14,u<0&&(c+=14),c<l){for(c&&p.c.push(+h.slice(0,c)),l-=14;c<l;)p.c.push(+h.slice(c,c+=14));c=14-(h=h.slice(c)).length}else c-=l;for(;c--;h+="0");p.c.push(+h)}}else p.c=[p.e=0]}function K(e,t,r,n){var i,o,a,s,u;if(null==r?r=R:d(r,0,8),!e.c)return e.toString();if(i=e.c[0],a=e.e,null==t)u=h(e.c),u=1==n||2==n&&(a<=x||a>=P)?g(u,a):v(u,a,"0");else if(o=(e=$(new F(e),t,r)).e,s=(u=h(e.c)).length,1==n||2==n&&(t<=o||o<=x)){for(;s<t;u+="0",s++);u=g(u,o)}else if(t-=a,u=v(u,o,"0"),o+1>s){if(--t>0)for(u+=".";t--;u+="0");}else if((t+=o-s)>0)for(o+1==s&&(u+=".");t--;u+="0");return e.s<0&&i?"-"+u:u}function z(e,t){for(var r,n=1,i=new F(e[0]);n<e.length;n++){if(!(r=new F(e[n])).s){i=r;break}t.call(i,r)&&(i=r)}return i}function V(e,t,r){for(var n=1,i=t.length;!t[--i];t.pop());for(i=t[0];i>=10;i/=10,n++);return(r=n+14*r-1)>B?e.c=e.e=null:r<O?e.c=[e.e=0]:(e.e=r,e.c=t),e}function $(e,t,r,n){var a,s,f,l,h,p,d,y=e.c,g=c;if(y){e:{for(a=1,l=y[0];l>=10;l/=10,a++);if((s=t-a)<0)s+=14,f=t,d=(h=y[p=0])/g[a-f-1]%10|0;else if((p=i((s+1)/14))>=y.length){if(!n)break e;for(;y.length<=p;y.push(0));h=d=0,a=1,f=(s%=14)-14+1}else{for(h=l=y[p],a=1;l>=10;l/=10,a++);d=(f=(s%=14)-14+a)<0?0:h/g[a-f-1]%10|0}if(n=n||t<0||null!=y[p+1]||(f<0?h:h%g[a-f-1]),n=r<4?(d||n)&&(0==r||r==(e.s<0?3:2)):d>5||5==d&&(4==r||n||6==r&&(s>0?f>0?h/g[a-f]:0:y[p-1])%10&1||r==(e.s<0?8:7)),t<1||!y[0])return y.length=0,n?(t-=e.e+1,y[0]=g[(14-t%14)%14],e.e=-t||0):y[0]=e.e=0,e;if(0==s?(y.length=p,l=1,p--):(y.length=p+1,l=g[14-s],y[p]=f>0?o(h/g[a-f]%g[f])*l:0),n)for(;;){if(0==p){for(s=1,f=y[0];f>=10;f/=10,s++);for(f=y[0]+=l,l=1;f>=10;f/=10,l++);s!=l&&(e.e++,y[0]==u&&(y[0]=1));break}if(y[p]+=l,y[p]!=u)break;y[p--]=0,l=1}for(s=y.length;0===y[--s];y.pop());}e.e>B?e.c=e.e=null:e.e<O&&(e.c=[e.e=0])}return e}function q(e){var t,r=e.e;return null===r?e.toString():(t=h(e.c),t=r<=x||r>=P?g(t,r):v(t,r,"0"),e.s<0?"-"+t:t)}return F.clone=e,F.ROUND_UP=0,F.ROUND_DOWN=1,F.ROUND_CEIL=2,F.ROUND_FLOOR=3,F.ROUND_HALF_UP=4,F.ROUND_HALF_DOWN=5,F.ROUND_HALF_EVEN=6,F.ROUND_HALF_CEIL=7,F.ROUND_HALF_FLOOR=8,F.EUCLID=9,F.config=F.set=function(e){var t,r;if(null!=e){if("object"!=typeof e)throw Error(a+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(d(r=e[t],0,f,t),T=r),e.hasOwnProperty(t="ROUNDING_MODE")&&(d(r=e[t],0,8,t),R=r),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((r=e[t])&&r.pop?(d(r[0],-f,0,t),d(r[1],0,f,t),x=r[0],P=r[1]):(d(r,-f,f,t),x=-(P=r<0?-r:r))),e.hasOwnProperty(t="RANGE"))if((r=e[t])&&r.pop)d(r[0],-f,-1,t),d(r[1],1,f,t),O=r[0],B=r[1];else{if(d(r,-f,f,t),!r)throw Error(a+t+" cannot be zero: "+r);O=-(B=r<0?-r:r)}if(e.hasOwnProperty(t="CRYPTO")){if((r=e[t])!==!!r)throw Error(a+t+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw k=!r,Error(a+"crypto unavailable");k=r}else k=r}if(e.hasOwnProperty(t="MODULO_MODE")&&(d(r=e[t],0,9,t),D=r),e.hasOwnProperty(t="POW_PRECISION")&&(d(r=e[t],0,f,t),U=r),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(r=e[t]))throw Error(a+t+" not an object: "+r);M=r}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(r=e[t])||/^.?$|[+\-.\s]|(.).*\1/.test(r))throw Error(a+t+" invalid: "+r);L="0123456789"==r.slice(0,10),j=r}}return{DECIMAL_PLACES:T,ROUNDING_MODE:R,EXPONENTIAL_AT:[x,P],RANGE:[O,B],CRYPTO:k,MODULO_MODE:D,POW_PRECISION:U,FORMAT:M,ALPHABET:j}},F.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!F.DEBUG)return!0;var t,r,n=e.c,i=e.e,s=e.s;e:if("[object Array]"=={}.toString.call(n)){if((1===s||-1===s)&&i>=-f&&i<=f&&i===o(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break e}if((t=(i+1)%14)<1&&(t+=14),String(n[0]).length==t){for(t=0;t<n.length;t++)if((r=n[t])<0||r>=u||r!==o(r))break e;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===s||1===s||-1===s))return!0;throw Error(a+"Invalid BigNumber: "+e)},F.maximum=F.max=function(){return z(arguments,N.lt)},F.minimum=F.min=function(){return z(arguments,N.gt)},F.random=(b=9007199254740992*Math.random()&2097151?function(){return o(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,r,n,s,u,l=0,h=[],p=new F(C);if(null==e?e=T:d(e,0,f),s=i(e/14),k)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(s*=2));l<s;)(u=131072*t[l]+(t[l+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),t[l]=r[0],t[l+1]=r[1]):(h.push(u%1e14),l+=2);l=s/2}else{if(!crypto.randomBytes)throw k=!1,Error(a+"crypto unavailable");for(t=crypto.randomBytes(s*=7);l<s;)(u=281474976710656*(31&t[l])+1099511627776*t[l+1]+4294967296*t[l+2]+16777216*t[l+3]+(t[l+4]<<16)+(t[l+5]<<8)+t[l+6])>=9e15?crypto.randomBytes(7).copy(t,l):(h.push(u%1e14),l+=7);l=s/7}if(!k)for(;l<s;)(u=b())<9e15&&(h[l++]=u%1e14);for(e%=14,(s=h[--l])&&e&&(u=c[14-e],h[l]=o(s/u)*u);0===h[l];h.pop(),l--);if(l<0)h=[n=0];else{for(n=-1;0===h[0];h.splice(0,1),n-=14);for(l=1,u=h[0];u>=10;u/=10,l++);l<14&&(n-=14-l)}return p.e=n,p.c=h,p}),F.sum=function(){for(var e=1,t=arguments,r=new F(t[0]);e<t.length;)r=r.plus(t[e++]);return r},_=function(){function e(e,t,r,n){for(var i,o,a=[0],s=0,u=e.length;s<u;){for(o=a.length;o--;a[o]*=t);for(a[0]+=n.indexOf(e.charAt(s++)),i=0;i<a.length;i++)a[i]>r-1&&(null==a[i+1]&&(a[i+1]=0),a[i+1]+=a[i]/r|0,a[i]%=r)}return a.reverse()}return function(t,n,i,o,a){var s,u,c,f,l,p,d,y,g=t.indexOf("."),_=T,m=R;for(g>=0&&(f=U,U=0,t=t.replace(".",""),p=(y=new F(n)).pow(t.length-g),U=f,y.c=e(v(h(p.c),p.e,"0"),10,i,"0123456789"),y.e=y.c.length),c=f=(d=e(t,n,i,a?(s=j,"0123456789"):(s="0123456789",j))).length;0==d[--f];d.pop());if(!d[0])return s.charAt(0);if(g<0?--c:(p.c=d,p.e=c,p.s=o,d=(p=r(p,y,_,m,i)).c,l=p.r,c=p.e),g=d[u=c+_+1],f=i/2,l=l||u<0||null!=d[u+1],l=m<4?(null!=g||l)&&(0==m||m==(p.s<0?3:2)):g>f||g==f&&(4==m||l||6==m&&1&d[u-1]||m==(p.s<0?8:7)),u<1||!d[0])t=l?v(s.charAt(1),-_,s.charAt(0)):s.charAt(0);else{if(d.length=u,l)for(--i;++d[--u]>i;)d[u]=0,u||(++c,d=[1].concat(d));for(f=d.length;!d[--f];);for(g=0,t="";g<=f;t+=s.charAt(d[g++]));t=v(t,c,s.charAt(0))}return t}}(),r=function(){function e(e,t,r){var n,i,o,a,s=0,u=e.length,c=t%1e7,f=t/1e7|0;for(e=e.slice();u--;)s=((i=c*(o=e[u]%1e7)+(n=f*o+(a=e[u]/1e7|0)*c)%1e7*1e7+s)/r|0)+(n/1e7|0)+f*a,e[u]=i%r;return s&&(e=[s].concat(e)),e}function t(e,t,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(e[i]!=t[i]){o=e[i]>t[i]?1:-1;break}return o}function r(e,t,r,n){for(var i=0;r--;)e[r]-=i,i=e[r]<t[r]?1:0,e[r]=i*n+e[r]-t[r];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(n,i,a,s,c){var f,h,p,d,y,g,v,_,m,b,w,S,E,A,I,N,C,T=n.s==i.s?1:-1,R=n.c,x=i.c;if(!(R&&R[0]&&x&&x[0]))return new F(n.s&&i.s&&(R?!x||R[0]!=x[0]:x)?R&&0==R[0]||!x?0*T:T/0:NaN);for(m=(_=new F(T)).c=[],T=a+(h=n.e-i.e)+1,c||(c=u,h=l(n.e/14)-l(i.e/14),T=T/14|0),p=0;x[p]==(R[p]||0);p++);if(x[p]>(R[p]||0)&&h--,T<0)m.push(1),d=!0;else{for(A=R.length,N=x.length,p=0,T+=2,(y=o(c/(x[0]+1)))>1&&(x=e(x,y,c),R=e(R,y,c),N=x.length,A=R.length),E=N,w=(b=R.slice(0,N)).length;w<N;b[w++]=0);C=x.slice(),C=[0].concat(C),I=x[0],x[1]>=c/2&&I++;do{if(y=0,(f=t(x,b,N,w))<0){if(S=b[0],N!=w&&(S=S*c+(b[1]||0)),(y=o(S/I))>1)for(y>=c&&(y=c-1),v=(g=e(x,y,c)).length,w=b.length;1==t(g,b,v,w);)y--,r(g,N<v?C:x,v,c),v=g.length,f=1;else 0==y&&(f=y=1),v=(g=x.slice()).length;if(v<w&&(g=[0].concat(g)),r(b,g,w,c),w=b.length,-1==f)for(;t(x,b,N,w)<1;)y++,r(b,N<w?C:x,w,c),w=b.length}else 0===f&&(y++,b=[0]);m[p++]=y,b[0]?b[w++]=R[E]||0:(b=[R[E]],w=1)}while((E++<A||null!=b[0])&&T--);d=null!=b[0],m[0]||m.splice(0,1)}if(c==u){for(p=1,T=m[0];T>=10;T/=10,p++);$(_,a+(_.e=p+14*h-1)+1,s,d)}else _.e=h,_.r=+d;return _}}(),w=/^(-?)0([xbo])(?=\w[\w.]*$)/i,S=/^([^.]+)\.$/,E=/^\.([^.]+)$/,A=/^-?(Infinity|NaN)$/,I=/^\s*\+(?=[\w.])|^\s+|\s+$/g,m=function(e,t,r,n){var i,o=r?t:t.replace(I,"");if(A.test(o))e.s=isNaN(o)?null:o<0?-1:1;else{if(!r&&(o=o.replace(w,(function(e,t,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?e:t})),n&&(i=n,o=o.replace(S,"$1").replace(E,"0.$1")),t!=o))return new F(o,i);if(F.DEBUG)throw Error(a+"Not a"+(n?" base "+n:"")+" number: "+t);e.s=null}e.c=e.e=null},N.absoluteValue=N.abs=function(){var e=new F(this);return e.s<0&&(e.s=1),e},N.comparedTo=function(e,t){return p(this,new F(e,t))},N.decimalPlaces=N.dp=function(e,t){var r,n,i,o=this;if(null!=e)return d(e,0,f),null==t?t=R:d(t,0,8),$(new F(o),e+o.e+1,t);if(!(r=o.c))return null;if(n=14*((i=r.length-1)-l(this.e/14)),i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},N.dividedBy=N.div=function(e,t){return r(this,new F(e,t),T,R)},N.dividedToIntegerBy=N.idiv=function(e,t){return r(this,new F(e,t),0,1)},N.exponentiatedBy=N.pow=function(e,t){var r,n,s,u,c,f,l,h,p=this;if((e=new F(e)).c&&!e.isInteger())throw Error(a+"Exponent not an integer: "+q(e));if(null!=t&&(t=new F(t)),c=e.e>14,!p.c||!p.c[0]||1==p.c[0]&&!p.e&&1==p.c.length||!e.c||!e.c[0])return h=new F(Math.pow(+q(p),c?2-y(e):+q(e))),t?h.mod(t):h;if(f=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new F(NaN);(n=!f&&p.isInteger()&&t.isInteger())&&(p=p.mod(t))}else{if(e.e>9&&(p.e>0||p.e<-1||(0==p.e?p.c[0]>1||c&&p.c[1]>=24e7:p.c[0]<8e13||c&&p.c[0]<=9999975e7)))return u=p.s<0&&y(e)?-0:0,p.e>-1&&(u=1/u),new F(f?1/u:u);U&&(u=i(U/14+2))}for(c?(r=new F(.5),f&&(e.s=1),l=y(e)):l=(s=Math.abs(+q(e)))%2,h=new F(C);;){if(l){if(!(h=h.times(p)).c)break;u?h.c.length>u&&(h.c.length=u):n&&(h=h.mod(t))}if(s){if(0===(s=o(s/2)))break;l=s%2}else if($(e=e.times(r),e.e+1,1),e.e>14)l=y(e);else{if(0===(s=+q(e)))break;l=s%2}p=p.times(p),u?p.c&&p.c.length>u&&(p.c.length=u):n&&(p=p.mod(t))}return n?h:(f&&(h=C.div(h)),t?h.mod(t):u?$(h,U,R,void 0):h)},N.integerValue=function(e){var t=new F(this);return null==e?e=R:d(e,0,8),$(t,t.e+1,e)},N.isEqualTo=N.eq=function(e,t){return 0===p(this,new F(e,t))},N.isFinite=function(){return!!this.c},N.isGreaterThan=N.gt=function(e,t){return p(this,new F(e,t))>0},N.isGreaterThanOrEqualTo=N.gte=function(e,t){return 1===(t=p(this,new F(e,t)))||0===t},N.isInteger=function(){return!!this.c&&l(this.e/14)>this.c.length-2},N.isLessThan=N.lt=function(e,t){return p(this,new F(e,t))<0},N.isLessThanOrEqualTo=N.lte=function(e,t){return-1===(t=p(this,new F(e,t)))||0===t},N.isNaN=function(){return!this.s},N.isNegative=function(){return this.s<0},N.isPositive=function(){return this.s>0},N.isZero=function(){return!!this.c&&0==this.c[0]},N.minus=function(e,t){var r,n,i,o,a=this,s=a.s;if(t=(e=new F(e,t)).s,!s||!t)return new F(NaN);if(s!=t)return e.s=-t,a.plus(e);var c=a.e/14,f=e.e/14,h=a.c,p=e.c;if(!c||!f){if(!h||!p)return h?(e.s=-t,e):new F(p?a:NaN);if(!h[0]||!p[0])return p[0]?(e.s=-t,e):new F(h[0]?a:3==R?-0:0)}if(c=l(c),f=l(f),h=h.slice(),s=c-f){for((o=s<0)?(s=-s,i=h):(f=c,i=p),i.reverse(),t=s;t--;i.push(0));i.reverse()}else for(n=(o=(s=h.length)<(t=p.length))?s:t,s=t=0;t<n;t++)if(h[t]!=p[t]){o=h[t]<p[t];break}if(o&&(i=h,h=p,p=i,e.s=-e.s),(t=(n=p.length)-(r=h.length))>0)for(;t--;h[r++]=0);for(t=u-1;n>s;){if(h[--n]<p[n]){for(r=n;r&&!h[--r];h[r]=t);--h[r],h[n]+=u}h[n]-=p[n]}for(;0==h[0];h.splice(0,1),--f);return h[0]?V(e,h,f):(e.s=3==R?-1:1,e.c=[e.e=0],e)},N.modulo=N.mod=function(e,t){var n,i,o=this;return e=new F(e,t),!o.c||!e.s||e.c&&!e.c[0]?new F(NaN):!e.c||o.c&&!o.c[0]?new F(o):(9==D?(i=e.s,e.s=1,n=r(o,e,0,3),e.s=i,n.s*=i):n=r(o,e,0,D),(e=o.minus(n.times(e))).c[0]||1!=D||(e.s=o.s),e)},N.multipliedBy=N.times=function(e,t){var r,n,i,o,a,s,c,f,h,p,d,y,g,v,_=this,m=_.c,b=(e=new F(e,t)).c;if(!(m&&b&&m[0]&&b[0]))return!_.s||!e.s||m&&!m[0]&&!b||b&&!b[0]&&!m?e.c=e.e=e.s=null:(e.s*=_.s,m&&b?(e.c=[0],e.e=0):e.c=e.e=null),e;for(n=l(_.e/14)+l(e.e/14),e.s*=_.s,(c=m.length)<(p=b.length)&&(g=m,m=b,b=g,i=c,c=p,p=i),i=c+p,g=[];i--;g.push(0));for(v=u,1e7,i=p;--i>=0;){for(r=0,d=b[i]%1e7,y=b[i]/1e7|0,o=i+(a=c);o>i;)r=((f=d*(f=m[--a]%1e7)+(s=y*f+(h=m[a]/1e7|0)*d)%1e7*1e7+g[o]+r)/v|0)+(s/1e7|0)+y*h,g[o--]=f%v;g[o]=r}return r?++n:g.splice(0,1),V(e,g,n)},N.negated=function(){var e=new F(this);return e.s=-e.s||null,e},N.plus=function(e,t){var r,n=this,i=n.s;if(t=(e=new F(e,t)).s,!i||!t)return new F(NaN);if(i!=t)return e.s=-t,n.minus(e);var o=n.e/14,a=e.e/14,s=n.c,c=e.c;if(!o||!a){if(!s||!c)return new F(i/0);if(!s[0]||!c[0])return c[0]?e:new F(s[0]?n:0*i)}if(o=l(o),a=l(a),s=s.slice(),i=o-a){for(i>0?(a=o,r=c):(i=-i,r=s),r.reverse();i--;r.push(0));r.reverse()}for((i=s.length)-(t=c.length)<0&&(r=c,c=s,s=r,t=i),i=0;t;)i=(s[--t]=s[t]+c[t]+i)/u|0,s[t]=u===s[t]?0:s[t]%u;return i&&(s=[i].concat(s),++a),V(e,s,a)},N.precision=N.sd=function(e,t){var r,n,i,o=this;if(null!=e&&e!==!!e)return d(e,1,f),null==t?t=R:d(t,0,8),$(new F(o),e,t);if(!(r=o.c))return null;if(n=14*(i=r.length-1)+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return e&&o.e+1>n&&(n=o.e+1),n},N.shiftedBy=function(e){return d(e,-9007199254740991,9007199254740991),this.times("1e"+e)},N.squareRoot=N.sqrt=function(){var e,t,n,i,o,a=this,s=a.c,u=a.s,c=a.e,f=T+4,p=new F("0.5");if(1!==u||!s||!s[0])return new F(!u||u<0&&(!s||s[0])?NaN:s?a:1/0);if(0==(u=Math.sqrt(+q(a)))||u==1/0?(((t=h(s)).length+c)%2==0&&(t+="0"),u=Math.sqrt(+t),c=l((c+1)/2)-(c<0||c%2),n=new F(t=u==1/0?"5e"+c:(t=u.toExponential()).slice(0,t.indexOf("e")+1)+c)):n=new F(u+""),n.c[0])for((u=(c=n.e)+f)<3&&(u=0);;)if(o=n,n=p.times(o.plus(r(a,o,f,1))),h(o.c).slice(0,u)===(t=h(n.c)).slice(0,u)){if(n.e<c&&--u,"9999"!=(t=t.slice(u-3,u+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||($(n,n.e+T+2,1),e=!n.times(n).eq(a));break}if(!i&&($(o,o.e+T+2,0),o.times(o).eq(a))){n=o;break}f+=4,u+=4,i=1}return $(n,n.e+T+1,R,e)},N.toExponential=function(e,t){return null!=e&&(d(e,0,f),e++),K(this,e,t,1)},N.toFixed=function(e,t){return null!=e&&(d(e,0,f),e=e+this.e+1),K(this,e,t)},N.toFormat=function(e,t,r){var n,i=this;if(null==r)null!=e&&t&&"object"==typeof t?(r=t,t=null):e&&"object"==typeof e?(r=e,e=t=null):r=M;else if("object"!=typeof r)throw Error(a+"Argument not an object: "+r);if(n=i.toFixed(e,t),i.c){var o,s=n.split("."),u=+r.groupSize,c=+r.secondaryGroupSize,f=r.groupSeparator||"",l=s[0],h=s[1],p=i.s<0,d=p?l.slice(1):l,y=d.length;if(c&&(o=u,u=c,c=o,y-=o),u>0&&y>0){for(o=y%u||u,l=d.substr(0,o);o<y;o+=u)l+=f+d.substr(o,u);c>0&&(l+=f+d.slice(o)),p&&(l="-"+l)}n=h?l+(r.decimalSeparator||"")+((c=+r.fractionGroupSize)?h.replace(new RegExp("\\d{"+c+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):h):l}return(r.prefix||"")+n+(r.suffix||"")},N.toFraction=function(e){var t,n,i,o,s,u,f,l,p,d,y,g,v=this,_=v.c;if(null!=e&&(!(f=new F(e)).isInteger()&&(f.c||1!==f.s)||f.lt(C)))throw Error(a+"Argument "+(f.isInteger()?"out of range: ":"not an integer: ")+q(f));if(!_)return new F(v);for(t=new F(C),p=n=new F(C),i=l=new F(C),g=h(_),s=t.e=g.length-v.e-1,t.c[0]=c[(u=s%14)<0?14+u:u],e=!e||f.comparedTo(t)>0?s>0?t:p:f,u=B,B=1/0,f=new F(g),l.c[0]=0;d=r(f,t,0,1),1!=(o=n.plus(d.times(i))).comparedTo(e);)n=i,i=o,p=l.plus(d.times(o=p)),l=o,t=f.minus(d.times(o=t)),f=o;return o=r(e.minus(n),i,0,1),l=l.plus(o.times(p)),n=n.plus(o.times(i)),l.s=p.s=v.s,y=r(p,i,s*=2,R).minus(v).abs().comparedTo(r(l,n,s,R).minus(v).abs())<1?[p,i]:[l,n],B=u,y},N.toNumber=function(){return+q(this)},N.toPrecision=function(e,t){return null!=e&&d(e,1,f),K(this,e,t,2)},N.toString=function(e){var t,r=this,n=r.s,i=r.e;return null===i?n?(t="Infinity",n<0&&(t="-"+t)):t="NaN":(null==e?t=i<=x||i>=P?g(h(r.c),i):v(h(r.c),i,"0"):10===e&&L?t=v(h((r=$(new F(r),T+i+1,R)).c),r.e,"0"):(d(e,2,j.length,"Base"),t=_(v(h(r.c),i,"0"),10,e,n,!0)),n<0&&r.c[0]&&(t="-"+t)),t},N.valueOf=N.toJSON=function(){return q(this)},N._isBigNumber=!0,N[Symbol.toStringTag]="BigNumber",N[Symbol.for("nodejs.util.inspect.custom")]=N.valueOf,null!=t&&F.set(t),F}();t.default=_},function(e,t){e.exports=require("net")},function(e,t,r){!function(e){"use strict";var t=function(e){var t,r=new Float64Array(16);if(e)for(t=0;t<e.length;t++)r[t]=e[t];return r},n=function(){throw new Error("no PRNG")},i=new Uint8Array(16),o=new Uint8Array(32);o[0]=9;var a=t(),s=t([1]),u=t([56129,1]),c=t([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),f=t([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),l=t([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),h=t([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),p=t([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function d(e,t,r,n){e[t]=r>>24&255,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=255&r,e[t+4]=n>>24&255,e[t+5]=n>>16&255,e[t+6]=n>>8&255,e[t+7]=255&n}function y(e,t,r,n,i){var o,a=0;for(o=0;o<i;o++)a|=e[t+o]^r[n+o];return(1&a-1>>>8)-1}function g(e,t,r,n){return y(e,t,r,n,16)}function v(e,t,r,n){return y(e,t,r,n,32)}function _(e,t,r,n){!function(e,t,r,n){for(var i,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,s=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,u=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,c=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,f=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,l=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,p=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,d=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,y=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,g=255&r[16]|(255&r[17])<<8|(255&r[18])<<16|(255&r[19])<<24,v=255&r[20]|(255&r[21])<<8|(255&r[22])<<16|(255&r[23])<<24,_=255&r[24]|(255&r[25])<<8|(255&r[26])<<16|(255&r[27])<<24,m=255&r[28]|(255&r[29])<<8|(255&r[30])<<16|(255&r[31])<<24,b=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,w=o,S=a,E=s,A=u,I=c,N=f,C=l,T=h,R=p,x=d,P=y,O=g,B=v,k=_,D=m,U=b,M=0;M<20;M+=2)w^=(i=(B^=(i=(R^=(i=(I^=(i=w+B|0)<<7|i>>>25)+w|0)<<9|i>>>23)+I|0)<<13|i>>>19)+R|0)<<18|i>>>14,N^=(i=(S^=(i=(k^=(i=(x^=(i=N+S|0)<<7|i>>>25)+N|0)<<9|i>>>23)+x|0)<<13|i>>>19)+k|0)<<18|i>>>14,P^=(i=(C^=(i=(E^=(i=(D^=(i=P+C|0)<<7|i>>>25)+P|0)<<9|i>>>23)+D|0)<<13|i>>>19)+E|0)<<18|i>>>14,U^=(i=(O^=(i=(T^=(i=(A^=(i=U+O|0)<<7|i>>>25)+U|0)<<9|i>>>23)+A|0)<<13|i>>>19)+T|0)<<18|i>>>14,w^=(i=(A^=(i=(E^=(i=(S^=(i=w+A|0)<<7|i>>>25)+w|0)<<9|i>>>23)+S|0)<<13|i>>>19)+E|0)<<18|i>>>14,N^=(i=(I^=(i=(T^=(i=(C^=(i=N+I|0)<<7|i>>>25)+N|0)<<9|i>>>23)+C|0)<<13|i>>>19)+T|0)<<18|i>>>14,P^=(i=(x^=(i=(R^=(i=(O^=(i=P+x|0)<<7|i>>>25)+P|0)<<9|i>>>23)+O|0)<<13|i>>>19)+R|0)<<18|i>>>14,U^=(i=(D^=(i=(k^=(i=(B^=(i=U+D|0)<<7|i>>>25)+U|0)<<9|i>>>23)+B|0)<<13|i>>>19)+k|0)<<18|i>>>14;w=w+o|0,S=S+a|0,E=E+s|0,A=A+u|0,I=I+c|0,N=N+f|0,C=C+l|0,T=T+h|0,R=R+p|0,x=x+d|0,P=P+y|0,O=O+g|0,B=B+v|0,k=k+_|0,D=D+m|0,U=U+b|0,e[0]=w>>>0&255,e[1]=w>>>8&255,e[2]=w>>>16&255,e[3]=w>>>24&255,e[4]=S>>>0&255,e[5]=S>>>8&255,e[6]=S>>>16&255,e[7]=S>>>24&255,e[8]=E>>>0&255,e[9]=E>>>8&255,e[10]=E>>>16&255,e[11]=E>>>24&255,e[12]=A>>>0&255,e[13]=A>>>8&255,e[14]=A>>>16&255,e[15]=A>>>24&255,e[16]=I>>>0&255,e[17]=I>>>8&255,e[18]=I>>>16&255,e[19]=I>>>24&255,e[20]=N>>>0&255,e[21]=N>>>8&255,e[22]=N>>>16&255,e[23]=N>>>24&255,e[24]=C>>>0&255,e[25]=C>>>8&255,e[26]=C>>>16&255,e[27]=C>>>24&255,e[28]=T>>>0&255,e[29]=T>>>8&255,e[30]=T>>>16&255,e[31]=T>>>24&255,e[32]=R>>>0&255,e[33]=R>>>8&255,e[34]=R>>>16&255,e[35]=R>>>24&255,e[36]=x>>>0&255,e[37]=x>>>8&255,e[38]=x>>>16&255,e[39]=x>>>24&255,e[40]=P>>>0&255,e[41]=P>>>8&255,e[42]=P>>>16&255,e[43]=P>>>24&255,e[44]=O>>>0&255,e[45]=O>>>8&255,e[46]=O>>>16&255,e[47]=O>>>24&255,e[48]=B>>>0&255,e[49]=B>>>8&255,e[50]=B>>>16&255,e[51]=B>>>24&255,e[52]=k>>>0&255,e[53]=k>>>8&255,e[54]=k>>>16&255,e[55]=k>>>24&255,e[56]=D>>>0&255,e[57]=D>>>8&255,e[58]=D>>>16&255,e[59]=D>>>24&255,e[60]=U>>>0&255,e[61]=U>>>8&255,e[62]=U>>>16&255,e[63]=U>>>24&255}(e,t,r,n)}function m(e,t,r,n){!function(e,t,r,n){for(var i,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,s=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,u=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,c=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,f=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,l=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,p=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,d=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,y=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,g=255&r[16]|(255&r[17])<<8|(255&r[18])<<16|(255&r[19])<<24,v=255&r[20]|(255&r[21])<<8|(255&r[22])<<16|(255&r[23])<<24,_=255&r[24]|(255&r[25])<<8|(255&r[26])<<16|(255&r[27])<<24,m=255&r[28]|(255&r[29])<<8|(255&r[30])<<16|(255&r[31])<<24,b=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,w=0;w<20;w+=2)o^=(i=(v^=(i=(p^=(i=(c^=(i=o+v|0)<<7|i>>>25)+o|0)<<9|i>>>23)+c|0)<<13|i>>>19)+p|0)<<18|i>>>14,f^=(i=(a^=(i=(_^=(i=(d^=(i=f+a|0)<<7|i>>>25)+f|0)<<9|i>>>23)+d|0)<<13|i>>>19)+_|0)<<18|i>>>14,y^=(i=(l^=(i=(s^=(i=(m^=(i=y+l|0)<<7|i>>>25)+y|0)<<9|i>>>23)+m|0)<<13|i>>>19)+s|0)<<18|i>>>14,b^=(i=(g^=(i=(h^=(i=(u^=(i=b+g|0)<<7|i>>>25)+b|0)<<9|i>>>23)+u|0)<<13|i>>>19)+h|0)<<18|i>>>14,o^=(i=(u^=(i=(s^=(i=(a^=(i=o+u|0)<<7|i>>>25)+o|0)<<9|i>>>23)+a|0)<<13|i>>>19)+s|0)<<18|i>>>14,f^=(i=(c^=(i=(h^=(i=(l^=(i=f+c|0)<<7|i>>>25)+f|0)<<9|i>>>23)+l|0)<<13|i>>>19)+h|0)<<18|i>>>14,y^=(i=(d^=(i=(p^=(i=(g^=(i=y+d|0)<<7|i>>>25)+y|0)<<9|i>>>23)+g|0)<<13|i>>>19)+p|0)<<18|i>>>14,b^=(i=(m^=(i=(_^=(i=(v^=(i=b+m|0)<<7|i>>>25)+b|0)<<9|i>>>23)+v|0)<<13|i>>>19)+_|0)<<18|i>>>14;e[0]=o>>>0&255,e[1]=o>>>8&255,e[2]=o>>>16&255,e[3]=o>>>24&255,e[4]=f>>>0&255,e[5]=f>>>8&255,e[6]=f>>>16&255,e[7]=f>>>24&255,e[8]=y>>>0&255,e[9]=y>>>8&255,e[10]=y>>>16&255,e[11]=y>>>24&255,e[12]=b>>>0&255,e[13]=b>>>8&255,e[14]=b>>>16&255,e[15]=b>>>24&255,e[16]=l>>>0&255,e[17]=l>>>8&255,e[18]=l>>>16&255,e[19]=l>>>24&255,e[20]=h>>>0&255,e[21]=h>>>8&255,e[22]=h>>>16&255,e[23]=h>>>24&255,e[24]=p>>>0&255,e[25]=p>>>8&255,e[26]=p>>>16&255,e[27]=p>>>24&255,e[28]=d>>>0&255,e[29]=d>>>8&255,e[30]=d>>>16&255,e[31]=d>>>24&255}(e,t,r,n)}var b=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function w(e,t,r,n,i,o,a){var s,u,c=new Uint8Array(16),f=new Uint8Array(64);for(u=0;u<16;u++)c[u]=0;for(u=0;u<8;u++)c[u]=o[u];for(;i>=64;){for(_(f,c,a,b),u=0;u<64;u++)e[t+u]=r[n+u]^f[u];for(s=1,u=8;u<16;u++)s=s+(255&c[u])|0,c[u]=255&s,s>>>=8;i-=64,t+=64,n+=64}if(i>0)for(_(f,c,a,b),u=0;u<i;u++)e[t+u]=r[n+u]^f[u];return 0}function S(e,t,r,n,i){var o,a,s=new Uint8Array(16),u=new Uint8Array(64);for(a=0;a<16;a++)s[a]=0;for(a=0;a<8;a++)s[a]=n[a];for(;r>=64;){for(_(u,s,i,b),a=0;a<64;a++)e[t+a]=u[a];for(o=1,a=8;a<16;a++)o=o+(255&s[a])|0,s[a]=255&o,o>>>=8;r-=64,t+=64}if(r>0)for(_(u,s,i,b),a=0;a<r;a++)e[t+a]=u[a];return 0}function E(e,t,r,n,i){var o=new Uint8Array(32);m(o,n,i,b);for(var a=new Uint8Array(8),s=0;s<8;s++)a[s]=n[s+16];return S(e,t,r,a,o)}function A(e,t,r,n,i,o,a){var s=new Uint8Array(32);m(s,o,a,b);for(var u=new Uint8Array(8),c=0;c<8;c++)u[c]=o[c+16];return w(e,t,r,n,i,u,s)}var I=function(e){var t,r,n,i,o,a,s,u;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,t=255&e[0]|(255&e[1])<<8,this.r[0]=8191&t,r=255&e[2]|(255&e[3])<<8,this.r[1]=8191&(t>>>13|r<<3),n=255&e[4]|(255&e[5])<<8,this.r[2]=7939&(r>>>10|n<<6),i=255&e[6]|(255&e[7])<<8,this.r[3]=8191&(n>>>7|i<<9),o=255&e[8]|(255&e[9])<<8,this.r[4]=255&(i>>>4|o<<12),this.r[5]=o>>>1&8190,a=255&e[10]|(255&e[11])<<8,this.r[6]=8191&(o>>>14|a<<2),s=255&e[12]|(255&e[13])<<8,this.r[7]=8065&(a>>>11|s<<5),u=255&e[14]|(255&e[15])<<8,this.r[8]=8191&(s>>>8|u<<8),this.r[9]=u>>>5&127,this.pad[0]=255&e[16]|(255&e[17])<<8,this.pad[1]=255&e[18]|(255&e[19])<<8,this.pad[2]=255&e[20]|(255&e[21])<<8,this.pad[3]=255&e[22]|(255&e[23])<<8,this.pad[4]=255&e[24]|(255&e[25])<<8,this.pad[5]=255&e[26]|(255&e[27])<<8,this.pad[6]=255&e[28]|(255&e[29])<<8,this.pad[7]=255&e[30]|(255&e[31])<<8};function N(e,t,r,n,i,o){var a=new I(o);return a.update(r,n,i),a.finish(e,t),0}function C(e,t,r,n,i,o){var a=new Uint8Array(16);return N(a,0,r,n,i,o),g(e,t,a,0)}function T(e,t,r,n,i){var o;if(r<32)return-1;for(A(e,0,t,0,r,n,i),N(e,16,e,32,r-32,e),o=0;o<16;o++)e[o]=0;return 0}function R(e,t,r,n,i){var o,a=new Uint8Array(32);if(r<32)return-1;if(E(a,0,32,n,i),0!==C(t,16,t,32,r-32,a))return-1;for(A(e,0,t,0,r,n,i),o=0;o<32;o++)e[o]=0;return 0}function x(e,t){var r;for(r=0;r<16;r++)e[r]=0|t[r]}function P(e){var t,r,n=1;for(t=0;t<16;t++)r=e[t]+n+65535,n=Math.floor(r/65536),e[t]=r-65536*n;e[0]+=n-1+37*(n-1)}function O(e,t,r){for(var n,i=~(r-1),o=0;o<16;o++)n=i&(e[o]^t[o]),e[o]^=n,t[o]^=n}function B(e,r){var n,i,o,a=t(),s=t();for(n=0;n<16;n++)s[n]=r[n];for(P(s),P(s),P(s),i=0;i<2;i++){for(a[0]=s[0]-65517,n=1;n<15;n++)a[n]=s[n]-65535-(a[n-1]>>16&1),a[n-1]&=65535;a[15]=s[15]-32767-(a[14]>>16&1),o=a[15]>>16&1,a[14]&=65535,O(s,a,1-o)}for(n=0;n<16;n++)e[2*n]=255&s[n],e[2*n+1]=s[n]>>8}function k(e,t){var r=new Uint8Array(32),n=new Uint8Array(32);return B(r,e),B(n,t),v(r,0,n,0)}function D(e){var t=new Uint8Array(32);return B(t,e),1&t[0]}function U(e,t){var r;for(r=0;r<16;r++)e[r]=t[2*r]+(t[2*r+1]<<8);e[15]&=32767}function M(e,t,r){for(var n=0;n<16;n++)e[n]=t[n]+r[n]}function j(e,t,r){for(var n=0;n<16;n++)e[n]=t[n]-r[n]}function L(e,t,r){var n,i,o=0,a=0,s=0,u=0,c=0,f=0,l=0,h=0,p=0,d=0,y=0,g=0,v=0,_=0,m=0,b=0,w=0,S=0,E=0,A=0,I=0,N=0,C=0,T=0,R=0,x=0,P=0,O=0,B=0,k=0,D=0,U=r[0],M=r[1],j=r[2],L=r[3],F=r[4],K=r[5],z=r[6],V=r[7],$=r[8],q=r[9],H=r[10],W=r[11],G=r[12],J=r[13],Y=r[14],Z=r[15];o+=(n=t[0])*U,a+=n*M,s+=n*j,u+=n*L,c+=n*F,f+=n*K,l+=n*z,h+=n*V,p+=n*$,d+=n*q,y+=n*H,g+=n*W,v+=n*G,_+=n*J,m+=n*Y,b+=n*Z,a+=(n=t[1])*U,s+=n*M,u+=n*j,c+=n*L,f+=n*F,l+=n*K,h+=n*z,p+=n*V,d+=n*$,y+=n*q,g+=n*H,v+=n*W,_+=n*G,m+=n*J,b+=n*Y,w+=n*Z,s+=(n=t[2])*U,u+=n*M,c+=n*j,f+=n*L,l+=n*F,h+=n*K,p+=n*z,d+=n*V,y+=n*$,g+=n*q,v+=n*H,_+=n*W,m+=n*G,b+=n*J,w+=n*Y,S+=n*Z,u+=(n=t[3])*U,c+=n*M,f+=n*j,l+=n*L,h+=n*F,p+=n*K,d+=n*z,y+=n*V,g+=n*$,v+=n*q,_+=n*H,m+=n*W,b+=n*G,w+=n*J,S+=n*Y,E+=n*Z,c+=(n=t[4])*U,f+=n*M,l+=n*j,h+=n*L,p+=n*F,d+=n*K,y+=n*z,g+=n*V,v+=n*$,_+=n*q,m+=n*H,b+=n*W,w+=n*G,S+=n*J,E+=n*Y,A+=n*Z,f+=(n=t[5])*U,l+=n*M,h+=n*j,p+=n*L,d+=n*F,y+=n*K,g+=n*z,v+=n*V,_+=n*$,m+=n*q,b+=n*H,w+=n*W,S+=n*G,E+=n*J,A+=n*Y,I+=n*Z,l+=(n=t[6])*U,h+=n*M,p+=n*j,d+=n*L,y+=n*F,g+=n*K,v+=n*z,_+=n*V,m+=n*$,b+=n*q,w+=n*H,S+=n*W,E+=n*G,A+=n*J,I+=n*Y,N+=n*Z,h+=(n=t[7])*U,p+=n*M,d+=n*j,y+=n*L,g+=n*F,v+=n*K,_+=n*z,m+=n*V,b+=n*$,w+=n*q,S+=n*H,E+=n*W,A+=n*G,I+=n*J,N+=n*Y,C+=n*Z,p+=(n=t[8])*U,d+=n*M,y+=n*j,g+=n*L,v+=n*F,_+=n*K,m+=n*z,b+=n*V,w+=n*$,S+=n*q,E+=n*H,A+=n*W,I+=n*G,N+=n*J,C+=n*Y,T+=n*Z,d+=(n=t[9])*U,y+=n*M,g+=n*j,v+=n*L,_+=n*F,m+=n*K,b+=n*z,w+=n*V,S+=n*$,E+=n*q,A+=n*H,I+=n*W,N+=n*G,C+=n*J,T+=n*Y,R+=n*Z,y+=(n=t[10])*U,g+=n*M,v+=n*j,_+=n*L,m+=n*F,b+=n*K,w+=n*z,S+=n*V,E+=n*$,A+=n*q,I+=n*H,N+=n*W,C+=n*G,T+=n*J,R+=n*Y,x+=n*Z,g+=(n=t[11])*U,v+=n*M,_+=n*j,m+=n*L,b+=n*F,w+=n*K,S+=n*z,E+=n*V,A+=n*$,I+=n*q,N+=n*H,C+=n*W,T+=n*G,R+=n*J,x+=n*Y,P+=n*Z,v+=(n=t[12])*U,_+=n*M,m+=n*j,b+=n*L,w+=n*F,S+=n*K,E+=n*z,A+=n*V,I+=n*$,N+=n*q,C+=n*H,T+=n*W,R+=n*G,x+=n*J,P+=n*Y,O+=n*Z,_+=(n=t[13])*U,m+=n*M,b+=n*j,w+=n*L,S+=n*F,E+=n*K,A+=n*z,I+=n*V,N+=n*$,C+=n*q,T+=n*H,R+=n*W,x+=n*G,P+=n*J,O+=n*Y,B+=n*Z,m+=(n=t[14])*U,b+=n*M,w+=n*j,S+=n*L,E+=n*F,A+=n*K,I+=n*z,N+=n*V,C+=n*$,T+=n*q,R+=n*H,x+=n*W,P+=n*G,O+=n*J,B+=n*Y,k+=n*Z,b+=(n=t[15])*U,a+=38*(S+=n*j),s+=38*(E+=n*L),u+=38*(A+=n*F),c+=38*(I+=n*K),f+=38*(N+=n*z),l+=38*(C+=n*V),h+=38*(T+=n*$),p+=38*(R+=n*q),d+=38*(x+=n*H),y+=38*(P+=n*W),g+=38*(O+=n*G),v+=38*(B+=n*J),_+=38*(k+=n*Y),m+=38*(D+=n*Z),o=(n=(o+=38*(w+=n*M))+(i=1)+65535)-65536*(i=Math.floor(n/65536)),a=(n=a+i+65535)-65536*(i=Math.floor(n/65536)),s=(n=s+i+65535)-65536*(i=Math.floor(n/65536)),u=(n=u+i+65535)-65536*(i=Math.floor(n/65536)),c=(n=c+i+65535)-65536*(i=Math.floor(n/65536)),f=(n=f+i+65535)-65536*(i=Math.floor(n/65536)),l=(n=l+i+65535)-65536*(i=Math.floor(n/65536)),h=(n=h+i+65535)-65536*(i=Math.floor(n/65536)),p=(n=p+i+65535)-65536*(i=Math.floor(n/65536)),d=(n=d+i+65535)-65536*(i=Math.floor(n/65536)),y=(n=y+i+65535)-65536*(i=Math.floor(n/65536)),g=(n=g+i+65535)-65536*(i=Math.floor(n/65536)),v=(n=v+i+65535)-65536*(i=Math.floor(n/65536)),_=(n=_+i+65535)-65536*(i=Math.floor(n/65536)),m=(n=m+i+65535)-65536*(i=Math.floor(n/65536)),b=(n=b+i+65535)-65536*(i=Math.floor(n/65536)),o=(n=(o+=i-1+37*(i-1))+(i=1)+65535)-65536*(i=Math.floor(n/65536)),a=(n=a+i+65535)-65536*(i=Math.floor(n/65536)),s=(n=s+i+65535)-65536*(i=Math.floor(n/65536)),u=(n=u+i+65535)-65536*(i=Math.floor(n/65536)),c=(n=c+i+65535)-65536*(i=Math.floor(n/65536)),f=(n=f+i+65535)-65536*(i=Math.floor(n/65536)),l=(n=l+i+65535)-65536*(i=Math.floor(n/65536)),h=(n=h+i+65535)-65536*(i=Math.floor(n/65536)),p=(n=p+i+65535)-65536*(i=Math.floor(n/65536)),d=(n=d+i+65535)-65536*(i=Math.floor(n/65536)),y=(n=y+i+65535)-65536*(i=Math.floor(n/65536)),g=(n=g+i+65535)-65536*(i=Math.floor(n/65536)),v=(n=v+i+65535)-65536*(i=Math.floor(n/65536)),_=(n=_+i+65535)-65536*(i=Math.floor(n/65536)),m=(n=m+i+65535)-65536*(i=Math.floor(n/65536)),b=(n=b+i+65535)-65536*(i=Math.floor(n/65536)),o+=i-1+37*(i-1),e[0]=o,e[1]=a,e[2]=s,e[3]=u,e[4]=c,e[5]=f,e[6]=l,e[7]=h,e[8]=p,e[9]=d,e[10]=y,e[11]=g,e[12]=v,e[13]=_,e[14]=m,e[15]=b}function F(e,t){L(e,t,t)}function K(e,r){var n,i=t();for(n=0;n<16;n++)i[n]=r[n];for(n=253;n>=0;n--)F(i,i),2!==n&&4!==n&&L(i,i,r);for(n=0;n<16;n++)e[n]=i[n]}function z(e,r){var n,i=t();for(n=0;n<16;n++)i[n]=r[n];for(n=250;n>=0;n--)F(i,i),1!==n&&L(i,i,r);for(n=0;n<16;n++)e[n]=i[n]}function V(e,r,n){var i,o,a=new Uint8Array(32),s=new Float64Array(80),c=t(),f=t(),l=t(),h=t(),p=t(),d=t();for(o=0;o<31;o++)a[o]=r[o];for(a[31]=127&r[31]|64,a[0]&=248,U(s,n),o=0;o<16;o++)f[o]=s[o],h[o]=c[o]=l[o]=0;for(c[0]=h[0]=1,o=254;o>=0;--o)O(c,f,i=a[o>>>3]>>>(7&o)&1),O(l,h,i),M(p,c,l),j(c,c,l),M(l,f,h),j(f,f,h),F(h,p),F(d,c),L(c,l,c),L(l,f,p),M(p,c,l),j(c,c,l),F(f,c),j(l,h,d),L(c,l,u),M(c,c,h),L(l,l,c),L(c,h,d),L(h,f,s),F(f,p),O(c,f,i),O(l,h,i);for(o=0;o<16;o++)s[o+16]=c[o],s[o+32]=l[o],s[o+48]=f[o],s[o+64]=h[o];var y=s.subarray(32),g=s.subarray(16);return K(y,y),L(g,g,y),B(e,g),0}function $(e,t){return V(e,t,o)}function q(e,t){return n(t,32),$(e,t)}function H(e,t,r){var n=new Uint8Array(32);return V(n,r,t),m(e,i,n,b)}I.prototype.blocks=function(e,t,r){for(var n,i,o,a,s,u,c,f,l,h,p,d,y,g,v,_,m,b,w,S=this.fin?0:2048,E=this.h[0],A=this.h[1],I=this.h[2],N=this.h[3],C=this.h[4],T=this.h[5],R=this.h[6],x=this.h[7],P=this.h[8],O=this.h[9],B=this.r[0],k=this.r[1],D=this.r[2],U=this.r[3],M=this.r[4],j=this.r[5],L=this.r[6],F=this.r[7],K=this.r[8],z=this.r[9];r>=16;)h=l=0,h+=(E+=8191&(n=255&e[t+0]|(255&e[t+1])<<8))*B,h+=(A+=8191&(n>>>13|(i=255&e[t+2]|(255&e[t+3])<<8)<<3))*(5*z),h+=(I+=8191&(i>>>10|(o=255&e[t+4]|(255&e[t+5])<<8)<<6))*(5*K),h+=(N+=8191&(o>>>7|(a=255&e[t+6]|(255&e[t+7])<<8)<<9))*(5*F),l=(h+=(C+=8191&(a>>>4|(s=255&e[t+8]|(255&e[t+9])<<8)<<12))*(5*L))>>>13,h&=8191,h+=(T+=s>>>1&8191)*(5*j),h+=(R+=8191&(s>>>14|(u=255&e[t+10]|(255&e[t+11])<<8)<<2))*(5*M),h+=(x+=8191&(u>>>11|(c=255&e[t+12]|(255&e[t+13])<<8)<<5))*(5*U),h+=(P+=8191&(c>>>8|(f=255&e[t+14]|(255&e[t+15])<<8)<<8))*(5*D),p=l+=(h+=(O+=f>>>5|S)*(5*k))>>>13,p+=E*k,p+=A*B,p+=I*(5*z),p+=N*(5*K),l=(p+=C*(5*F))>>>13,p&=8191,p+=T*(5*L),p+=R*(5*j),p+=x*(5*M),p+=P*(5*U),l+=(p+=O*(5*D))>>>13,p&=8191,d=l,d+=E*D,d+=A*k,d+=I*B,d+=N*(5*z),l=(d+=C*(5*K))>>>13,d&=8191,d+=T*(5*F),d+=R*(5*L),d+=x*(5*j),d+=P*(5*M),y=l+=(d+=O*(5*U))>>>13,y+=E*U,y+=A*D,y+=I*k,y+=N*B,l=(y+=C*(5*z))>>>13,y&=8191,y+=T*(5*K),y+=R*(5*F),y+=x*(5*L),y+=P*(5*j),g=l+=(y+=O*(5*M))>>>13,g+=E*M,g+=A*U,g+=I*D,g+=N*k,l=(g+=C*B)>>>13,g&=8191,g+=T*(5*z),g+=R*(5*K),g+=x*(5*F),g+=P*(5*L),v=l+=(g+=O*(5*j))>>>13,v+=E*j,v+=A*M,v+=I*U,v+=N*D,l=(v+=C*k)>>>13,v&=8191,v+=T*B,v+=R*(5*z),v+=x*(5*K),v+=P*(5*F),_=l+=(v+=O*(5*L))>>>13,_+=E*L,_+=A*j,_+=I*M,_+=N*U,l=(_+=C*D)>>>13,_&=8191,_+=T*k,_+=R*B,_+=x*(5*z),_+=P*(5*K),m=l+=(_+=O*(5*F))>>>13,m+=E*F,m+=A*L,m+=I*j,m+=N*M,l=(m+=C*U)>>>13,m&=8191,m+=T*D,m+=R*k,m+=x*B,m+=P*(5*z),b=l+=(m+=O*(5*K))>>>13,b+=E*K,b+=A*F,b+=I*L,b+=N*j,l=(b+=C*M)>>>13,b&=8191,b+=T*U,b+=R*D,b+=x*k,b+=P*B,w=l+=(b+=O*(5*z))>>>13,w+=E*z,w+=A*K,w+=I*F,w+=N*L,l=(w+=C*j)>>>13,w&=8191,w+=T*M,w+=R*U,w+=x*D,w+=P*k,E=h=8191&(l=(l=((l+=(w+=O*B)>>>13)<<2)+l|0)+(h&=8191)|0),A=p+=l>>>=13,I=d&=8191,N=y&=8191,C=g&=8191,T=v&=8191,R=_&=8191,x=m&=8191,P=b&=8191,O=w&=8191,t+=16,r-=16;this.h[0]=E,this.h[1]=A,this.h[2]=I,this.h[3]=N,this.h[4]=C,this.h[5]=T,this.h[6]=R,this.h[7]=x,this.h[8]=P,this.h[9]=O},I.prototype.finish=function(e,t){var r,n,i,o,a=new Uint16Array(10);if(this.leftover){for(o=this.leftover,this.buffer[o++]=1;o<16;o++)this.buffer[o]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(r=this.h[1]>>>13,this.h[1]&=8191,o=2;o<10;o++)this.h[o]+=r,r=this.h[o]>>>13,this.h[o]&=8191;for(this.h[0]+=5*r,r=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=r,r=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=r,a[0]=this.h[0]+5,r=a[0]>>>13,a[0]&=8191,o=1;o<10;o++)a[o]=this.h[o]+r,r=a[o]>>>13,a[o]&=8191;for(a[9]-=8192,n=(1^r)-1,o=0;o<10;o++)a[o]&=n;for(n=~n,o=0;o<10;o++)this.h[o]=this.h[o]&n|a[o];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),i=this.h[0]+this.pad[0],this.h[0]=65535&i,o=1;o<8;o++)i=(this.h[o]+this.pad[o]|0)+(i>>>16)|0,this.h[o]=65535&i;e[t+0]=this.h[0]>>>0&255,e[t+1]=this.h[0]>>>8&255,e[t+2]=this.h[1]>>>0&255,e[t+3]=this.h[1]>>>8&255,e[t+4]=this.h[2]>>>0&255,e[t+5]=this.h[2]>>>8&255,e[t+6]=this.h[3]>>>0&255,e[t+7]=this.h[3]>>>8&255,e[t+8]=this.h[4]>>>0&255,e[t+9]=this.h[4]>>>8&255,e[t+10]=this.h[5]>>>0&255,e[t+11]=this.h[5]>>>8&255,e[t+12]=this.h[6]>>>0&255,e[t+13]=this.h[6]>>>8&255,e[t+14]=this.h[7]>>>0&255,e[t+15]=this.h[7]>>>8&255},I.prototype.update=function(e,t,r){var n,i;if(this.leftover){for((i=16-this.leftover)>r&&(i=r),n=0;n<i;n++)this.buffer[this.leftover+n]=e[t+n];if(r-=i,t+=i,this.leftover+=i,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(r>=16&&(i=r-r%16,this.blocks(e,t,i),t+=i,r-=i),r){for(n=0;n<r;n++)this.buffer[this.leftover+n]=e[t+n];this.leftover+=r}};var W=T,G=R;var J=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function Y(e,t,r,n){for(var i,o,a,s,u,c,f,l,h,p,d,y,g,v,_,m,b,w,S,E,A,I,N,C,T,R,x=new Int32Array(16),P=new Int32Array(16),O=e[0],B=e[1],k=e[2],D=e[3],U=e[4],M=e[5],j=e[6],L=e[7],F=t[0],K=t[1],z=t[2],V=t[3],$=t[4],q=t[5],H=t[6],W=t[7],G=0;n>=128;){for(S=0;S<16;S++)E=8*S+G,x[S]=r[E+0]<<24|r[E+1]<<16|r[E+2]<<8|r[E+3],P[S]=r[E+4]<<24|r[E+5]<<16|r[E+6]<<8|r[E+7];for(S=0;S<80;S++)if(i=O,o=B,a=k,s=D,u=U,c=M,f=j,L,h=F,p=K,d=z,y=V,g=$,v=q,_=H,W,N=65535&(I=W),C=I>>>16,T=65535&(A=L),R=A>>>16,N+=65535&(I=($>>>14|U<<18)^($>>>18|U<<14)^(U>>>9|$<<23)),C+=I>>>16,T+=65535&(A=(U>>>14|$<<18)^(U>>>18|$<<14)^($>>>9|U<<23)),R+=A>>>16,N+=65535&(I=$&q^~$&H),C+=I>>>16,T+=65535&(A=U&M^~U&j),R+=A>>>16,N+=65535&(I=J[2*S+1]),C+=I>>>16,T+=65535&(A=J[2*S]),R+=A>>>16,A=x[S%16],C+=(I=P[S%16])>>>16,T+=65535&A,R+=A>>>16,T+=(C+=(N+=65535&I)>>>16)>>>16,N=65535&(I=w=65535&N|C<<16),C=I>>>16,T=65535&(A=b=65535&T|(R+=T>>>16)<<16),R=A>>>16,N+=65535&(I=(F>>>28|O<<4)^(O>>>2|F<<30)^(O>>>7|F<<25)),C+=I>>>16,T+=65535&(A=(O>>>28|F<<4)^(F>>>2|O<<30)^(F>>>7|O<<25)),R+=A>>>16,C+=(I=F&K^F&z^K&z)>>>16,T+=65535&(A=O&B^O&k^B&k),R+=A>>>16,l=65535&(T+=(C+=(N+=65535&I)>>>16)>>>16)|(R+=T>>>16)<<16,m=65535&N|C<<16,N=65535&(I=y),C=I>>>16,T=65535&(A=s),R=A>>>16,C+=(I=w)>>>16,T+=65535&(A=b),R+=A>>>16,B=i,k=o,D=a,U=s=65535&(T+=(C+=(N+=65535&I)>>>16)>>>16)|(R+=T>>>16)<<16,M=u,j=c,L=f,O=l,K=h,z=p,V=d,$=y=65535&N|C<<16,q=g,H=v,W=_,F=m,S%16==15)for(E=0;E<16;E++)A=x[E],N=65535&(I=P[E]),C=I>>>16,T=65535&A,R=A>>>16,A=x[(E+9)%16],N+=65535&(I=P[(E+9)%16]),C+=I>>>16,T+=65535&A,R+=A>>>16,b=x[(E+1)%16],N+=65535&(I=((w=P[(E+1)%16])>>>1|b<<31)^(w>>>8|b<<24)^(w>>>7|b<<25)),C+=I>>>16,T+=65535&(A=(b>>>1|w<<31)^(b>>>8|w<<24)^b>>>7),R+=A>>>16,b=x[(E+14)%16],C+=(I=((w=P[(E+14)%16])>>>19|b<<13)^(b>>>29|w<<3)^(w>>>6|b<<26))>>>16,T+=65535&(A=(b>>>19|w<<13)^(w>>>29|b<<3)^b>>>6),R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,x[E]=65535&T|R<<16,P[E]=65535&N|C<<16;N=65535&(I=F),C=I>>>16,T=65535&(A=O),R=A>>>16,A=e[0],C+=(I=t[0])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[0]=O=65535&T|R<<16,t[0]=F=65535&N|C<<16,N=65535&(I=K),C=I>>>16,T=65535&(A=B),R=A>>>16,A=e[1],C+=(I=t[1])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[1]=B=65535&T|R<<16,t[1]=K=65535&N|C<<16,N=65535&(I=z),C=I>>>16,T=65535&(A=k),R=A>>>16,A=e[2],C+=(I=t[2])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[2]=k=65535&T|R<<16,t[2]=z=65535&N|C<<16,N=65535&(I=V),C=I>>>16,T=65535&(A=D),R=A>>>16,A=e[3],C+=(I=t[3])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[3]=D=65535&T|R<<16,t[3]=V=65535&N|C<<16,N=65535&(I=$),C=I>>>16,T=65535&(A=U),R=A>>>16,A=e[4],C+=(I=t[4])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[4]=U=65535&T|R<<16,t[4]=$=65535&N|C<<16,N=65535&(I=q),C=I>>>16,T=65535&(A=M),R=A>>>16,A=e[5],C+=(I=t[5])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[5]=M=65535&T|R<<16,t[5]=q=65535&N|C<<16,N=65535&(I=H),C=I>>>16,T=65535&(A=j),R=A>>>16,A=e[6],C+=(I=t[6])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[6]=j=65535&T|R<<16,t[6]=H=65535&N|C<<16,N=65535&(I=W),C=I>>>16,T=65535&(A=L),R=A>>>16,A=e[7],C+=(I=t[7])>>>16,T+=65535&A,R+=A>>>16,R+=(T+=(C+=(N+=65535&I)>>>16)>>>16)>>>16,e[7]=L=65535&T|R<<16,t[7]=W=65535&N|C<<16,G+=128,n-=128}return n}function Z(e,t,r){var n,i=new Int32Array(8),o=new Int32Array(8),a=new Uint8Array(256),s=r;for(i[0]=1779033703,i[1]=3144134277,i[2]=1013904242,i[3]=2773480762,i[4]=1359893119,i[5]=2600822924,i[6]=528734635,i[7]=1541459225,o[0]=4089235720,o[1]=2227873595,o[2]=4271175723,o[3]=1595750129,o[4]=2917565137,o[5]=725511199,o[6]=4215389547,o[7]=327033209,Y(i,o,t,r),r%=128,n=0;n<r;n++)a[n]=t[s-r+n];for(a[r]=128,a[(r=256-128*(r<112?1:0))-9]=0,d(a,r-8,s/536870912|0,s<<3),Y(i,o,a,r),n=0;n<8;n++)d(e,8*n,i[n],o[n]);return 0}function X(e,r){var n=t(),i=t(),o=t(),a=t(),s=t(),u=t(),c=t(),l=t(),h=t();j(n,e[1],e[0]),j(h,r[1],r[0]),L(n,n,h),M(i,e[0],e[1]),M(h,r[0],r[1]),L(i,i,h),L(o,e[3],r[3]),L(o,o,f),L(a,e[2],r[2]),M(a,a,a),j(s,i,n),j(u,a,o),M(c,a,o),M(l,i,n),L(e[0],s,u),L(e[1],l,c),L(e[2],c,u),L(e[3],s,l)}function Q(e,t,r){var n;for(n=0;n<4;n++)O(e[n],t[n],r)}function ee(e,r){var n=t(),i=t(),o=t();K(o,r[2]),L(n,r[0],o),L(i,r[1],o),B(e,i),e[31]^=D(n)<<7}function te(e,t,r){var n,i;for(x(e[0],a),x(e[1],s),x(e[2],s),x(e[3],a),i=255;i>=0;--i)Q(e,t,n=r[i/8|0]>>(7&i)&1),X(t,e),X(e,e),Q(e,t,n)}function re(e,r){var n=[t(),t(),t(),t()];x(n[0],l),x(n[1],h),x(n[2],s),L(n[3],l,h),te(e,n,r)}function ne(e,r,i){var o,a=new Uint8Array(64),s=[t(),t(),t(),t()];for(i||n(r,32),Z(a,r,32),a[0]&=248,a[31]&=127,a[31]|=64,re(s,a),ee(e,s),o=0;o<32;o++)r[o+32]=e[o];return 0}var ie=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function oe(e,t){var r,n,i,o;for(n=63;n>=32;--n){for(r=0,i=n-32,o=n-12;i<o;++i)t[i]+=r-16*t[n]*ie[i-(n-32)],r=Math.floor((t[i]+128)/256),t[i]-=256*r;t[i]+=r,t[n]=0}for(r=0,i=0;i<32;i++)t[i]+=r-(t[31]>>4)*ie[i],r=t[i]>>8,t[i]&=255;for(i=0;i<32;i++)t[i]-=r*ie[i];for(n=0;n<32;n++)t[n+1]+=t[n]>>8,e[n]=255&t[n]}function ae(e){var t,r=new Float64Array(64);for(t=0;t<64;t++)r[t]=e[t];for(t=0;t<64;t++)e[t]=0;oe(e,r)}function se(e,r,n,i){var o,a,s=new Uint8Array(64),u=new Uint8Array(64),c=new Uint8Array(64),f=new Float64Array(64),l=[t(),t(),t(),t()];Z(s,i,32),s[0]&=248,s[31]&=127,s[31]|=64;var h=n+64;for(o=0;o<n;o++)e[64+o]=r[o];for(o=0;o<32;o++)e[32+o]=s[32+o];for(Z(c,e.subarray(32),n+32),ae(c),re(l,c),ee(e,l),o=32;o<64;o++)e[o]=i[o];for(Z(u,e,n+64),ae(u),o=0;o<64;o++)f[o]=0;for(o=0;o<32;o++)f[o]=c[o];for(o=0;o<32;o++)for(a=0;a<32;a++)f[o+a]+=u[o]*s[a];return oe(e.subarray(32),f),h}function ue(e,r,n,i){var o,u=new Uint8Array(32),f=new Uint8Array(64),l=[t(),t(),t(),t()],h=[t(),t(),t(),t()];if(n<64)return-1;if(function(e,r){var n=t(),i=t(),o=t(),u=t(),f=t(),l=t(),h=t();return x(e[2],s),U(e[1],r),F(o,e[1]),L(u,o,c),j(o,o,e[2]),M(u,e[2],u),F(f,u),F(l,f),L(h,l,f),L(n,h,o),L(n,n,u),z(n,n),L(n,n,o),L(n,n,u),L(n,n,u),L(e[0],n,u),F(i,e[0]),L(i,i,u),k(i,o)&&L(e[0],e[0],p),F(i,e[0]),L(i,i,u),k(i,o)?-1:(D(e[0])===r[31]>>7&&j(e[0],a,e[0]),L(e[3],e[0],e[1]),0)}(h,i))return-1;for(o=0;o<n;o++)e[o]=r[o];for(o=0;o<32;o++)e[o+32]=i[o];if(Z(f,e,n),ae(f),te(l,h,f),re(h,r.subarray(32)),X(l,h),ee(u,l),n-=64,v(r,0,u,0)){for(o=0;o<n;o++)e[o]=0;return-1}for(o=0;o<n;o++)e[o]=r[o+64];return n}function ce(e,t){if(32!==e.length)throw new Error("bad key size");if(24!==t.length)throw new Error("bad nonce size")}function fe(){for(var e=0;e<arguments.length;e++)if(!(arguments[e]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function le(e){for(var t=0;t<e.length;t++)e[t]=0}e.lowlevel={crypto_core_hsalsa20:m,crypto_stream_xor:A,crypto_stream:E,crypto_stream_salsa20_xor:w,crypto_stream_salsa20:S,crypto_onetimeauth:N,crypto_onetimeauth_verify:C,crypto_verify_16:g,crypto_verify_32:v,crypto_secretbox:T,crypto_secretbox_open:R,crypto_scalarmult:V,crypto_scalarmult_base:$,crypto_box_beforenm:H,crypto_box_afternm:W,crypto_box:function(e,t,r,n,i,o){var a=new Uint8Array(32);return H(a,i,o),W(e,t,r,n,a)},crypto_box_open:function(e,t,r,n,i,o){var a=new Uint8Array(32);return H(a,i,o),G(e,t,r,n,a)},crypto_box_keypair:q,crypto_hash:Z,crypto_sign:se,crypto_sign_keypair:ne,crypto_sign_open:ue,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:t,D:c,L:ie,pack25519:B,unpack25519:U,M:L,A:M,S:F,Z:j,pow2523:z,add:X,set25519:x,modL:oe,scalarmult:te,scalarbase:re},e.randomBytes=function(e){var t=new Uint8Array(e);return n(t,e),t},e.secretbox=function(e,t,r){fe(e,t,r),ce(r,t);for(var n=new Uint8Array(32+e.length),i=new Uint8Array(n.length),o=0;o<e.length;o++)n[o+32]=e[o];return T(i,n,n.length,t,r),i.subarray(16)},e.secretbox.open=function(e,t,r){fe(e,t,r),ce(r,t);for(var n=new Uint8Array(16+e.length),i=new Uint8Array(n.length),o=0;o<e.length;o++)n[o+16]=e[o];return n.length<32||0!==R(i,n,n.length,t,r)?null:i.subarray(32)},e.secretbox.keyLength=32,e.secretbox.nonceLength=24,e.secretbox.overheadLength=16,e.scalarMult=function(e,t){if(fe(e,t),32!==e.length)throw new Error("bad n size");if(32!==t.length)throw new Error("bad p size");var r=new Uint8Array(32);return V(r,e,t),r},e.scalarMult.base=function(e){if(fe(e),32!==e.length)throw new Error("bad n size");var t=new Uint8Array(32);return $(t,e),t},e.scalarMult.scalarLength=32,e.scalarMult.groupElementLength=32,e.box=function(t,r,n,i){var o=e.box.before(n,i);return e.secretbox(t,r,o)},e.box.before=function(e,t){fe(e,t),function(e,t){if(32!==e.length)throw new Error("bad public key size");if(32!==t.length)throw new Error("bad secret key size")}(e,t);var r=new Uint8Array(32);return H(r,e,t),r},e.box.after=e.secretbox,e.box.open=function(t,r,n,i){var o=e.box.before(n,i);return e.secretbox.open(t,r,o)},e.box.open.after=e.secretbox.open,e.box.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(32);return q(e,t),{publicKey:e,secretKey:t}},e.box.keyPair.fromSecretKey=function(e){if(fe(e),32!==e.length)throw new Error("bad secret key size");var t=new Uint8Array(32);return $(t,e),{publicKey:t,secretKey:new Uint8Array(e)}},e.box.publicKeyLength=32,e.box.secretKeyLength=32,e.box.sharedKeyLength=32,e.box.nonceLength=24,e.box.overheadLength=e.secretbox.overheadLength,e.sign=function(e,t){if(fe(e,t),64!==t.length)throw new Error("bad secret key size");var r=new Uint8Array(64+e.length);return se(r,e,e.length,t),r},e.sign.open=function(e,t){if(fe(e,t),32!==t.length)throw new Error("bad public key size");var r=new Uint8Array(e.length),n=ue(r,e,e.length,t);if(n<0)return null;for(var i=new Uint8Array(n),o=0;o<i.length;o++)i[o]=r[o];return i},e.sign.detached=function(t,r){for(var n=e.sign(t,r),i=new Uint8Array(64),o=0;o<i.length;o++)i[o]=n[o];return i},e.sign.detached.verify=function(e,t,r){if(fe(e,t,r),64!==t.length)throw new Error("bad signature size");if(32!==r.length)throw new Error("bad public key size");var n,i=new Uint8Array(64+e.length),o=new Uint8Array(64+e.length);for(n=0;n<64;n++)i[n]=t[n];for(n=0;n<e.length;n++)i[n+64]=e[n];return ue(o,i,i.length,r)>=0},e.sign.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(64);return ne(e,t),{publicKey:e,secretKey:t}},e.sign.keyPair.fromSecretKey=function(e){if(fe(e),64!==e.length)throw new Error("bad secret key size");for(var t=new Uint8Array(32),r=0;r<t.length;r++)t[r]=e[32+r];return{publicKey:t,secretKey:new Uint8Array(e)}},e.sign.keyPair.fromSeed=function(e){if(fe(e),32!==e.length)throw new Error("bad seed size");for(var t=new Uint8Array(32),r=new Uint8Array(64),n=0;n<32;n++)r[n]=e[n];return ne(t,r,!0),{publicKey:t,secretKey:r}},e.sign.publicKeyLength=32,e.sign.secretKeyLength=64,e.sign.seedLength=32,e.sign.signatureLength=64,e.hash=function(e){fe(e);var t=new Uint8Array(64);return Z(t,e,e.length),t},e.hash.hashLength=64,e.verify=function(e,t){return fe(e,t),0!==e.length&&0!==t.length&&(e.length===t.length&&0===y(e,0,t,0,e.length))},e.setPRNG=function(e){n=e},function(){var t="undefined"!=typeof self?self.crypto||self.msCrypto:null;if(t&&t.getRandomValues){e.setPRNG((function(e,r){var n,i=new Uint8Array(r);for(n=0;n<r;n+=65536)t.getRandomValues(i.subarray(n,n+Math.min(r-n,65536)));for(n=0;n<r;n++)e[n]=i[n];le(i)}))}else(t=r(2))&&t.randomBytes&&e.setPRNG((function(e,r){var n,i=t.randomBytes(r);for(n=0;n<r;n++)e[n]=i[n];le(i)}))}()}(e.exports?e.exports:self.nacl=self.nacl||{})},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9);class i{static parse(e){const t=[],r=e.toString("utf8").split("\n").map(e=>e.trim()).filter(e=>""!==e&&!e.startsWith("#"));for(;r.length>0;)t.push(o(r));if(0===t.length)throw new Error("PEM: no block");return t}constructor(e,t){this.type=e,this.body=t,this.headers=Object.create(null)}get procType(){return this.getHeader("Proc-Type")}getHeader(e){const t=this.headers[e];return null==t?"":t}setHeader(e,t){if(e.includes(":"))throw new Error("pem: cannot encode a header key that contains a colon");if(""===e||""===t)throw new Error("pem: invalid header key or value");this.headers[e]=t}toString(){let e="-----BEGIN "+this.type+"-----\n";const t=Object.keys(this.headers);if(t.length>0){const r=this.procType;""!==r&&(e+=`Proc-Type: ${r}\n`),t.sort();for(const r of t)"Proc-Type"!==r&&(e+=`${r}: ${this.headers[r]}\n`);e+="\n"}const r=this.body.toString("base64");let n=0;for(;n<r.length;)e+=r.slice(n,n+64)+"\n",n+=64;return e+="-----END "+this.type+"-----\n",e}toBuffer(){return Buffer.from(this.toString(),"utf8")}valueOf(){return this.body}toJSON(){return{type:this.type,body:this.body,headers:this.headers}}[n.inspect.custom](e,t){return`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}function o(e){let t=e.shift();if(null==t||!t.startsWith("-----BEGIN ")||!t.endsWith("-----"))throw new Error("pem: invalid BEGIN line");const r=t.slice("-----BEGIN ".length,t.length-"-----".length);if(""===r)throw new Error("pem: invalid type");const n=[];for(t=e.shift();null!=t&&t.includes(": ");){const r=t.split(": ");if(2!==r.length||""===r[0]||""===r[1])throw new Error("pem: invalid Header line");n.push(r),t=e.shift()}let o="";for(;null!=t&&!t.startsWith("-----END ");)o+=t,t=e.shift();if(null==t||t!==`-----END ${r}-----`)throw new Error("pem: invalid END line");const a=new i(r,Buffer.from(o,"base64"));if(""===o||a.body.toString("base64")!==o)throw new Error("pem: invalid base64 body");for(const e of n)a.setHeader(e[0],e[1]);return a}t.PEM=i},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9),i=r(22);var o,a;!function(e){e[e.UNIVERSAL=0]="UNIVERSAL",e[e.APPLICATION=64]="APPLICATION",e[e.CONTEXT_SPECIFIC=128]="CONTEXT_SPECIFIC",e[e.PRIVATE=192]="PRIVATE"}(o=t.Class||(t.Class={})),function(e){e[e.NONE=0]="NONE",e[e.BOOLEAN=1]="BOOLEAN",e[e.INTEGER=2]="INTEGER",e[e.BITSTRING=3]="BITSTRING",e[e.OCTETSTRING=4]="OCTETSTRING",e[e.NULL=5]="NULL",e[e.OID=6]="OID",e[e.ENUMERATED=10]="ENUMERATED",e[e.UTF8=12]="UTF8",e[e.SEQUENCE=16]="SEQUENCE",e[e.SET=17]="SET",e[e.NUMERICSTRING=18]="NUMERICSTRING",e[e.PRINTABLESTRING=19]="PRINTABLESTRING",e[e.T61STRING=20]="T61STRING",e[e.IA5STRING=22]="IA5STRING",e[e.UTCTIME=23]="UTCTIME",e[e.GENERALIZEDTIME=24]="GENERALIZEDTIME",e[e.GENERALSTRING=27]="GENERALSTRING"}(a=t.Tag||(t.Tag={}));class s{constructor(e,t){this.buf=e,this.bitLen=t}at(e){if(e<0||e>=this.bitLen||!Number.isInteger(e))return 0;const t=Math.floor(e/8),r=7-e%8;return this.buf[t]>>r&1}rightAlign(){const e=8-this.bitLen%8;if(8===e||0===this.buf.length)return this.buf;const t=Buffer.alloc(this.buf.length);t[0]=this.buf[0]>>e;for(let r=1;r<this.buf.length;r++)t[r]=this.buf[r-1]<<8-e,t[r]|=this.buf[r]>>e;return t}}t.BitString=s;class u{static Bool(e){const t=new u(o.UNIVERSAL,a.BOOLEAN,Buffer.from([e?255:0]));return t._value=e,t}static parseBool(e){if(!(e instanceof Buffer)||1!==e.length)throw new Error("ASN1 syntax error: invalid boolean");switch(e[0]){case 0:return!1;case 255:return!0;default:throw new Error("ASN1 syntax error: invalid boolean")}}static Integer(e){if(e instanceof Buffer){const t=new u(o.UNIVERSAL,a.INTEGER,e);return t._value=e.toString("hex"),t}if(!Number.isSafeInteger(e))throw new Error("ASN1 syntax error: invalid integer");let t;if(e>=-128&&e<128)t=Buffer.alloc(1),t.writeInt8(e,0);else if(e>=-32768&&e<32768)t=Buffer.alloc(2),t.writeIntBE(e,0,2);else if(e>=-8388608&&e<8388608)t=Buffer.alloc(3),t.writeIntBE(e,0,3);else if(e>=-2147483648&&e<2147483648)t=Buffer.alloc(4),t.writeIntBE(e,0,4);else if(e>=-549755813888&&e<549755813888)t=Buffer.alloc(5),t.writeIntBE(e,0,5);else{if(!(e>=-0x800000000000&&e<0x800000000000))throw new Error("ASN1 syntax error: invalid Integer");t=Buffer.alloc(6),t.writeIntBE(e,0,6)}const r=new u(o.UNIVERSAL,a.INTEGER,t);return r._value=e,r}static parseInteger(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid Integer");return e.length>6?e.toString("hex"):e.readIntBE(0,e.length)}static parseIntegerNum(e){const t=u.parseInteger(e);if("number"!=typeof t)throw new Error("ASN1 syntax error: invalid Integer number");return t}static parseIntegerStr(e){const t=u.parseInteger(e);return"number"==typeof t?t.toString(16):t}static BitString(e){e instanceof Buffer&&(e=new s(e,8*e.length));const t=8*e.buf.length-e.bitLen,r=Buffer.alloc(e.buf.length+1);return r.writeInt8(t,0),e.buf.copy(r,1),new u(o.UNIVERSAL,a.BITSTRING,r)}static parseBitString(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid BitString");const t=e[0];if(t>7||1===e.length&&t>0||0!=(e[e.length-1]&(1<<e[0])-1))throw new Error("ASN1 syntax error: invalid padding bits in BIT STRING");return new s(e.slice(1),8*(e.length-1)-t)}static Null(){const e=new u(o.UNIVERSAL,a.NULL,Buffer.alloc(0));return e._value=null,e}static parseNull(e){if(!(e instanceof Buffer)||0!==e.length)throw new Error("ASN1 syntax error: invalid null");return null}static OID(e){const t=e.split(".");if(0===t.length)throw new Error("ASN1 syntax error: invalid Object Identifier");const r=[];r.push(40*l(t[0])+l(t[1]));const n=[];for(let e=2;e<t.length;++e){let i=l(t[e]);for(n.length=0,n.push(127&i);i>127;)i>>>=7,n.unshift(127&i|128);r.push(...n)}const i=new u(o.UNIVERSAL,a.OID,Buffer.from(r));return i._value=e,i}static parseOID(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid OID");let t=Math.floor(e[0]/40)+"."+e[0]%40,r=0;for(let n=1;n<e.length;n++)e[n]>=128?(r+=127&e[n],r<<=7):(t+="."+(r+e[n]),r=0);return t}static UTF8(e){const t=new u(o.UNIVERSAL,a.UTF8,Buffer.from(e,"utf8"));return t._value=e,t}static parseUTF8(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static NumericString(e){if(!c(e))throw new Error("ASN1 syntax error: invalid NumericString");const t=new u(o.UNIVERSAL,a.NUMERICSTRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseNumericString(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");const t=e.toString("utf8");if(!c(t))throw new Error("ASN1 syntax error: invalid NumericString");return t}static PrintableString(e){const t=new u(o.UNIVERSAL,a.PRINTABLESTRING,Buffer.from(e,"utf8"));return t._value=e,t}static parsePrintableString(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static IA5String(e){if(!f(e))throw new Error("ASN1 syntax error: invalid IA5String");const t=new u(o.UNIVERSAL,a.IA5STRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseIA5String(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");const t=e.toString("utf8");if(!f(t))throw new Error("ASN1 syntax error: invalid IA5String");return t}static T61String(e){const t=new u(o.UNIVERSAL,a.T61STRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseT61String(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static GeneralString(e){const t=new u(o.UNIVERSAL,a.GENERALSTRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseGeneralString(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static UTCTime(e){let t="";const r=[];r.push((""+e.getUTCFullYear()).substr(2)),r.push(""+(e.getUTCMonth()+1)),r.push(""+e.getUTCDate()),r.push(""+e.getUTCHours()),r.push(""+e.getUTCMinutes()),r.push(""+e.getUTCSeconds());for(const e of r)e.length<2&&(t+="0"),t+=e;t+="Z";const n=new u(o.UNIVERSAL,a.UTCTIME,Buffer.from(t,"utf8"));return n._value=e,n}static parseUTCTime(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid UTC Time");const t=e.toString("utf8"),r=new Date;let n=l(t.substr(0,2));n=n>=50?1900+n:2e3+n;const i=l(t.substr(2,2))-1,o=l(t.substr(4,2)),a=l(t.substr(6,2)),s=l(t.substr(8,2));let u=0,c=0,f="";if(t.length>11&&(c=10,f=t.charAt(c),"+"!==f&&"-"!==f&&(u=l(t.substr(10,2)),c+=2)),r.setUTCFullYear(n,i,o),r.setUTCHours(a,s,u,0),c>0&&(f=t.charAt(c),"+"===f||"-"===f)){let e=60*l(t.substr(c+1,2))+l(t.substr(c+4,2));e*=6e4,"+"===f?r.setTime(+r-e):r.setTime(+r+e)}return r}static GeneralizedTime(e){let t="";const r=[];r.push(""+e.getUTCFullYear()),r.push(""+(e.getUTCMonth()+1)),r.push(""+e.getUTCDate()),r.push(""+e.getUTCHours()),r.push(""+e.getUTCMinutes()),r.push(""+e.getUTCSeconds());for(const e of r)e.length<2&&(t+="0"),t+=e;t+="Z";const n=new u(o.UNIVERSAL,a.GENERALIZEDTIME,Buffer.from(t,"utf8"));return n._value=e,n}static parseGeneralizedTime(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid Generalized Time");const t=e.toString("utf8"),r=new Date,n=l(t.substr(0,4)),i=l(t.substr(4,2))-1,o=l(t.substr(6,2)),a=l(t.substr(8,2)),s=l(t.substr(10,2)),u=l(t.substr(12,2));let c=0,f=0,h=!1;"Z"===t.charAt(t.length-1)&&(h=!0);const p=t.length-5,d=t.charAt(p);if("+"===d||"-"===d){f=60*l(t.substr(p+1,2))+l(t.substr(p+4,2)),f*=6e4,"+"===d&&(f*=-1),h=!0}return"."===t.charAt(14)&&(c=1e3*parseFloat(t.substr(14))),h?(r.setUTCFullYear(n,i,o),r.setUTCHours(a,s,u,c),r.setTime(+r+f)):(r.setFullYear(n,i,o),r.setHours(a,s,u,c)),r}static parseTime(e,t){switch(e){case a.UTCTIME:return u.parseUTCTime(t);case a.GENERALIZEDTIME:return u.parseGeneralizedTime(t);default:throw new Error("Invalid ASN1 time tag")}}static Set(e){const t=new u(o.UNIVERSAL,a.SET,Buffer.concat(e.map(e=>e.toDER())));return t._value=e,t}static Seq(e){const t=new u(o.UNIVERSAL,a.SEQUENCE,Buffer.concat(e.map(e=>e.toDER())));return t._value=e,t}static Spec(e,t,r=!0){const n=Array.isArray(t)?Buffer.concat(t.map(e=>e.toDER())):t.toDER();Array.isArray(t)&&(r=!0);const i=new u(o.CONTEXT_SPECIFIC,e,n,r);return i._value=t,i}static fromDER(e,t=!1){return u._fromDER(new i.BufferVisitor(e),t)}static parseDER(e,t,r){const n=u._fromDER(new i.BufferVisitor(e),!1);if(n.class!==t&&n.tag!==r)throw new Error(`invalid ASN.1 DER for class ${t} and tag ${r}`);return n}static parseDERWithTemplate(e,t){const r=u._fromDER(new i.BufferVisitor(e),!0),n={},o=r.validate(t,n);if(null!=o)throw o.data=r,o;return n}static _parseCompound(e,t){const r=[],n=e.length,o=new i.BufferVisitor(e);let a=0;for(;a<n;){const e=o.end;r.push(u._fromDER(o,t)),a+=o.end-e}return r}static _fromDER(e,t){if(!(e.buf instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid Generalized Time");e.mustWalk(1,"Too few bytes to read ASN.1 tag.");const r=e.start,n=e.buf[r],i=192&n,o=31&n,s=function(e){e.mustWalk(1,"Too few bytes to read ASN.1 value length.");const t=e.buf[e.start];if(0==(128&t))return t;const r=127&t;return e.mustWalk(r,"Too few bytes to read ASN.1 value length."),e.buf.readUIntBE(e.start,r)}(e);if(e.mustHas(s),0!==s&&o===a.NULL)throw new Error("invalid value length or NULL tag.");e.mustWalk(s);const c=32==(32&n),f=new u(i,o,e.buf.slice(e.start,e.end),c);return c&&t&&(f._value=u._parseCompound(f.bytes,t)),f._der=e.buf.slice(r,e.end),f}constructor(e,t,r,n=!1){this.class=e,this.tag=t,this.bytes=r,this.isCompound=n||t===a.SEQUENCE||t===a.SET,this._value=void 0,this._der=null}get value(){return void 0===this._value&&(this._value=this.valueOf()),this._value}get DER(){return null==this._der&&(this._der=this.toDER()),this._der}mustCompound(e="asn1 object value is not compound"){if(!this.isCompound||!Array.isArray(this.value)){const t=new Error(e);throw t.data=this.toJSON(),t}return this.value}equals(e){return e instanceof u&&(this.class===e.class&&this.tag===e.tag&&this.isCompound===e.isCompound&&!!this.bytes.equals(e.bytes))}toDER(){let e=this.class|this.tag;this.isCompound&&(e|=32);const t=function(e){if(e<=127)return 0;if(e<=255)return 1;if(e<=65535)return 2;if(e<=16777215)return 3;if(e<=4294967295)return 4;if(e<=0xffffffffff)return 5;if(e<=0xffffffffffff)return 6;throw new Error("invalid value length")}(this.bytes.length),r=Buffer.allocUnsafe(2+t+this.bytes.length);return r.writeInt8(e,0),0===t?(r.writeUInt8(this.bytes.length,1),this.bytes.copy(r,2)):(r.writeUInt8(128|t,1),r.writeUIntBE(this.bytes.length,2,t),this.bytes.copy(r,2+t)),r}valueOf(){if(this.isCompound)return u._parseCompound(this.bytes,!1);if(this.class!==o.UNIVERSAL)return this.bytes;switch(this.tag){case a.BOOLEAN:return u.parseBool(this.bytes);case a.INTEGER:return u.parseInteger(this.bytes);case a.BITSTRING:return u.parseBitString(this.bytes);case a.NULL:return u.parseNull(this.bytes);case a.OID:return u.parseOID(this.bytes);case a.UTF8:return u.parseUTF8(this.bytes);case a.NUMERICSTRING:return u.parseNumericString(this.bytes);case a.PRINTABLESTRING:return u.parsePrintableString(this.bytes);case a.T61STRING:return u.parseT61String(this.bytes);case a.IA5STRING:return u.parseIA5String(this.bytes);case a.GENERALSTRING:return u.parseGeneralString(this.bytes);case a.UTCTIME:return u.parseUTCTime(this.bytes);case a.GENERALIZEDTIME:return u.parseGeneralizedTime(this.bytes);default:return this.bytes}}validate(e,t={}){if(this.class!==e.class)return new Error(`ASN.1 object validate failure for ${e.name} : error class ${o[this.class]}`);if(!(Array.isArray(e.tag)?e.tag:[e.tag]).includes(this.tag))return new Error(`ASN.1 object validate failure for ${e.name}: error tag ${a[this.tag]}`);if(null!=e.capture&&(t[e.capture]=this),Array.isArray(e.value)){const r=this.mustCompound(e.name+" need compound ASN1 value");for(let n=0,i=0;n<e.value.length;n++)if(null!=r[i]){const o=r[i].validate(e.value[n],t);if(null==o)i++;else if(!0!==e.value[n].optional)return o}else if(!0!==e.value[n].optional)return new Error(`ASN.1 object validate failure for ${e.value[n].name}: not exists`)}else if(null!=e.value){const r=this.tag===a.BITSTRING?this.bytes.slice(1):this.bytes;return u.fromDER(r).validate(e.value,t)}return null}toJSON(){let e=this.value;return Array.isArray(e)&&(e=e.map(e=>e.toJSON())),{class:o[this.class],tag:this.class===o.UNIVERSAL?a[this.tag]:this.tag,value:e}}[n.inspect.custom](e,t){return t.depth<=2&&(t.depth=10),`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}function c(e){for(const t of e){const e=t.charCodeAt(0);if(32!==e&&(e<48||e>57))return!1}return!0}function f(e){for(const t of e)if(t.charCodeAt(0)>=128)return!1;return!0}function l(e,t=10){const r=parseInt(e,t);if(Number.isNaN(r))throw new Error(`Invalid numeric string "${e}" in radix ${t}.`);return r}t.ASN1=u},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const n=r(9),i=r(2),o=r(21),a=r(13),s=r(20),u=Object.create(null);u.CN=a.getOID("commonName"),u.commonName="CN",u.C=a.getOID("countryName"),u.countryName="C",u.L=a.getOID("localityName"),u.localityName="L",u.ST=a.getOID("stateOrProvinceName"),u.stateOrProvinceName="ST",u.O=a.getOID("organizationName"),u.organizationName="O",u.OU=a.getOID("organizationalUnitName"),u.organizationalUnitName="OU",u.E=a.getOID("emailAddress"),u.emailAddress="E";const c={name:"Certificate",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,capture:"tbsCertificate",value:[{name:"Certificate.TBSCertificate.version",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.NONE,optional:!0,value:[{name:"Certificate.TBSCertificate.version.integer",class:o.Class.UNIVERSAL,tag:o.Tag.INTEGER,capture:"certVersion"}]},{name:"Certificate.TBSCertificate.serialNumber",class:o.Class.UNIVERSAL,tag:o.Tag.INTEGER,capture:"certSerialNumber"},{name:"Certificate.TBSCertificate.signature",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate.signature.algorithm",class:o.Class.UNIVERSAL,tag:o.Tag.OID,capture:"certinfoSignatureOID"},{name:"Certificate.TBSCertificate.signature.parameters",class:o.Class.UNIVERSAL,tag:o.Tag.OCTETSTRING,optional:!0,capture:"certinfoSignatureParams"}]},{name:"Certificate.TBSCertificate.issuer",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,capture:"certIssuer"},{name:"Certificate.TBSCertificate.validity",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate.validity.notBefore",class:o.Class.UNIVERSAL,tag:[o.Tag.UTCTIME,o.Tag.GENERALIZEDTIME],capture:"certValidityNotBefore"},{name:"Certificate.TBSCertificate.validity.notAfter",class:o.Class.UNIVERSAL,tag:[o.Tag.UTCTIME,o.Tag.GENERALIZEDTIME],capture:"certValidityNotAfter"}]},{name:"Certificate.TBSCertificate.subject",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,capture:"certSubject"},s.publicKeyValidator,{name:"Certificate.TBSCertificate.issuerUniqueID",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.BOOLEAN,optional:!0,value:[{name:"Certificate.TBSCertificate.issuerUniqueID.id",class:o.Class.UNIVERSAL,tag:o.Tag.BITSTRING,capture:"certIssuerUniqueId"}]},{name:"Certificate.TBSCertificate.subjectUniqueID",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.INTEGER,optional:!0,value:[{name:"Certificate.TBSCertificate.subjectUniqueID.id",class:o.Class.UNIVERSAL,tag:o.Tag.BITSTRING,capture:"certSubjectUniqueId"}]},{name:"Certificate.TBSCertificate.extensions",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.BITSTRING,capture:"certExtensions",optional:!0}]},{name:"Certificate.signatureAlgorithm",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.signatureAlgorithm.algorithm",class:o.Class.UNIVERSAL,tag:o.Tag.OID,capture:"certSignatureOID"},{name:"Certificate.TBSCertificate.signature.parameters",class:o.Class.UNIVERSAL,tag:o.Tag.OCTETSTRING,optional:!0,capture:"certSignatureParams"}]},{name:"Certificate.signatureValue",class:o.Class.UNIVERSAL,tag:o.Tag.BITSTRING,capture:"certSignature"}]};class f{constructor(){this.attributes=[],this.uniqueId=null}get commonName(){return this.getFieldValue("commonName")}get organizationName(){return this.getFieldValue("organizationName")}get organizationalUnitName(){return this.getFieldValue("organizationalUnitName")}get countryName(){return this.getFieldValue("countryName")}get localityName(){return this.getFieldValue("localityName")}get serialName(){return this.getFieldValue("serialName")}getHash(){const e=i.createHash("sha1");for(const t of this.attributes)e.update(t.oid),e.update(t.value);return e.digest()}getField(e){for(const t of this.attributes)if(e===t.oid||e===t.name||e===t.shortName)return t;return null}addField(e){v([e]),this.attributes.push(e)}setAttrs(e){v(e),this.attributes=e}toJSON(){const e={};for(const t of this.attributes){const r=t.shortName;"string"==typeof r&&""!==r&&(e[r]=t.value)}return e.uniqueId=this.uniqueId,e.attributes=this.attributes,e}getFieldValue(e){const t=this.getField(e);return null!=t?t.value:""}}t.DistinguishedName=f;class l{static fromPEMs(e){const t=[],r=o.PEM.parse(e);for(const e of r){if("CERTIFICATE"!==e.type&&"X509 CERTIFICATE"!==e.type&&"TRUSTED CERTIFICATE"!==e.type)throw new Error("Could not convert certificate from PEM: invalid type");if(e.procType.includes("ENCRYPTED"))throw new Error("Could not convert certificate from PEM: PEM is encrypted.");const r=o.ASN1.fromDER(e.body);t.push(new l(r))}if(0===t.length)throw new Error("No Certificate");return t}static fromPEM(e){return l.fromPEMs(e)[0]}constructor(e){const t=Object.create(null),r=e.validate(c,t);if(null!=r)throw new Error("Cannot read X.509 certificate: "+r.message);if(this.raw=e.DER,this.version=null==t.certVersion?0:o.ASN1.parseIntegerNum(t.certVersion.bytes)+1,this.serialNumber=o.ASN1.parseIntegerStr(t.certSerialNumber.bytes),this.signatureOID=o.ASN1.parseOID(t.certSignatureOID.bytes),this.signatureAlgorithm=a.getOIDName(this.signatureOID),this.infoSignatureOID=o.ASN1.parseOID(t.certinfoSignatureOID.bytes),this.signature=o.ASN1.parseBitString(t.certSignature.bytes).buf,this.validFrom=o.ASN1.parseTime(t.certValidityNotBefore.tag,t.certValidityNotBefore.bytes),this.validTo=o.ASN1.parseTime(t.certValidityNotAfter.tag,t.certValidityNotAfter.bytes),this.issuer=new f,this.issuer.setAttrs(_(t.certIssuer)),null!=t.certIssuerUniqueId&&(this.issuer.uniqueId=o.ASN1.parseBitString(t.certIssuerUniqueId.bytes)),this.subject=new f,this.subject.setAttrs(_(t.certSubject)),null!=t.certSubjectUniqueId&&(this.subject.uniqueId=o.ASN1.parseBitString(t.certSubjectUniqueId.bytes)),this.extensions=[],this.subjectKeyIdentifier="",this.authorityKeyIdentifier="",this.ocspServer="",this.issuingCertificateURL="",this.isCA=!1,this.maxPathLen=-1,this.basicConstraintsValid=!1,this.keyUsage=0,this.dnsNames=[],this.emailAddresses=[],this.ipAddresses=[],this.uris=[],null!=t.certExtensions){this.extensions=function(e){const t=[];for(const r of e.mustCompound())for(const e of r.mustCompound())t.push(h(e));return t}(t.certExtensions);for(const e of this.extensions)if("string"==typeof e.subjectKeyIdentifier&&(this.subjectKeyIdentifier=e.subjectKeyIdentifier),"string"==typeof e.authorityKeyIdentifier&&(this.authorityKeyIdentifier=e.authorityKeyIdentifier),"string"==typeof e.authorityInfoAccessOcsp&&(this.ocspServer=e.authorityInfoAccessOcsp),"string"==typeof e.authorityInfoAccessIssuers&&(this.issuingCertificateURL=e.authorityInfoAccessIssuers),"boolean"==typeof e.basicConstraintsValid&&(this.isCA=e.isCA,this.maxPathLen=e.maxPathLen,this.basicConstraintsValid=e.basicConstraintsValid),"number"==typeof e.keyUsage&&(this.keyUsage=e.keyUsage),Array.isArray(e.altNames))for(const t of e.altNames)null!=t.dnsName&&this.dnsNames.push(t.dnsName),null!=t.email&&this.emailAddresses.push(t.email),null!=t.ip&&this.ipAddresses.push(t.ip),null!=t.uri&&this.uris.push(t.uri)}this.publicKey=new s.PublicKey(t.publicKeyInfo),this.publicKeyRaw=this.publicKey.toDER(),this.tbsCertificate=t.tbsCertificate}getExtension(e,t=""){for(const r of this.extensions)if(e===r.oid||e===r.name)return""===t?r:r[t];return null}checkSignature(e){if(3===this.version&&!this.basicConstraintsValid||this.basicConstraintsValid&&!this.isCA)return new Error("The parent constraint violation error");if(!0!==this.getExtension("keyUsage","keyCertSign"))return new Error("The parent constraint violation error");if(!e.isIssuer(this))return new Error("The parent certificate did not issue the given child certificate");const t=function(e){switch(a.getOIDName(e)){case"sha1WithRsaEncryption":return"sha1";case"md5WithRsaEncryption":return"md5";case"sha256WithRsaEncryption":return"sha256";case"sha384WithRsaEncryption":return"sha384";case"sha512WithRsaEncryption":return"sha512";case"RSASSA-PSS":return"sha256";case"ecdsaWithSha1":return"sha1";case"ecdsaWithSha256":return"sha256";case"ecdsaWithSha384":return"sha384";case"ecdsaWithSha512":return"sha512";case"dsaWithSha1":return"sha1";case"dsaWithSha256":return"sha256";default:return""}}(e.signatureOID);if(""===t)return new Error("Unknown child signature OID.");return!1===this.publicKey.verify(e.tbsCertificate.DER,e.signature,t)?new Error("Child signature not matched"):null}isIssuer(e){return this.issuer.getHash().equals(e.subject.getHash())}verifySubjectKeyIdentifier(){return this.publicKey.getFingerprint("sha1","PublicKey").toString("hex")===this.subjectKeyIdentifier}toJSON(){const e={};for(const t of Object.keys(this))e[t]=m(this[t]);return delete e.tbsCertificate,e}[n.inspect.custom](e,t){return t.depth<=2&&(t.depth=10),`<${this.constructor.name} ${n.inspect(this.toJSON(),t)}>`}}function h(e){const t={};switch(t.oid=o.ASN1.parseOID(e.value[0].bytes),t.critical=!1,e.value[1].tag===o.Tag.BOOLEAN?(t.critical=o.ASN1.parseBool(e.value[1].bytes),t.value=e.value[2].bytes):t.value=e.value[1].bytes,t.name=a.getOIDName(t.oid),t.name){case"keyUsage":!function(e){const t=o.ASN1.parseBitString(o.ASN1.fromDER(e.value).bytes);let r=0,n=0;e.keyUsage=0;for(let r=0;r<9;r++)0!==t.at(r)&&(e.keyUsage|=1<<r);t.buf.length>0&&(r=t.buf[0],n=t.buf.length>1?t.buf[1]:0);e.digitalSignature=128==(128&r),e.nonRepudiation=64==(64&r),e.keyEncipherment=32==(32&r),e.dataEncipherment=16==(16&r),e.keyAgreement=8==(8&r),e.keyCertSign=4==(4&r),e.cRLSign=2==(2&r),e.encipherOnly=1==(1&r),e.decipherOnly=128==(128&n)}(t);break;case"basicConstraints":!function(e){const t=o.ASN1.fromDER(e.value).mustCompound();t.length>0&&t[0].tag===o.Tag.BOOLEAN?e.isCA=o.ASN1.parseBool(t[0].bytes):e.isCA=!1;let r=null;t.length>0&&t[0].tag===o.Tag.INTEGER?r=t[0].bytes:t.length>1&&(r=t[1].bytes);e.maxPathLen=null!==r?o.ASN1.parseInteger(r):-1;e.basicConstraintsValid=!0}(t);break;case"extKeyUsage":!function(e){const t=o.ASN1.fromDER(e.value).mustCompound();for(const r of t)e[a.getOIDName(o.ASN1.parseOID(r.bytes))]=!0}(t);break;case"nsCertType":!function(e){const t=o.ASN1.parseBitString(o.ASN1.fromDER(e.value).bytes);let r=0;t.buf.length>0&&(r=t.buf[0]);e.client=128==(128&r),e.server=64==(64&r),e.email=32==(32&r),e.objsign=16==(16&r),e.reserved=8==(8&r),e.sslCA=4==(4&r),e.emailCA=2==(2&r),e.objCA=1==(1&r)}(t);break;case"subjectAltName":case"issuerAltName":p(t);break;case"subjectKeyIdentifier":!function(e){const t=o.ASN1.parseDERWithTemplate(e.value,d);e.subjectKeyIdentifier=t.subjectKeyIdentifier.bytes.toString("hex")}(t);break;case"authorityKeyIdentifier":!function(e){const t=o.ASN1.parseDERWithTemplate(e.value,y);e.authorityKeyIdentifier=t.authorityKeyIdentifier.bytes.toString("hex")}(t);break;case"authorityInfoAccess":!function(e){const t=o.ASN1.parseDERWithTemplate(e.value,g);null!=t.authorityInfoAccessOcsp&&(e.authorityInfoAccessOcsp=t.authorityInfoAccessOcsp.bytes.toString());null!=t.authorityInfoAccessIssuers&&(e.authorityInfoAccessIssuers=t.authorityInfoAccessIssuers.bytes.toString())}(t)}return t}function p(e){e.altNames=[];const t=o.ASN1.fromDER(e.value).mustCompound();for(const r of t){const t={tag:r.tag,value:r.bytes};switch(e.altNames.push(t),r.tag){case 1:t.email=r.bytes.toString();break;case 2:t.dnsName=r.bytes.toString();break;case 6:t.uri=r.bytes.toString();break;case 7:t.ip=a.bytesToIP(r.bytes);break;case 8:t.oid=o.ASN1.parseOID(r.bytes)}}}t.Certificate=l;const d={name:"subjectKeyIdentifier",class:o.Class.UNIVERSAL,tag:o.Tag.OCTETSTRING,capture:"subjectKeyIdentifier"};const y={name:"authorityKeyIdentifier",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"authorityKeyIdentifier.value",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.NONE,capture:"authorityKeyIdentifier"}]};const g={name:"authorityInfoAccess",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"authorityInfoAccess.authorityInfoAccessOcsp",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,optional:!0,value:[{name:"authorityInfoAccess.authorityInfoAccessOcsp.oid",class:o.Class.UNIVERSAL,tag:o.Tag.OID},{name:"authorityInfoAccess.authorityInfoAccessOcsp.value",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.OID,capture:"authorityInfoAccessOcsp"}]},{name:"authorityInfoAccess.authorityInfoAccessIssuers",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,optional:!0,value:[{name:"authorityInfoAccess.authorityInfoAccessIssuers.oid",class:o.Class.UNIVERSAL,tag:o.Tag.OID},{name:"authorityInfoAccess.authorityInfoAccessIssuers.value",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.OID,capture:"authorityInfoAccessIssuers"}]}]};function v(e){for(const t of e){if(null!=t.name&&""!==t.name||(null!=t.oid&&(t.name=a.getOIDName(t.oid)),""===t.name&&null!=t.shortName&&(t.name=a.getOIDName(u[t.shortName]))),null==t.oid||""===t.oid){if(""===t.name)throw new Error("Attribute oid not specified.");t.oid=a.getOID(t.name)}if(null!=t.shortName&&""!==t.shortName||(t.shortName=null==u[t.name]?"":u[t.name]),null==t.value)throw new Error("Attribute value not specified.")}}function _(e){const t=[];for(const n of e.mustCompound())for(const e of n.mustCompound()){const n=e.mustCompound(),i={};i.oid=o.ASN1.parseOID(n[0].bytes),i.value=n[1].value,i.valueTag=n[1].tag,i.name=a.getOIDName(i.oid),i.shortName=(r=i.name,null==u[r]?"":u[r]),t.push(i)}var r;return t}function m(e){return null==e||e instanceof Buffer||"function"!=typeof e.toJSON?e:e.toJSON()}},function(e){e.exports=JSON.parse('{"name":"alipay-sdk","version":"3.2.0","description":"蚂蚁金服开放平台 node sdk","main":"lib/alipay.js","scripts":{"build":"npm run tsc","tsc":"tsc -p ./tsconfig.json","tsc:watch":"tsc -w","lint":"tslint -p ./tsconfig.json --fix","lint:no-fix":"tslint -p ./tsconfig.json","test":"npm run build && mocha","ci":"npm run tsc && npm run lint:no-fix && nyc mocha -t 6000","prepublishOnly":"npm run tsc && npm run test"},"author":"dersoncheng","homepage":"https://github.com/ali-sdk/alipay-sdk","bugs":"https://github.com/ali-sdk/alipay-sdk/issues","license":"ISC","publishConfig":{"registry":"https://registry.npmjs.org"},"dependencies":{"@fidm/x509":"^1.2.1","@types/node":"^9.6.0","bignumber.js":"^9.0.0","camelcase-keys":"^4.2.0","crypto-js":"^4.0.0","decamelize":"^2.0.0","is":"^3.2.1","is-json":"^2.0.1","isuri":"^2.0.3","lodash":"^4.17.20","moment":"^2.16.0","request":"^2.86.0","snakecase-keys":"^1.1.1","urllib":"^2.17.0"},"nyc":{"extends":"@istanbuljs/nyc-config-typescript","include":["lib"],"extension":[".ts"],"check-coverage":true,"reporter":["text-summary","json","html"],"sourceMap":true},"ci":{"version":"8, 10, 12, 14"},"devDependencies":{"@istanbuljs/nyc-config-typescript":"^0.1.3","mocha":"^3.1.2","nyc":"^14.1.1","query-string":"^6.5.0","should":"^11.1.1","sinon":"^1.17.7","tslint":"^5.8.0","tslint-config-airbnb":"^5.4.2","typescript":"3.5.1"}}')},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=r(3);function i(e,t,r){t.forEach(t=>{void 0!==e[t]&&(e[t]=(0,n.accMul)(Number(e[t]),r))})}function o(e,t){t.forEach(t=>{void 0!==e[t]&&(e[t]=e[t].replace(/[-+:\s]/g,""))})}var a={unifiedOrder:{args:{_pre:e=>(i(e,["totalFee"],.01),e),totalAmount:"totalFee",buyerId:"openid"},returnValue:{transactionId:"tradeNo"}},getOrderInfo:{args:{_pre:e=>(i(e,["totalFee"],.01),e),buyerId:"openid",totalAmount:"totalFee"},returnValue:e=>("object"==typeof e&&e.qrCode&&(e.codeUrl=e.qrCode,delete e.qrCode),e)},orderQuery:{args:{tradeNo:"transactionId"},returnValue:{_pre:e=>(i(e,["totalAmount","settleAmount","buyerPayAmount","payAmount","pointAmount","invoiceAmount","receiptAmount","chargeAmount","mdiscountAmount","discountAmount"],100),o(e,["sendPayDate"]),e),transactionId:"tradeNo",openid:"buyerUserId",tradeState:function(e){switch(e.tradeStatus){case"WAIT_BUYER_PAY":return"USERPAYING";case"TRADE_CLOSED":return"CLOSED";case"TRADE_SUCCESS":return"SUCCESS";case"TRADE_FINISHED":return"FINISHED";default:return e.tradeStatus}},totalFee:"totalAmount",settlementTotalFee:"settleAmount",feeType:"transCurrency",cashFeeType:"payCurrency",cashFee:"buyerPayAmount",fundBillList:function(e){return e.fundBillList?e.fundBillList.map(e=>(e.amount=100*Number(e.amount),e.realAmount=100*Number(e.realAmount),e)):[]},tradeSettleDetailList:function(e){return e.tradeSettleDetailList?e.tradeSettleDetailList.map(e=>(e.amount=100*Number(e.amount),e)):[]},timeEnd:"sendPayDate",_purify:{shouldDelete:["tradeStatus"]}}},cancelOrder:{args:{tradeNo:"transactionId"},returnValue:{transactionId:"tradeNo"}},closeOrder:{args:{tradeNo:"transactionId"},returnValue:{transactionId:"tradeNo"}},refund:{args:{_pre:e=>(i(e,["refundFee","sendBackFee"],.01),e),tradeNo:"transactionId",refundAmount:"refundFee",outRequestNo:"outRefundNo",refundCurrency:"refundFeeType",refundReason:"refundDesc",goodsDetail:function(e){return e.goodsDetail?e.goodsDetail.map(e=>(e.price=Number(e.price)/100,e)):[]},refundRoyaltyParameters:function(e){return e.refundRoyaltyParameters?e.refundRoyaltyParameters.map(e=>(e.amount=Number(e.amount)/100,e)):[]}},returnValue:{_pre:e=>(i(e,["refundFee","presentRefundBuyerAmount","presentRefundDiscountAmount","presentRefundMdiscountAmount"],100),e),transactionId:"tradeNo",openid:"buyerUserId",cashRefundFee:"presentRefundBuyerAmount",refundId:"refundSettlementId",cashFeeType:"refundCurrency",refundDetailItemList:function(e){return e.refundDetailItemList?e.refundDetailItemList.map(e=>(e.amount=100*Number(e.amount),e.realAmount=100*Number(e.realAmount),e)):[]},refundPresetPaytoolList:function(e){return e.refundPresetPaytoolList?e.refundPresetPaytoolList.map(e=>(e.amount=100*Number(e.amount),e)):[]}}},refundQuery:{args:{tradeNo:"transactionId",outRequestNo:"outRefundNo"},returnValue:{_pre:e=>(i(e,["totalAmount","refundAmount","sendBackFee","presentRefundBuyerAmount","presentRefundBuyerAmount","presentRefundMdiscountAmount"],100),e),transactionId:"tradeNo",outRefundNo:"outRequestNo",totalFee:"totalAmount",refundFee:"refundAmount",refundDesc:"refundReason",refundId:"refundSettlementId",refundSuccessTime:"gmtRefundPay",refundRoyaltys:function(e){return e.refundRoyaltys?e.refundRoyaltys.map(e=>(e.refundAmount=100*Number(e.refundAmount),e)):[]},refundDetailItemList:function(e){return e.refundDetailItemList?e.refundDetailItemList.map(e=>(e.amount=100*Number(e.amount),e.realAmount=100*Number(e.realAmount),e)):[]}}},verifyPaymentNotify:{returnValue:{_pre:e=>(i(e,["invoiceAmount","receiptAmount","buyerPayAmount","totalAmount","pointAmount"],100),o(e,["gmtPayment"]),e),openid:"buyerId",transactionId:"tradeNo",totalFee:"totalAmount",cashFee:"buyerPayAmount",timeEnd:"gmtPayment",resultCode:function(e){return e.tradeStatus.replace("TRADE_","")},fundBillList:function(e){return e.fundBillList?JSON.parse(e.fundBillList).map(e=>(e.amount=100*Number(e.amount),e)):[]}}},verifyRefundNotify:{returnValue:{_pre:e=>(i(e,["totalAmount","refundFee"],100),e),openid:"buyerId",outRefundNo:"outBizNo",transactionId:"tradeNo",totalFee:"totalAmount",successTime:function(e){e.successTime=e.gmtRefund.replace("+","").split(".")[0]},resultCode:function(e){return e.tradeStatus.replace("TRADE_","")},fundBillList:function(e){return e.fundBillList?JSON.parse(e.fundBillList).map(e=>(e.amount=100*Number(e.amount),e)):[]},_purify:{shouldDelete:["gmtRefund"]}}}};t.default=a,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default=class{constructor(e){e.sandbox&&(e.gateway="https://sandbox.itunes.apple.com/verifyReceipt"),e=Object.assign({gateway:"https://buy.itunes.apple.com/verifyReceipt",timeout:5e3,password:""},e),this.options=e}async _request(e){const t={method:"POST",contentType:"json",dataType:"json",data:e,timeout:this.options.timeout},{status:r,data:n}=await uniCloud.httpclient.request(this.options.gateway,t);if(200!==r)throw new Error("request fail");return this._parse(n)}_parse(e){const t=this._tradeState(e.status);return 0===e.status?{transactionId:e.receipt.transaction_id,receipt:e.receipt,...t}:t}_tradeState(e){let t="PAYERROR",r="";switch(e){case-1:t="NOTPAY";break;case 0:t="SUCCESS";break;default:r="Error status ["+e+"]"}return{tradeState:t,errMsg:r}}async verifyReceipt(e){const t=(e={"receipt-data":e.receiptData}).password||this.options.password;t&&(e.password=t);return await this._request(e)}},e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(85)),i=s(r(86)),o=s(r(87)),a=r(3);function s(e){return e&&e.__esModule?e:{default:e}}function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}var c=class extends class{constructor(e){if(this.options=e,!e.appId)throw new Error("appId required");if(!e.mchId)throw new Error("mchId required");if(!e.v3Key)throw new Error("v3Key required");if(!e.appPrivateKeyPath&&!e.appPrivateKeyContent)throw new Error("missing appPrivateKeyPath or appPrivateKeyContent");if(!e.appCertPath&&!e.appCertContent)throw new Error("missing appCertPath or appCertContent");this._protocols=o.default,this.platformCertificate=[],this._baseURL="https://api.mch.weixin.qq.com",this._cert=e.appCertPath?i.default.loadCertFromPath(e.appCertPath):i.default.loadCertFromContent(i.default.formatKey(e.appCertContent,"CERTIFICATE")),this._privateKey=e.appPrivateKeyPath?i.default.loadPrivateKeyFromPath(e.appPrivateKeyPath):i.default.loadPrivateKeyFromContent(i.default.formatKey(e.appPrivateKeyContent,"PRIVATE KEY"))}_getAuthorization(e="GET",t="",r={}){const n=i.default.getNonceStr(),o=Date.parse(new Date)/1e3,a=[e,"GET"===e&&Object.keys(r).length>0?`${t}?${i.default.getQueryStr(r)}`:t,o,n,"GET"===e?"":JSON.stringify(r)].reduce((e,t)=>e+=t+"\n",""),s=i.default.rsaPrivateKeySign(this._privateKey.toPEM(),a).toString("base64"),u=this._cert.serialNumber.toUpperCase();return`WECHATPAY2-SHA256-RSA2048 mchid="${this.options.mchId}",nonce_str="${n}",signature="${s}",timestamp="${o}",serial_no="${u}"`}async _request(e,t,r="GET",n=200){t=(0,a.camel2snakeJson)(t);const i=this._getAuthorization(r,e,t);let{status:o,data:s={},headers:u}=await uniCloud.httpclient.request(`${this._baseURL}${e}`,{method:r,data:t,headers:{Accept:"application/json","content-type":"application/json",Authorization:i},dataType:"json",timeout:this.options.timeout});if(o!==n)throw new a.UniCloudError({code:s.code,message:s.message});return s||(s={}),await this._verifyResponseSign(u,s),s.appid&&(s.appId=s.appid),s.mchid&&(s.mchId=s.mchid),(0,a.snake2camelJson)(s)}_publicParams(e){const t={appid:this.options.appId,mchid:this.options.mchId};return Object.assign(t,e)}_getPayParamsByPrepayId(e,t){let r;switch(t){case"APP":r={appid:this.options.subAppId?this.options.subAppId:this.options.appId,partnerid:this.options.mchId,prepayid:e,package:"Sign=WXPay",noncestr:i.default.getNonceStr(),timestamp:""+(Date.now()/1e3|0)},r.sign=this._clientPaySign(r,t);break;case"JSAPI":default:{const n=""+(Date.now()/1e3|0);r={appId:this.options.subAppId?this.options.subAppId:this.options.appId,nonceStr:i.default.getNonceStr(),package:"prepay_id="+e,timeStamp:n},r.signType="RSA",r.paySign=this._clientPaySign(r,t),r.timestamp=n;break}}return r}_clientPaySign(e,t){const r=[e.appid||e.appId,e.timestamp||e.timeStamp,e.noncestr||e.nonceStr,"JSAPI"===t?e.package:e.prepayid].reduce((e,t)=>e+=t+"\n","");return i.default.rsaPrivateKeySign(this._privateKey.toPEM(),r).toString("base64")}async _getPlatformCert(){if(this.platformCertificate.length<=0){var e,t;const r="/v3/certificates",{status:n,data:o}=await uniCloud.httpclient.request(`${this._baseURL}${r}`,{method:"GET",headers:{Accept:"application/json","content-type":"application/json",Authorization:this._getAuthorization("GET",r)},dataType:"json",timeout:this.options.timeout});if(200!==n)throw new Error("request fail");this.platformCertificate=null!==(e=null===(t=o.data)||void 0===t?void 0:t.reduce((e,t)=>{if(t.encrypt_certificate){const{nonce:e,associated_data:r,ciphertext:n}=t.encrypt_certificate,o=i.default.decryptCiphertext(n,this.options.v3Key,e,r);t.certificate=i.default.loadCertFromContent(o)}return e.push(t),e},[]))&&void 0!==e?e:[]}return this.platformCertificate=this.platformCertificate.filter(e=>new Date(e.expire_time).getTime()>Date.now()),this.platformCertificate[0]}async _verifyResponseSign(e,t={}){const r=await this._getPlatformCert(),{"wechatpay-timestamp":n,"wechatpay-nonce":o,"wechatpay-signature":a}=e,s=[n,o,Object.keys(t).length?JSON.stringify(t):""].reduce((e,t)=>e+=t+"\n","");if(!i.default.rsaPublicKeyVerifySign(r.certificate.publicKey.toPEM(),s,a))throw new Error("response signature verification failed")}_downloadFile(e){const t=n.parse(e);return uniCloud.httpclient.request(e,{method:"GET",headers:{Accept:"application/json","content-type":"application/json",Authorization:this._getAuthorization("GET",t.path)},dataType:"text",timeout:this.options.timeout})}}{async getOrderInfo(e){if((e=this._publicParams(e)).sceneInfo.payerClientIp=e.sceneInfo.payerClientIp||"127.0.0.1","JSAPI"!==e.tradeType&&delete e.openid,!e.tradeType)throw new Error("tradeType required");const{tradeType:t,...r}=e,n=await this._request("/v3/pay/transactions/"+("MWEB"===e.tradeType?"h5":e.tradeType.toLowerCase()),r,"POST");if("NATIVE"===e.tradeType||"MWEB"===e.tradeType)return n;if(!n.prepayId)throw new Error(n.errMsg||"获取prepayId失败");return this._getPayParamsByPrepayId(n.prepayId,e.tradeType)}async orderQuery(e){var t;const r=await this._request(e.transactionId?"/v3/pay/transactions/id/"+e.transactionId:"/v3/pay/transactions/out-trade-no/"+e.outTradeNo,{mchid:this.options.mchId});if(r.settlementTotalFee=0,(null===(t=r.promotion_detail)||void 0===t?void 0:t.length)>0){const e=r.promotion_detail.reduce((e,t)=>("NOCASH"===t.type&&(e+=t.amount),e),0);r.settlementTotalFeeres=r.amount.total-e}return r}async closeOrder(e){return await this._request(`/v3/pay/transactions/out-trade-no/${e.outTradeNo}/close`,{mchid:this.options.mchId},"POST",204)}async refund(e){return await this._request("/v3/refund/domestic/refunds",e,"POST")}async refundQuery(e){return await this._request("/v3/refund/domestic/refunds/"+e.outRefundNo)}async downloadBill(e){return this._request("/v3/bill/tradebill",e).then(e=>this._downloadFile(e.downloadUrl)).then(e=>Promise.resolve({content:e.data}))}async downloadFundflow(e){return this._request("/v3/bill/fundflowbill",e).then(e=>this._downloadFile(e.downloadUrl)).then(e=>Promise.resolve({content:e.data}))}async checkNotifyType(e){const{headers:t}=e,r="string"==typeof e.body?JSON.parse(e.body):e.body;await this._verifyResponseSign(t,r);const{resource:n}=r;switch(null==n?void 0:n.original_type){case"transaction":default:return"payment";case"refund":return"refund"}}async verifyPaymentNotify(e){const{headers:t}=e,r="string"==typeof e.body?JSON.parse(e.body):e.body;await this._verifyResponseSign(t,r);const{resource:n}=r,o=i.default.decryptCiphertext(n.ciphertext,this.options.v3Key,n.nonce,n.associated_data);return(0,a.snake2camelJson)(JSON.parse(o))}async verifyRefundNotify(e){const{headers:t}=e,r="string"==typeof e.body?JSON.parse(e.body):e.body;await this._verifyResponseSign(t,r);const{resource:n}=r,o=i.default.decryptCiphertext(n.ciphertext,this.options.v3Key,n.nonce,n.associated_data);return(0,a.snake2camelJson)(JSON.parse(o))}};t.default=c,e.exports=t.default},function(e,t){e.exports=require("url")},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n},o=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=u(t);if(r&&r.has(e))return r.get(e);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if("default"!==o&&Object.prototype.hasOwnProperty.call(e,o)){var a=i?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}n.default=e,r&&r.set(e,n);return n}(r(10)),a=r(19),s=r(3);function u(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(u=function(e){return e?r:t})(e)}var c={decryptData:function(e,t,r=""){const n=i.default.createDecipheriv("aes-256-ecb",t,r);n.setAutoPadding(!0);let o=n.update(e,"base64","utf8");return o+=n.final("utf8"),o},md5:function(e,t="utf8"){return i.default.createHash("md5").update(e,t).digest("hex")},sha256:function(e,t,r="utf8"){return i.default.createHmac("sha256",t).update(e,r).digest("hex")},getQueryStr:function(e){return Object.keys(e).map(t=>t+"="+((0,s.isPlainObject)(e[t])?JSON.stringify(e[t]):e[t])).join("&")},getNonceStr:function(e=16){let t="";for(;t.length<e;)t+=Math.random().toString(32).substring(2);return t.substring(0,e)},decodeBase64:function(e){return Buffer.from(e,"base64").toString("utf-8")},decryptCiphertext:function(e,t,r,n){const o=Buffer.from(e,"base64"),a=i.default.createDecipheriv("aes-256-gcm",t,r);return a.setAuthTag(o.slice(-16)),a.setAAD(Buffer.from(n)),Buffer.concat([a.update(o.slice(0,-16)),a.final()]).toString()},loadCertFromPath:function(e){return a.Certificate.fromPEM(o.readFileSync(e))},loadCertFromContent:function(e){return"string"==typeof e&&(e=Buffer.from(e)),a.Certificate.fromPEM(e)},loadPrivateKeyFromPath:function(e){return a.PrivateKey.fromPEM(o.readFileSync(e))},loadPrivateKeyFromContent:function(e){return"string"==typeof e&&(e=Buffer.from(e)),a.PrivateKey.fromPEM(e)},rsaPrivateKeySign:function(e,t,r="base64"){return i.default.createSign("RSA-SHA256").update(Buffer.from(t),"utf8").sign(e)},rsaPublicKeyVerifySign:function(e,t,r){return i.default.createVerify("RSA-SHA256").update(t,"utf8").verify(e,r,"base64")},rsaPublicKeyEncryptData:function(e,t){return i.default.publicEncrypt({key:t,padding:i.default.constants.RSA_PKCS1_OAEP_PADDING},Buffer.from(e))},rsaPrivateKeyDecryptData:function(e,t){return"string"==typeof e&&(e=Buffer.from(e)),i.default.privateDecrypt({key:t,padding:i.default.constants.RSA_PKCS1_OAEP_PADDING},e).toString()},formatKey:function(e,t){return`-----BEGIN ${t}-----\n${e}\n-----END ${t}-----`}};t.default=c,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={getOrderInfo:{args:{_purify:{shouldDelete:["subject"]},"amount.total":"totalFee","payer.openid":"openid",description:"body","sceneInfo.payerClientIp":"spbillCreateIp"}},orderQuery:{returnValue:{_purify:{shouldDelete:["appid","mchid","sceneInfo","promotionDetail"]},totalFee:"amount.total",cashFee:"amount.payer_total"}},refund:{args:{"amount.total":"totalFee","amount.refund":"refundFee","amount.currency":"refundFeeType",reason:"refundDesc"},returnValue:{refundFee:"amount.refund",cashRefundFee:"amount.payer_refund"}},refundQuery:{args:{shouldDelete:["outTradeNo","transactionId","refundId"]},returnValue:{totalFee:"amount.total",refundFee:"amount.refund"}},downloadFundflow:{args:{accountType:e=>e.accountType.toUpperCase()}},verifyPaymentNotify:{returnValue:{totalFee:"amount.total",cashFee:"amount.payer_total",feeType:"amount.currency",timeEnd:"success_time",openid:"payer.openid",returnCode:"trade_state"}},verifyRefundNotify:{returnValue:{totalFee:"amount.total",refundFee:"amount.refund",settlementTotalFee:"amount.payer_total",settlementRefundFee:"amount.payer_refund",refundRecvAccout:"user_received_account"}}},e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=s(r(89)),i=s(r(90)),o=r(3),a=s(r(91));function s(e){return e&&e.__esModule?e:{default:e}}var u=class extends class{constructor(e){if(this.options={currencyType:"CNY",rate:100,timeout:1e4,...e},!e.appId)throw new Error("appId required");if(!e.mchId)throw new Error("mchId required");if(!e.sandbox&&!e.appKey)throw new Error("appKey required");if(e.sandbox&&!e.sandboxAppKey)throw new Error("sandboxAppKey required");if(!e.offerId)throw new Error("offerId required");if(!e.accessToken)throw new Error("accessToken required");if(!e.token)throw new Error("token required");if(!e.encodingAESKey)throw new Error("encodingAESKey required");this.appKey=e.sandbox?e.sandboxAppKey:e.appKey,this._protocols=i.default,this._baseURL="https://api.weixin.qq.com",this._wxCrypto=new a.default({appId:e.appId,encodingAESKey:e.encodingAESKey,token:e.token})}async _request(e,t,r="GET",i=200){t=this._publicParams(t),t=(0,o.camel2snakeJson)(t),t=JSON.parse(JSON.stringify(t)),this.options.debug&&console.log("params",t);const a=JSON.stringify(t),s=this.appKey,u=n.default.getPaySig(e,a,s),c=this.options.accessToken;let f=`${this._baseURL}${e}?access_token=${c}&pay_sig=${u}`;if(t.session_key){f+="&signature="+n.default.getSignature(a,t.session_key)}const{status:l,data:h={}}=await uniCloud.httpclient.request(f,{method:r,data:t,headers:{"content-type":"application/json"},dataType:"json",timeout:this.options.timeout});if(l!==i)throw new o.UniCloudError({code:h.code,message:h.message});if(h.errcode&&(h.errCode=h.errcode),h.errmsg&&(h.errMsg=h.errmsg),delete h.errcode,delete h.errmsg,h.errCode||(h.errCode=0),h.code=h.errCode,h.errMsg&&(h.msg=h.errMsg),h.errCode)throw new o.UniCloudError({code:h.errCode,message:h.errMsg});const p=(0,o.snake2camelJson)(h);return p.appId||(p.appId=this.options.appId),p.mchId||(p.mchId=this.options.mchId),this.options.debug&&console.log("res",p),p}_publicParams(e){const t={env:this.options.sandbox?1:0};return Object.assign(t,e)}async _verifyResponseSign(e){const{queryStringParameters:t={}}=e,{signature:r,timestamp:n,nonce:i}=t;return this._wxCrypto.verifyResponseSign({signature:r,timestamp:n,nonce:i})}async _decrypt(e={}){const{msgSignature:t,timestamp:r,nonce:n,body:i={}}=e,{Encrypt:o}=i,a=this._wxCrypto;if(!a.verifyMsgSign({timestamp:r,nonce:n,encrypt:o,msg_signature:t}))return null;if(!o)return i;const s=a.decrypt(o);return this.options.appId!==s.appId?null:s.value}}{async getOrderInfo(e){e=this._publicParams(e);const{sessionKey:t,mode:r,outTradeNo:i,buyQuantity:o,productId:a,goodsPrice:s,attach:u}=e;if(!r)throw new Error("mode required");if("short_series_goods"===r){if(!a)throw new Error("productId required");if(!s)throw new Error("goodsPrice required")}if(!t)throw new Error("sessionKey required");if(!i)throw new Error("outTradeNo required");if(!o)throw new Error("buyQuantity required");const c=JSON.stringify({offerId:this.options.offerId,env:this.options.sandbox?1:0,currencyType:this.options.currencyType,buyQuantity:o,outTradeNo:i,productId:a,goodsPrice:s,attach:u}),f=this.appKey;return{signData:c,mode:r,paySig:n.default.getPaySig("requestVirtualPayment",c,f),signature:n.default.getSignature(c,t)}}async orderQuery(e){return await this._request("/xpay/query_order",e,"POST")}async closeOrder(e){return{}}async refund(e){const t=await this._request("/xpay/refund_order",e,"POST");return"OK"===t.errMsg&&(t.refundFee=e.refundFee),t}async refundQuery(e){return await this._request("/xpay/query_order",e,"POST")}checkNotifyType(e){let{body:t={},queryStringParameters:r={}}=e;if(e.isBase64Encoded&&(t=Buffer.from(t,"base64").toString("utf-8")),t&&"string"==typeof t)try{t=JSON.parse(t)}catch(e){t={}}const{Event:n}=t;return!n&&r.echostr?"token":["xpay_coin_pay_notify","xpay_goods_deliver_notify"].indexOf(n)>-1?"payment":["xpay_refund_notify"].indexOf(n)>-1?"refund":"unknown"}async verifyTokenNotify(e){const{queryStringParameters:t={}}=e,{echostr:r}=t;if(!await this._verifyResponseSign(e))return null;return{appId:this.options.appId,mchId:this.options.mchId,echostr:r}}async _verifyNotify(e){let{body:t={}}=e;if(e.isBase64Encoded&&(t=Buffer.from(t,"base64").toString("utf-8")),"string"==typeof t)try{t=JSON.parse(t)}catch(e){}if(!await this._verifyResponseSign(e))return null;const{queryStringParameters:r={}}=e,{msg_signature:n,timestamp:i,nonce:a}=r,s=this._decrypt({msgSignature:n,timestamp:i,nonce:a,body:t}),u=(0,o.snake2camelJson)(s);return u.appId=this.options.appId,u.mchId=this.options.mchId,u}async verifyPaymentNotify(e){return"payment"===this.checkNotifyType(e)&&await this._verifyNotify(e)}async verifyRefundNotify(e){return"refund"===this.checkNotifyType(e)&&await this._verifyNotify(e)}async notifyProvideGoods(e){return await this._request("/xpay/notify_provide_goods",e,"POST")}async queryUserBalance(e){if(!e.openid)throw new Error("参数 openid 必填");if(!e.userIp)throw new Error("参数 userIp 必填");return await this._request("/xpay/query_user_balance",e,"POST")}async currencyPay(e){if(!e.sessionKey)throw new Error("接口 currencyPay 的参数 sessionKey 必填");if(!e.openid)throw new Error("参数 openid 必填");if(!e.userIp)throw new Error("参数 userIp 必填");if(!e.amount)throw new Error("参数 amount 必填");if(!e.orderId)throw new Error("参数 outTradeNo 必填");if(!e.deviceType)throw new Error("参数 deviceType 必填");return await this._request("/xpay/currency_pay",e,"POST")}async cancelCurrencyPay(e){if(!e.openid)throw new Error("参数 openid 必填");if(!e.userIp)throw new Error("参数 userIp 必填");if(!e.amount)throw new Error("参数 amount 必填");if(!e.payOrderId)throw new Error("参数 outTradeNo 必填");if(!e.orderId)throw new Error("参数 outRefundNo 必填");if(!e.deviceType)throw new Error("参数 deviceType 必填");return await this._request("/xpay/cancel_currency_pay",e,"POST")}async presentCurrency(e){if(!e.openid)throw new Error("参数 openid 必填");if(!e.userIp)throw new Error("参数 userIp 必填");if(!e.orderId)throw new Error("参数 outTradeNo 必填");if(!e.amount)throw new Error("参数 amount 必填");if(!e.deviceType)throw new Error("参数 deviceType 必填");return await this._request("/xpay/present_currency",e,"POST")}};t.default=u,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n};function o(e,t,r="utf8"){return i.default.createHmac("sha256",t).update(e,r).digest("hex")}var a={sha256:o,getPaySig:function(e,t,r){return o(e+"&"+t,r)},getSignature:function(e,t){return o(e,t)}};t.default=a,e.exports=t.default},function(e,t,r){"use strict";function n(e,t="Asia/Shanghai"){const r=function(e){const t=(new Date).getTimezoneOffset();return"UTC"===e?0:-t}(t),n=new Date(e+60*r*1e3);return`${n.getUTCFullYear()}${(n.getUTCMonth()+1).toString().padStart(2,"0")}${n.getUTCDate().toString().padStart(2,"0")}${n.getUTCHours().toString().padStart(2,"0")}${n.getUTCMinutes().toString().padStart(2,"0")}${n.getUTCSeconds().toString().padStart(2,"0")}`}function i(e){const t={};return Object.keys(e).forEach(r=>{const n=r.charAt(0).toLowerCase()+r.slice(1);t[n]=e[r]}),t}Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o={getOrderInfo:{args:{_purify:{shouldDelete:["subject","tradeType"]}}},orderQuery:{args:{orderId:"outTradeNo",wxOrderId:"transactionId"},returnValue:{_pre(e){const t=JSON.parse(JSON.stringify(e.order));let r="",n="";[0,1].indexOf(t.status)>-1?(r="NOTPAY",n="未支付"):[6].indexOf(t.status)>-1?(r="PAYERROR",n="支付失败"):[2,3,4].indexOf(t.status)>-1?(r="SUCCESS",n="支付成功"):[5,8,9,10].indexOf(t.status)>-1?(r="REFUND",n="订单发生过退款"):[7].indexOf(t.status)>-1?(r="REFUNDRROR",n="订单退款失败"):(r="NOTPAY",n="未支付");let i={outTradeNo:t.order_id,transactionId:t.wx_order_id,totalFee:t.order_fee,cashFee:t.paid_fee,leftFee:t.left_fee,couponFee:t.coupon_fee,tradeState:r,tradeStateDesc:n,refundFee:t.refund_fee,originalResult:t};return i=JSON.parse(JSON.stringify(i)),i}}},refund:{args:{_purify:{shouldDelete:["totalFee","refundFeeType","refundDesc"]},orderId:"outTradeNo",wxOrderId:"transactionId",refundOrderId:"outRefundNo",refundFee:"refundFee"},returnValue:{}},refundQuery:{args:{orderId:"outRefundNo",wxOrderId:"transactionId"},returnValue:{_pre(e){const t=JSON.parse(JSON.stringify(e.order));let r="",n="";[0,1].indexOf(t.status)>-1?(r="NOTPAY",n="未支付"):[6].indexOf(t.status)>-1?(r="PAYERROR",n="支付失败"):[2,3,4].indexOf(t.status)>-1?(r="SUCCESS",n="支付成功"):[5,8,9,10].indexOf(t.status)>-1?(r="REFUND",n="订单发生过退款"):[7].indexOf(t.status)>-1?(r="REFUNDRROR",n="订单退款失败"):(r="NOTPAY",n="未支付");let i={outTradeNo:t.order_id,transactionId:t.wx_order_id,totalFee:t.order_fee,cashFee:t.paid_fee,couponFee:t.coupon_fee,tradeState:r,tradeStateDesc:n,refundFee:t.refund_fee,originalResult:t};return i=JSON.parse(JSON.stringify(i)),i}}},verifyPaymentNotify:{returnValue:{_pre(e){const t=e.OutTradeNo,r=e.WeChatPayInfo?1e3*e.WeChatPayInfo.PaidTime:Date.now(),o=e.WeChatPayInfo?e.WeChatPayInfo.MchOrderNo:void 0,a=e.OpenId,s=e.appId,u=e.Env;return"xpay_goods_deliver_notify"===e.Event?{outTradeNo:t,transactionId:o,tradeState:"SUCCESS",openid:a,appId:s,totalFee:e.GoodsInfo.Quantity*e.GoodsInfo.OrigPrice,cashFee:e.GoodsInfo.Quantity*e.GoodsInfo.ActualPrice,attach:e.GoodsInfo.Attach,timeEnd:n(r),goodsInfo:i(e.GoodsInfo),weChatPayInfo:i(e.WeChatPayInfo),env:u}:"xpay_coin_pay_notify"===e.Event?{outTradeNo:t,transactionId:o,tradeState:"SUCCESS",openid:a,appId:s,totalFee:e.CoinInfo.Quantity*e.CoinInfo.OrigPrice,cashFee:e.CoinInfo.Quantity*e.CoinInfo.ActualPrice,attach:e.CoinInfo.Attach,timeEnd:n(r),coinInfo:i(e.CoinInfo),weChatPayInfo:i(e.WeChatPayInfo),env:u}:void 0}}},verifyRefundNotify:{returnValue:{_pre:e=>({outTradeNo:e.MchOrderId,transactionId:e.WxpayRefundTransactionId,openid:e.OpenId,appId:e.appId,refundFee:e.RefundFee,outRefundNo:e.MchRefundId,refundStatus:0===e.RetCode?"SUCCESS":"CHANGE",retMsg:e.RetMsg,retryCount:e.RetryTimes})}},notifyProvideGoods:{args:{orderId:"outTradeNo",wxOrderId:"transactionId"},returnValue:{}},currencyPay:{args:{orderId:"outTradeNo"},returnValue:{outTradeNo:"orderId"}},cancelCurrencyPay:{args:{payOrderId:"outTradeNo",orderId:"outRefundNo"},returnValue:{outRefundNo:"orderId"}},presentCurrency:{args:{orderId:"outTradeNo"},returnValue:{outTradeNo:"orderId"}}};t.default=o,e.exports=t.default},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n};var o=class{constructor(e={}){const{appId:t,encodingAESKey:r,token:n}=e,i=Buffer.from(r+"=","base64"),o=i.slice(0,16);this.data={appId:t,token:n,key:i,iv:o}}encrypt(e){"string"!=typeof e&&(e=JSON.stringify(e));const{appId:t,key:r,iv:n}=this.data,o=i.default.randomBytes(16),a=Buffer.alloc(4);a.writeUInt32BE(Buffer.byteLength(e),0);const s=Buffer.from(e),u=Buffer.from(t);let c=Buffer.concat([o,a,s,u]);const f=i.default.createCipheriv("aes-256-cbc",r,n);f.setAutoPadding(!1),c=this.PKCS7Encode(c);return Buffer.concat([f.update(c),f.final()]).toString("base64")}decrypt(e){const{key:t,iv:r}=this.data,n=Buffer.from(e,"base64"),o=i.default.createDecipheriv("aes-256-cbc",t,r);o.setAutoPadding(!1);let a=Buffer.concat([o.update(n),o.final()]);a=this.PKCS7Decode(a);const s=20+a.readUInt32BE(16),u=a.slice(20,s),c=a.slice(s),f=u.toString(),l=c.toString();let h;try{h=JSON.parse(f)}catch(e){h=f}return{value:h,text:f,appId:l}}getMsgSign(e){const{token:t}=this.data,{timestamp:r,nonce:n,encrypt:o}=e,a=[t,r,n,o].sort().join("");return i.default.createHash("sha1").update(a).digest("hex")}verifyMsgSign(e){return this.getMsgSign(e)===e.msg_signature}getVerifyResponseSign(e){const{token:t}=this.data,{timestamp:r,nonce:n}=e,o=[t,r,n];o.sort((e,t)=>e.localeCompare(t));const a=o.join("");return i.default.createHash("sha1").update(a).digest("hex")}verifyResponseSign(e){return this.getVerifyResponseSign(e)===e.signature}PKCS7Decode(e){const t=e[e.length-1];return e.slice(0,e.length-t)}PKCS7Encode(e){const t=32-e.length%32,r=t,n=Buffer.alloc(t,r);return Buffer.concat([e,n])}};t.default=o,e.exports=t.default}]));