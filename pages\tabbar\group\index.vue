<!-- 
	团购列表页面 
-->
<template>
	<view>
		<!-- 水印背景 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />
		<!-- 页面标题和返回按钮 -->
		<view style="display:inline-flex;">
			<view style="margin:25rpx; display: block;vertical-align: middle;text-align: center;word-wrap: break-word;
			font-size:40rpx;color:red;width:520rpx;">
				团购页面
			</view>
			<view style="vertical-align: middle;">
				<button size="mini" style="height:70rpx;line-height:70rpx;margin:25rpx;" @click="back">返回</button>
			</view>
		</view>
		<!-- 团购列表 -->
		<view class="shop-list">
			<view class="list-content" style="background-color: #fefefe;">
				<!-- 无数据时显示 -->
				<view v-if="page.list.length==0"
					style="font-size: 30rpx;margin-top:50rpx;width:100vw;text-align: center;color:#ccc;">
					暂无数据
				</view>
				<view v-else>
					<!-- 搜索栏 -->
					<uni-search-bar @confirm="searchM" :focus="false" v-model="searchValue">
					</uni-search-bar>
					<!-- 团购项目列表 -->
					<view class="shop-item" v-for="item in page.list" :key="item._id" @click="handleDetail(item)">
						<!-- 项目图片 -->
						<view>
							<image class="img" :src="item.pic" style="height:100rpx;width: 100rpx;"></image>
						</view>
						<!-- 项目详情 -->
						<view class="shop-content">
							<!-- 项目标题 -->
							<view class="shop-title">
								{{item.title}}
							</view>
							<!-- 发布日期和限量信息 -->
							<view style="display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
								<view>发布日期：{{formatDate(item.createTime)}}</view>
								<view style="margin-left:30rpx;" v-if="item.limit">限量：{{item.limit}}</view>
							</view>
							<!-- 标签 -->
							<view class="shop-tag" v-if="item.label">
								<view v-for="label in item.label.split(',')" :key="label" class="tag-item">
									{{label}}
								</view>
							</view>
							<!-- 价格和统计信息 -->
							<view class="shop-footer">
								<view class="shop-price">
									<view class="unit" style="line-height: 40rpx;">￥</view>
									{{item.price}}
									<view class="origin-price" v-if="item.originPrice">
										{{item.originPrice}}
									</view>
									<!-- 浏览量 -->
									<view style="margin-left:50rpx;">
										<img style="height:30rpx;width:30rpx;" src='/static/images/review.png' />
									</view>
									<view style="margin-left:10rpx;">{{item.hits?item.hits:0}}</view>
									<!-- 预定量 -->
									<view style="margin-left:50rpx;">
										<img style="height:30rpx;width:30rpx;" src='/static/images/orders.png' />
									</view>
									<view style="margin-left:10rpx;">{{item.yd?item.yd:0}}</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				bgSrc: uni.getStorageSync('bg'),
				page: {
					list: []
				},
				currentIndex: 0,
				pageSize: 8,
				pageNumber: 1,
				searchValue: ''
			}
		},
		onLoad() {
			this.loadData();
		},
		onPullDownRefresh() {
			console.log('下拉刷新');
		
			// 模拟数据请求
			setTimeout(() => {
				uni.stopPullDownRefresh(); // 停止下拉刷新
			}, 1500);
		},
		onReachBottom() {
			console.log('上拉触底');
			// 加载更多数据
			this.loadData()
		},
		methods: {
			// 加载团购列表数据
			async loadData() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/glist',
						params: {
							pageSize: this.pageSize,
							pageNumber: this.pageNumber,
							sc:this.searchValue
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				});
				const now = Date.now();
				const threeDaysInMs = 72 * 60 * 60 * 1000; // 72小时的毫秒数
			
				// 处理返回的数据
				res.result.page.list = res.result.page.list.map(item => {
					// 根据dept字段设置标签前缀
					let prefix = item.dept === 'JJG' ? '区' : '所';
					item.label = item.label ? `${prefix}, ${item.label}` : prefix;
					// 添加NEW标签
					if ((now - new Date(item.createTime).getTime()) <= threeDaysInMs) {
						item.label = 'NEW,' + item.label;
					}
					return item;
				});
			
				// 更新或追加数据
				if (this.pageNumber === 1) {
					this.page.list = res.result.page.list;
				} else {
					this.page.list = this.page.list.concat(res.result.page.list);
				}
				// 增加页码准备下次加载
				if (res.result.page.list.length > 0) {
					this.pageNumber++;
				}			
			},
			
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 返回按钮处理
			back() {
				uni.switchTab({
					url: '../shenghuo/index'
				})
			},
			// 处理团购项目点击
			async handleDetail(item) {
				// 增加点击量
				await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/addHitGroup',
						params: {
							'_id': item._id
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				})
				
				console.log(item);
				// 跳转到详情页
				uni.navigateTo({
					url: "/pages/tabbar/group/view?index=" + this.currentIndex + "&params=" +
						encodeURIComponent(JSON.stringify(item))
				})
			},
			// 搜索处理
			async searchM() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/glist',
						params: {
							'sc': this.searchValue
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				});
				this.page = res.result.page;
				console.log(this.page);
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 样式定义 */
	page {
		background: #f2f2f2;
	}

	#watermark-canvas {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 1000;
		width: 100%;
		height: 100vh;
		pointer-events: none;
		opacity: 0.5;
	}

	/* 其他样式定义 */
</style>