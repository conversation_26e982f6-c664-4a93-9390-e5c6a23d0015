/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 颜色变量 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */


/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
$base-color: #ff536f;


/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable:#c0c0c0;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:24upx;
$uni-font-size-base:28upx;
$uni-font-size-lg:32upx;

/* 图片尺寸 */
$uni-img-size-sm:40upx;
$uni-img-size-base:52upx;
$uni-img-size-lg:80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;

/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36upx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30upx;



/* 颜色变量 */

/* 行为相关颜色 */
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #f0ad4e;
$uni-color-error: #dd524d;

/* 文字基本颜色 */
$uni-text-color:#333;//基本色
$uni-text-color-inverse:#fff;//反色
$uni-text-color-grey:#999;//辅助灰色，如加载更多的提示信息
$uni-text-color-placeholder: #808080;
$uni-text-color-disable:#c0c0c0;

/* 背景颜色 */
$uni-bg-color:#ffffff;
$uni-bg-color-grey:#f8f8f8;
$uni-bg-color-hover:#f1f1f1;//点击状态颜色
$uni-bg-color-mask:rgba(0, 0, 0, 0.4);//遮罩颜色

/* 边框颜色 */
$uni-border-color:#c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$uni-font-size-sm:24upx;
$uni-font-size-base:28upx;
$uni-font-size-lg:32upx;

/* 图片尺寸 */
$uni-img-size-sm:40upx;
$uni-img-size-base:52upx;
$uni-img-size-lg:80upx;

/* Border Radius */
$uni-border-radius-sm: 4upx;
$uni-border-radius-base: 6upx;
$uni-border-radius-lg: 12upx;
$uni-border-radius-circle: 50%;

/* 水平间距 */
$uni-spacing-row-sm: 10px;
$uni-spacing-row-base: 20upx;
$uni-spacing-row-lg: 30upx;

/* 垂直间距 */
$uni-spacing-col-sm: 8upx;
$uni-spacing-col-base: 16upx;
$uni-spacing-col-lg: 24upx;

/* 透明度 */
$uni-opacity-disabled: 0.3; // 组件禁用态的透明度

/* 文章场景相关 */
$uni-color-title: #2C405A; // 文章标题颜色
$uni-font-size-title:40upx;
$uni-color-subtitle: #555555; // 二级标题颜色
$uni-font-size-subtitle:36upx;
$uni-color-paragraph: #3F536E; // 文章段落颜色
$uni-font-size-paragraph:30upx;


	.content {
		position: relative;
		width: 100%;
		height: 100vh;
		/* Adjust as needed */
	}

	#watermark-canvas {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	page {
		background: #EEE;
	}

	.shop-list {
		display: flex;
		font-size: 10pt;
		background-color: #fff;

		margin-top: 10px;

		.list-left {
			position: fixed;
			width: 160rpx;
			text-align: center;
			background: rgb(248, 248, 248);
			height: 100%;
			overflow-y: auto;
			padding-bottom: 36rpx;

			&-item {
				padding: 24rpx 0;

				&.active {
					background: #fff;

					&::before {
						content: '';
						position: absolute;
						left: 4rpx;
						border-radius: 2px;
						width: 8rpx;
						height: 40rpx;
						background: linear-gradient(to right, #feef3c, #f3cd34);
					}
				}
			}
		}

		.list-content {
			margin-left: 10rpx;

			.list-sort {
				width: 730rpx; //580rpx;
				display: flex;
				justify-content: flex-end;

				.sort-item {
					display: flex;
					align-items: center;
					padding: 12rpx 18rpx;
					color: rgba(0, 0, 0, .7);

					&.active {
						color: #f3cd34;
					}

					.price-sort-icon {
						margin-left: 4rpx;
						width: 20rpx;
						height: 24rpx;
					}
				}
			}

			.shop-item {
				display: flex;
				padding: 10rpx;
				padding-right: 0;
				width: 98vw;

				.img {
					flex-shrink: 0;
					width: 160rpx;
					height: 160rpx;
				}

				.shop-content {
					flex: 1;
					padding: 10rpx;
					display: flex;
					flex-direction: column;

					.shop-title {
						font-size: 26rpx;
						word-break: break-all;

						.limited {
							padding: 2rpx 6rpx;
							border-radius: 6rpx;
							line-height: 26rpx;
							color: rgba(0, 0, 0, 0.8);
							background: linear-gradient(to right, #feef3c, #f3cd34);
							font-size: 20rpx;
							display: inline-block;
						}
					}

					.shop-desc {
						margin-top: 4rpx;
						font-size: 20rpx;
						color: rgba(0, 0, 0, .4);
					}

					.shop-tag {
						display: flex;
						flex-wrap: wrap;
						color: #f3cd34;

						.tag-item {
							margin: 4rpx 8rpx 0 0;
							font-size: 20rpx;
							border-radius: 6rpx;
							padding: 0 4rpx;
							display: inline-block;
							border: 1px solid #feef3c;
						}
					}

					.shop-footer {
						display: flex;
						justify-content: space-between;

						.shop-price {
							margin-top: 4rpx;
							display: flex;
							color: rgb(231, 79, 58);
							align-items: center;

							.unit {
								align-self: flex-end;
								font-size: 20rpx;
							}

							.origin-price {
								margin-top: 2rpx;
								margin-left: 4rpx;
								font-size: 20rpx;
								text-decoration: line-through;
								color: rgb(0, 0, 0, 0.4);
							}
						}


						.shop-button {
							background: linear-gradient(to right, #feef3c, #f3cd34);
							border-radius: 6rpx;
							height: 36rpx;
							display: flex;
							align-items: center;
							padding: 0 12rpx;
							border-radius: 36rpx;
						}
					}
				}
			}
		}
	}
