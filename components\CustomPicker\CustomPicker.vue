<template>
  <view>
    <!-- Trigger button to show picker -->
    <view class="trigger-button" @tap="showPicker = true">{{ selectedText || '请选择' }}</view>

    <!-- Overlay and Picker Container -->
    <view v-if="showPicker" class="picker-overlay" @tap="hidePicker">
      <view class="picker-container" @tap.stop>
        <scroll-view class="picker-list" scroll-y>
          <view v-for="(item, index) in options" :key="index" class="picker-item" @tap="selectItem(index)">
            {{ item }}
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      required: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showPicker: false,
      selectedText: this.value
    };
  },
  watch: {
    value(newVal) {
      this.selectedText = newVal;
    }
  },
  methods: {
    hidePicker() {
      this.showPicker = false;
    },
    selectItem(index) {
      this.selectedText = this.options[index];
      this.$emit('change', this.selectedText); // 使用 'change' 事件传递选中的值
      this.hidePicker();
    },
    clearSelection() {
      this.selectedText = '';
      this.$emit('change', this.selectedText); // 清空时触发 'change' 事件
    }
  }
};
</script>

<style scoped>
.trigger-button {
  text-align: left;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
  background-color: #fff;
}

.picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.picker-container {
  background: #fff;
  border-radius: 10px;
  overflow: hidden;
  width: 80%;
  max-height: 80%;
  display: flex;
  flex-direction: column;
}

.picker-list {
  flex: 1;
  padding: 10px;
}

.picker-item {
  padding: 10px;
  border-bottom: 1px solid #eee;
  white-space: normal;
  word-break: break-word;
  cursor: pointer;
}
</style>
