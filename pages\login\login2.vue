<!-- 
	小程序登录页面 
-->
<template>
	<view class="app">
		<!-- 页面背景装饰元素 -->
		<view class="left-bottom-sign"></view>
		<view class="back-btn mix-icon icon-guanbi" @tap="navBack"></view>
		<view class="right-top-sign"></view>
		<view class="wrapper">
			<!-- 欢迎信息 -->
			<view class="welcome">
				系统仅供内部使用<br />请输入信息进行绑定
			</view>
			<!-- 输入表单 -->
			<view class="input-content">
				<!-- 姓名输入框 -->
				<view class="input-item">
					<text class="tit">姓名</text>
					<view class="row">
						<input v-model="truename" type="string" maxlength="6" placeholder="请输入姓名"
								placeholder-style="color: #909399" />
					</view>
				</view>
				<!-- 手机号码输入框 -->
				<view class="input-item">
					<text class="tit">手机号码</text>
					<view class="row">
						<input v-model="telnum" type="number" maxlength="13" placeholder="请输入手机号码"
								placeholder-style="color: #909399" @blur="telChange" />
					</view>
				</view>

				<!-- 验证码输入框 -->
				<view class="input-item" style="display: block;">
					<view><text class="tit">验证码</text>
						<!-- 获取验证码按钮 -->
						<a v-if="yzcode != randcode && stamp == 0" @tap="getSms" class="tit"
								style="color:red;float:right;"><span>获取验证码</span></a>
						<!-- 倒计时显示 -->
						<span v-else-if="stamp > 0" class="tit" style="color:#ccc;float:right;">{{stamp}}秒</span>
						<!-- 验证成功显示 -->
						<span v-else class="tit" style="color:#blue;float:right;">√</span>
					</view>
					<view class="row">
						<input v-model="yzcode" type="string" maxlength="8" placeholder="请输入收到的验证码"
								placeholder-style="color: #909399" />
					</view>
				</view>

				<!-- 用户协议勾选 -->
				<view><checkbox-group @change="onCheckboxChange">
						<checkbox :value="checked" :checked="checked"></checkbox>我同意协议
						<text @click="dlg(0)">《用户服务协议》</text>
						<text @click="dlg(1)" href="#">《隐私政策》</text>
					</checkbox-group>
				</view>

			</view>

			<!-- 登录按钮 -->
			<mix-button ref="confirmBtn" text="登录" marginTop="60rpx" @onConfirm="login"></mix-button>

		</view>

		<!-- 弹出框组件 -->
		<uni-popup ref="popup1" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<!-- 弹出框内容 -->
				<view style="color: #606266;font-size: 15rpx;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;
					justify-content: center;align-items: center;padding: 5upx;">
					<mp-html :content="text" />
				</view>
				<!-- 确定按钮 -->
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view
						style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 15upx;margin-bottom:50rpx;">
						<view @tap="ewm_close" style="margin-left: 200rpx;padding: 8upx 15upx 8upx 15upx;">
							确 定
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 加载中组件 -->
		<mix-loading v-if="isLoading"></mix-loading>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 各种数据属性
				canUseAppleregister: false,
				agreement: true,
				telyes: '13912720430',
				username: '',
				truename: '',
				password: '',
				stamp: 0,
				yzcode: '',
				telnum: '',
				randcode: 'error',
				code: '',
				isLoading: false,
				uid: '',
				actionType: '',
				text: '',
				checked: false,
				show: 1,
				// 隐私政策HTML内容
				privacyPolicy: `
				    <h1 style="text-align:center;">隐私政策</h1>
				    <p>我们非常重视您的隐私。请仔细阅读以下隐私政策，以了解我们如何收集、使用和保护您的个人信息。</p>
				    <h2>1. 收集的信息</h2>
				    <p>我们收集的信息包括但不限于：姓名、电子邮件地址、设备信息等。</p>
				    <h2>2. 信息的使用</h2>
				    <p>我们使用您的信息是为了：</p>
				    <ul>
				      <li>提供和改进我们的服务</li>
				      <li>处理您的请求和查询</li>
				      <li>进行数据分析和研究</li>
				    </ul>
				    <h2>3. 信息的共享和披露</h2>
				    <p>我们不会与第三方共享您的个人信息，除非：</p>
					<ul>
					  <li>获得您的明确同意</li>
					  <li>法律要求</li>
					  <li>保护我们的合法权益</li>
					</ul>
				    <h2>4. 信息的保护</h2>
				    <p>我们采取合理的安全措施保护您的信息，防止未经授权的访问、使用或披露。</p>
				    <h2>5. 用户的权利</h2>
				    <p>您有权访问、更正和删除您的个人信息。如果您有任何疑问或请求，请联系我们的客服。</p>
					<h2>6. 隐私政策的变更</h2>
					<p>我们保留随时更新本隐私政策的权利。任何更新将发布在本页面上。</p>
					<p>如有任何问题，请联系我们：[联系方式]</p>
				    `,
				// 用户服务协议HTML内容
				userAgreement: `
				    <h1  style="text-align:center;">用户服务协议</h1>
				    <p>欢迎使用我们的应用程序。在您使用我们的服务之前，请仔细阅读以下条款。</p>
				    <h2>1. 服务内容</h2>
				    <p>我们提供的服务包括但不限于：...</p>
				    <h2>2. 用户信息的收集和使用</h2>
				    <p>我们收集的信息包括但不限于：姓名、电子邮件地址、设备信息等。我们收集这些信息是为了：</p>
				    <ul>
				      <li>提供和改进我们的服务</li>
				      <li>处理您的请求和查询</li>
				      <li>进行数据分析和研究</li>
				    </ul>
				    <h2>3. 用户的权利</h2>
				    <p>您有权访问、更正和删除您的个人信息。如果您有任何疑问或请求，请联系我们的客服。</p>
				    <h2>4. 责任声明</h2>
				    <p>我们将尽力确保服务的连续性和安全性，但对于由于不可抗力导致的服务中断或数据丢失，我们不承担责任。</p>
				    <h2>5. 其他</h2>
				    <p>本协议自发布之日起生效。我们保留对本协议的最终解释权。</p>
				    <p>如有任何问题，请联系我们：[联系方式]</p>
				    `,
			}
		},
		onLoad() {},
		methods: {
			// 复选框状态改变处理
			onCheckboxChange(event) {
				this.checked = !this.checked;
			},
			// 显示协议弹窗
			dlg(v) {
				if (v == 0) {
					this.text = this.userAgreement;
				} else {
					this.text = this.privacyPolicy;
				}
				this.show = 1;
				this.$refs.popup1.open()
			},
			// 关闭弹窗
			ewm_close() {
				this.$refs.popup1.close();
			},
			// 手机号码变更处理
			telChange(e) {
				if (this.telnum != this.telyes && this.telyes != '') {
					this.yzcode = '';
				}
			},
			// 获取短信验证码
			getSms() {
				let that = this;
				// 验证手机号格式
				if (!this.isMobile(this.telnum)) {
					uni.showModal({
						content: '输入手机号码错误，请重新输入',
						showCancel: false,
						success(res) {
							if (res.confirm) {
								that.telnum = '';
							}
						}
					})
				} else {
					// 设置倒计时
					that.stamp = 90;
					that.timer = setInterval(function() {
						that.jishiqi();
					}, 1000);
					// 生成随机验证码
					that.randcode = this.randomNum(100000, 999999);
					// 调用云函数发送短信
					uniCloud.callFunction({
							name: 'usercenter',
							data: {
								action: 'sendSmsCode',
								params: {
									mobile: that.telnum,
									templateId: '33071',
									code: that.randcode,
									type: 'register'
								}
							}
						})
						.then(res => {
							if (res.result.code == 0) {
								that.telyes = that.telnum;
							}
						});
				}
			},
			// 显示提示框
			showModal(content) {
				uni.showModal({
					title: '提示',
					content: content,
					showCancel: false
				})
			},
			// 倒计时处理
			jishiqi() {
				if (this.stamp > 0) {
					this.stamp--;
				} else {
					if (this.timer) {
						clearTimeout(this.timer);
						this.timer = null;
					}
					this.stamp = 0;
				}
			},
			// 生成随机数
			randomNum(min, max) {
				return Math.floor(Math.random() * (max - min) + min);
			},
			// 验证手机号格式
			isMobile(s) {
				return /^1[0-9]{10}$/.test(s)
			},
			// 返回上一页
			navBack() {
				uni.navigateBack();
			},
			// 登录处理
			async login(data) {
				// 检查是否同意用户协议
				if (!this.checked) {
					this.showModal('你必须同意用户协议和隐私政策，才可以继续使用本系统!');
					return;
				}
				const {
					telnum,
					telyes,
					truename
				} = this;
				
				// 验证输入
				if (truename == '') {
					this.showModal('姓名不可为空!');
				} else if (telnum == '') {
					this.showModal('手机号不可为空!');
				} else if (telnum != telyes) {
					this.showModal('请验证手机号码!');
				} else {
					// 检查用户是否存在
					let countRes = await uniCloud.callFunction({
						name: 'ycxcx',
						data: {
							action: 'usercount',
							params: {
								xm: truename,
								tel: telnum
							}
						}
					});

					if (countRes.result.data.length == 0) {
						uni.showModal({
							showCancel: false,
							content: '未在系统中找到您的帐号，请核实姓名、手机号是否跟登记的一致！',
							success: (res) => {}
						});
						return;
					}

					// 检查用户是否已注册
					let isRegisterRes = await uniCloud.callFunction({
						name: 'usercenter',
						data: {
							action: 'checkUser',
							params: {
								mobile: telnum
							},
						},
					});

					this.actionType = isRegisterRes.result.data.length > 0 ? 'login' : 'register';

					// 设置验证码
					let setCodeRes = await uniCloud.callFunction({
						name: 'usercenter',
						data: {
							action: 'setVerifyCode',
							params: {
								mobile: telnum,
								code: this.yzcode,
								type: this.actionType
							},
						},
					});
					if (setCodeRes.result.code != 0) {
						this.showModal('验证失败!');
						return;
					}

					// 登录或注册
					let loginRes = await uniCloud.callFunction({
						name: 'usercenter',
						data: {
							action: 'loginBySms',
							params: {
								mobile: telnum,
								code: this.yzcode,
								type: this.actionType
							},
						},
					});
					if (!loginRes.result.token) {
						this.showModal("登录失败");
						return;
					}
					// 保存登录信息
					uni.setStorageSync('token', loginRes.result.token);
					uni.setStorageSync('uid', loginRes.result.uid);

					let cn = countRes.result.data[0].cardNumber || '';
					let isgm = countRes.result.data[0].isgm || 0;
					let dept = countRes.result.data[0].dept || '';
					if (loginRes.result.userInfo) {
						uni.setStorageSync('user', loginRes.result.userInfo);
					} else {
						// 更新用户信息
						let upUser = await uniCloud.callFunction({
							name: 'usercenter',
							data: {
								action: 'updateUser',
								params: {
									uid: this.uid,
									username: truename.trim(),
									email: '',
									mobile: telnum.trim(),
									truename: truename.trim(),
									cardNumber: cn,
									isgm: isgm,
									islogin: 1,
									dept: dept
								},
								uniIdToken: uni.getStorageSync('token')
							}
						});
					}
					// 保存用户信息到本地存储
					uni.setStorageSync('uni_tel', telnum.trim());
					uni.setStorageSync('uni_truename', truename);
					uni.setStorageSync('uni_isgm', isgm);
					uni.setStorageSync('uni_cardNumber', cn);
					uni.setStorageSync('dept', dept);
					uni.setStorageSync('nickname', truename);
					uni.setStorageSync('username', truename);
					uni.setStorageSync('islogin', 1);

					getApp().globalData.isgm = isgm;
					// 跳转到首页
					uni.switchTab({
						url: '/pages/tabbar/shenghuo/index'
					});
				}
			}
		}
	}
</script>

<style>
	/* 页面背景样式 */
	page {
		background: #fff;
	}
</style>
<style scoped lang='scss'>
	/* 组件样式 */
	.app {
		/* ... 样式定义 ... */
	}
	/* 其他样式定义 */
</style>
					let dept = countRes.result.data[0].dept || '';
					if (loginRes.result.userInfo) {
						uni.setStorageSync('user', loginRes.result.userInfo);
					} else {
						let upUser = await uniCloud.callFunction({
							name: 'usercenter',
							data: {
								action: 'updateUser',
								params: {
									uid: this.uid,
									username: truename.trim(),
									email: '',
									mobile: telnum.trim(),
									truename: truename.trim(),
									cardNumber: cn,
									isgm: isgm,
									islogin: 1,
									dept: dept
								},
								uniIdToken: uni.getStorageSync('token')
							}
						});
					}
					uni.setStorageSync('uni_tel', telnum.trim());
					uni.setStorageSync('uni_truename', truename);
					uni.setStorageSync('uni_isgm', isgm);
					uni.setStorageSync('uni_cardNumber', cn);
					uni.setStorageSync('dept', dept);
					uni.setStorageSync('nickname', truename);
					uni.setStorageSync('username', truename);
					uni.setStorageSync('islogin', 1);

					getApp().globalData.isgm = isgm;
					uni.switchTab({
						url: '/pages/tabbar/shenghuo/index'
					});
				}
			}
		}
	}
</script>
<style>
	page {
		background: #fff;
	}
</style>
<style scoped lang='scss'>
	.app {
		//padding-top: 15vh;
		display: flex;
		align-items: center;
		/*定义body的元素垂直居中*/
		justify-content: center;
		/*定义body的里的元素水平居中*/
		position: relative;
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		background: #fff;
	}

	.wrapper {
		position: relative;
		z-index: 90;
		//padding-bottom: 40rpx;
	}

	.back-btn {
		position: absolute;
		left: 20rpx;
		top: calc(var(--status-bar-height) + 20rpx);
		z-index: 90;
		padding: 20rpx;
		font-size: 32rpx;
		color: #606266;
	}

	.left-top-sign {
		font-size: 120rpx;
		color: #f8f8f8;
		position: relative;
		left: -12rpx;
	}

	.right-top-sign {
		position: absolute;
		top: 80rpx;
		right: -30rpx;
		z-index: 95;

		&:before,
		&:after {
			display: block;
			content: "";
			width: 400rpx;
			height: 80rpx;
			background: #b4f3e2;
		}

		&:before {
			transform: rotate(50deg);
			border-top-right-radius: 50px;
		}

		&:after {
			position: absolute;
			right: -198rpx;
			top: 0;
			transform: rotate(-50deg);
			border-top-left-radius: 50px;
		}
	}

	.left-bottom-sign {
		position: absolute;
		left: -270rpx;
		bottom: -320rpx;
		border: 100rpx solid #d0d1fd;
		border-radius: 50%;
		padding: 180rpx;
	}

	.welcome {
		position: relative;
		left: 50rpx;
		top: -90rpx;
		font-size: 46rpx;
		color: #555;
		text-shadow: 1px 0px 1px rgba(0, 0, 0, .3);
	}

	.input-content {
		padding: 0 60rpx;
	}

	.input-item {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		padding: 0 30rpx;
		background: #f8f6fc;
		height: 120rpx;
		border-radius: 4px;
		margin-bottom: 50rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.row {
			width: 100%;
		}

		.tit {
			height: 50rpx;
			line-height: 56rpx;
			font-size: 26rpx;
			color: #606266;
		}

		input {
			flex: 1;
			height: 60rpx;
			font-size: 30rpx;
			color: #303133;
			width: 100%;
		}
	}

	/* 其他登录方式 */
	.other-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 20rpx;
		margin-top: 80rpx;

		.line {
			margin-bottom: 40rpx;

			.tit {
				margin: 0 32rpx;
				font-size: 24rpx;
				color: #606266;
			}

			&:before,
			&:after {
				content: '';
				width: 160rpx;
				height: 0;
				border-top: 1px solid #e0e0e0;
			}
		}

		.item {
			font-size: 24rpx;
			color: #606266;
			background-color: #fff;
			border: 0;

			&:after {
				border: 0;
			}
		}

		.icon {
			width: 90rpx;
			height: 90rpx;
			margin: 0 24rpx 16rpx;
		}
	}

	.agreement {
		position: absolute;
		left: 0;
		bottom: 6vh;
		z-index: 1;
		width: 750rpx;
		height: 90rpx;
		font-size: 24rpx;
		color: #999;

		.mix-icon {
			font-size: 36rpx;
			color: #ccc;
			margin-right: 8rpx;
			margin-top: 1px;

			&.active {
				color: $base-color;
			}
		}

		.tit {
			color: #40a2ff;
		}
	}
</style>