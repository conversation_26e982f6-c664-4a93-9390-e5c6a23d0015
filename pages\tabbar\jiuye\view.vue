<!-- 
	就业资讯查看页面 
-->
<template>
	<view>
		<!-- 水印画布 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />
		<view class='box'>
			<!-- 显示标题 -->
			<view style="width:100%;display: block;float:center;">
				<view class='subtitle' style="width:100%;height:100%;">
					{{item.title}}
				</view>
			</view>
			<!-- 按钮区域 -->
			<view style="display: block;right:50rpx;float:right;font-size:10rpx;margin-top:10rpx;">
				<!-- 参加活动按钮 -->
				<button size="mini" style="height:60rpx;line-height:60rpx;text-align:center;" @click="xd"
					:disabled="isDisabled">参加活动</button>
				<!-- 返回按钮 -->
				<button size="mini" style="margin-left:20rpx;height:60rpx;line-height:60rpx;text-align:center;"
					@click="back">返回</button>
			</view>
			<!-- 显示时间信息 -->
			<view class='subtime'>
				<view style="display: flex;align:left;">发布日期：{{ formatDate(item.createTime) }} </view>
				<view style="margin-left:20rpx;display:flex;align:left;">截止日期：{{ item.jzrq }} </view>
			</view>
			<!-- 显示详细信息 -->
			<view class='subdetail'>
				<mp-html :content="item.intro" />
			</view>
		</view>
	</view>
</template>
<script>
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				// 横幅图片来源
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片来源
				bgSrc: uni.getStorageSync('bg'),
				// 当前项目信息
				item: {},
			}
		},
		computed: {
			// 使用计算属性动态生成背景图片的样式
			backgroundStyle() {
				return `background-image: url(${this.item.pic}); width: 150rpx; height: 150rpx;`
			},
			// 判断按钮是否禁用
			isDisabled() {
				if (this.item.jzrq) {
					let targetDateString = this.item.jzrq;
					// 将字符串转换为 Date 对象
					let targetDate = new Date();
					if (targetDateString.length == 8)
					 targetDate = new Date(targetDateString.slice(0, 4), targetDateString.slice(4, 6) - 1, targetDateString.slice(6, 8));	
					else
					 targetDate = new Date(targetDateString)						// 直接加一天
					targetDate.setDate(targetDate.getDate() + 1);
					// 返回当前日期是否超过截止日期
					return new Date() > targetDate
				} else {
					return false;
				}
			}
		},
		// 页面加载时
		onLoad(options) {
			if (options.params) {
				let params;
				try {
					// 解析传入的参数
					params = JSON.parse(decodeURIComponent(options.params));
				} catch (e) {
					if (e instanceof URIError) {
						params = JSON.parse(options.params);
					}
				}

				// 设置当前项目信息
				this.item = params;
				if (this.item.jzrq) {
					// 格式化截止日期
					this.item.jzrq = this.item.jzrq.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')
				}
			}
		},
		methods: {
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 返回上一层页面
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一层页面
				});
			},
			// 参加活动
			async xd() {
				let content = '是否确认参加活动？'
				let sres = await uni.showModal({
					title: '提示',
					content
				});
				if (!sres.confirm) {
					return
				}
				uni.showLoading({
					mask: true
				})

				// 调用云函数保存参加信息
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveJY',
						params: {
							userId: uni.getStorageSync("uid"),
							tag: this.index,
							_id: this.item._id
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});

				let msg = '已报名参与，点击确定返回上个页面！';
				let title = '报名成功';
				if (res.result.state == 'fail') {
					msg = res.result.msg;
					title = '报名失败';
				}
				uni.hideLoading();

				// 显示报名结果
				uni.showModal({
					title: title,
					content: msg,
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack({
								delta: 1 // 返回上一层页面
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			}
		}

	}
</script>

<style>
	page {
		background: #EEE;
	}

	.title,
	.box {
		height: 100%;
		font-size: 50rpx;
		margin: 10rpx;
		border-radius: 15rpx;
		background-color: #fff;
		padding: 20rpx;
	}

	.subtitle {
		font-size: 40rpx;
		font-weight: 900;
		text-align: center;
		line-height: 50rpx;
		height: 60rpx;
		display: block;
		width: 100vw;
	}

	.subtime {
		display: inline-flex;
		text-align: right;
		color: red;
		font-size: 20rpx;
		margin-top: 10rpx;
		margin-left: 100rpx;
	}

	.subdetail {
		font-size: 30rpx;
		margin-top: 20rpx;
		margin-left: 20rpx;
		margin-right: 20rpx;
	}
</style>