// utils.js

// 从 './auth.js' 导入 checkToken 函数
import {
	checkToken
} from './auth.js';

// 定义一个异步函数 callCloudFunction，接受 options 参数
const callCloudFunction = async (options) => {
	await checkToken(); // 检查并刷新 token
	// 从 options 中解构出 name 和 data
	const {
		name,
		data
	} = options;
	// 调用 uniCloud.callFunction 并返回结果
	return uniCloud.callFunction({
		name: name, // 设置云函数名称
		data: {
			...data, // 展开传入的数据
			"uniIdToken": uni.getStorageSync("token") // 添加 token 到数据中
		}
	});
};

// 导出 callCloudFunction 函数
export {
	callCloudFunction
};