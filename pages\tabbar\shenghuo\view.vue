<!-- 
	生活圈查看页面 
-->
<template>
	<view>
		<!-- 创建一个带水印的画布背景 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 显示横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 标题区域 -->
		<view class="title">
			<!-- 左侧标题文本 -->
			<view class='left'>
				{{item.title}}
			</view>
			<!-- 右侧按钮区域 -->
			<view class='right'>
				<!-- 预约按钮，根据条件显示和禁用 -->
				<button size="mini" type='primary' @click="showDialog" v-if="item.yy" :disabled="isDisabled"
					style="height:60rpx;line-height:60rpx;text-align:center;">预约</button>
				<!-- 返回按钮 -->
				<button size="mini" style="margin-top:0rpx ;height:60rpx;line-height:60rpx;text-align:center;"
					@click="back">返回</button>
			</view>
		</view>

		<!-- 商品图片展示区域 -->
		<view style="text-align: center;margin:0 10rpx;">
			<image :src="item.pic" style="width:100%;object-fit: contain; border-radius: 15rpx;"></image>
		</view>		
		<!-- 商品介绍区域 -->
		<view class='box'>
			<view class='subtitle'>
				商品介绍：
			</view>
			<view class='subdetail'>
				<mp-html :content="item.intro" />
			</view>
		</view>

		<!-- 购买须知区域，根据条件显示 -->
		<view class='box' v-if="item.xz">
			<view class='subtitle'>
				购买须知：
			</view>
			<view class='subdetail'>
				<mp-html :content="item.xz" />
			</view>
		</view>

		<!-- 评论区域 -->
		<view class='box'>
			<!-- 评论输入框 -->
			<view class="comment-box">
				<view class="cu-form-group ">
					<uni-easyinput type="textarea" :disabled="!canComment" autoHeight v-model="content"
						placeholder="输入评论内容"></uni-easyinput>
				</view>

				<!-- 提交评论按钮 -->
				<button size="mini" style="margin-top:20rpx;margin-left:30%; background-color:blueviolet;color: white;"
					@click="submitComment" :disabled="!canComment">提交评论</button>
				<!-- 未购买时的提示信息 -->
				<view v-if="!canComment" class="comment-hint">购买后方可进行评论</view>
			</view>
			<!-- 评论列表 -->
			<view v-for="comment in commentList" :key="comment.commentId" v-if="commentList.length>0" class="comments">
				<view style="font-size:12px;display: inline-flex;">
					<view style='width:300rpx;display: block;'>{{comment.xm}}({{comment.dept}})</view>
					<view style='width:350rpx;text-align: right;'>发表时间: {{ formatDate(comment.createTime) }}</view>
				</view>
				<view style="font-size:12px;padding-left:2%;padding-right:2%">
					{{ comment.content }}
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	// 导入云函数调用工具
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				// 横幅图片地址
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片地址
				bgSrc: uni.getStorageSync('bg'),
				// 商品信息
				item: {},
				// 评论列表
				commentList: {},
				// 是否可以评论
				canComment: true,
				// 评论内容
				content: '',
				// 商品索引
				index: 0,
				// 评分
				star: 0
			}
		},
		computed: {
			// 计算预约按钮是否禁用
			isDisabled() {
				if (this.item.jzrq) {
					let targetDateString = this.item.jzrq;
					// 将字符串转换为 Date 对象
					let targetDate = new Date(targetDateString.slice(0, 4), targetDateString.slice(4, 6) - 1, targetDateString.slice(6, 8));					
					// 直接加一天
					targetDate.setDate(targetDate.getDate() + 1);
					return new Date() > targetDate
				} else {
					return false;
				}
			}
		},
		// 页面加载时执行
		onLoad(options) {
			console.log(options);
			if (options.params) {
				let params;
				try {
					params = JSON.parse(decodeURIComponent(options.params));
				} catch (e) {
					if (e instanceof URIError) {
						params = JSON.parse(options.params);
					}
				}
				this.item = params;
				this.index = options.index;
			}
		},
		// 页面显示时执行
		onShow() {
			this.checkPurchase();
			this.loadComments(this.item._id);
		},
		methods: {
			// 获取评分值
			getvalue(e) {
				this.star = e;
			},
			// 显示预约对话框
			showDialog() {
				uni.showModal({
					title: '请输入数量',
					editable: true,
					placeholderText: '输入您需要的数量',
					success: (res) => {
						if (res.confirm) {
							const quantity = res.content.trim();
							if (this.isValidQuantity(quantity)) {
								console.log('用户点击确定');
								this.submitQuantity(quantity);
							} else {
								uni.showToast({
									title: '请输入有效的数字',
									icon: 'none',
									duration: 2000
								});
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 验证数量是否有效
			isValidQuantity(quantity) {
				return /^\d+$/.test(quantity) && parseInt(quantity) > 0;
			},
			// 提交预约数量
			async submitQuantity(quantity) {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveOrder',
						params: {
							"userId": uni.getStorageSync("uid"),
							"dept": uni.getStorageSync("dept"),
							"sl": +quantity,
							"lifeid": this.item._id
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				let msg = '已下单，点击确定返回上个页面！';
				let title = '订购成功';
				if (res.result.state == 'fail') {
					msg = res.result.msg;
					title = '订购失败';
				}

				uni.showModal({
					title: title,
					content: msg,
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							uni.navigateBack({
								delta: 1 // 返回上一层页面
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 返回上一页
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一层页面
				});
			},
			// 检查是否已购买
			async checkPurchase() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/checkPurchase',
						params: {
							lifeId: this.item._id,
							userId: uni.getStorageSync("uid"), // 应由用户状态管理提供
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				if (res.result.code === 0) {
					this.canComment = res.result.purchased;
				} else {
					console.error('检查购买状态失败:', res.result.msg);
				}
			},
			// 加载评论列表
			async loadComments(productId) {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/commentList',
						params: {
							'sc': productId
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				if (res.result.code === 0) {
					this.commentList = res.result.data;
				} else {
					console.error('加载评论失败:', res.result.msg);
				}
			},
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 提交评论
			async submitComment() {
				if (!this.canComment) {
					uni.showToast({
						title: '请先购买商品后再评论',
						icon: 'none'
					});
					return;
				}

				let res = await callCloudFunction({
					name: 'admin',
					action: 'xcx/saveComment',
					data: {
						action: 'xcx/saveComment',
						params: {
							lgid: this.item._id,
							lx: 0,
							userId: uni.getStorageSync("uid"),
							dept: uni.getStorageSync("dept"),
							content: this.content,
						},
						"uniIdToken": uni.getStorageSync("token")
					},
				});

				if (res.result.state === 'ok') {
					uni.showToast({
						title: '评论成功'
					});
					this.content = ''; // 清空输入框
				} else {
					uni.showToast({
						title: '评论失败',
						icon: 'none'
					});
				}
			}
		}
	}
</script>

<style>
	/* 标题样式 */
	.title {
		font-size: 30rpx;
		margin: 10rpx;
		border-radius: 15rpx;
		background-color: #fff;
		padding: 20rpx;
		display: flex;
	}

	/* 标题左侧样式 */
	.title .left {
		width: 80%;
		display: flex;
	}

	/* 标题右侧样式 */
	.title .right {
		margin-left: 2%;
		width: 50%;
		display: flex;
	}

	/* 内容框样式 */
	.box {
		font-size: 40rpx;
		margin: 10rpx;
		border-radius: 15rpx;
		background-color: #fff;
		padding: 20rpx;
	}

	/* 副标题样式 */
	.subtitle {
		font-size: 30rpx;
		font-weight: 900;
	}

	/* 副标题详情样式 */
	.subdetail {
		font-size: 20rpx;
		margin-top: 4px;
	}

	/* 评论框样式 */
	.comment-box {
		margin-bottom: 20px;
	}

	/* 评论项样式 */
	.comment-item {
		margin-bottom: 10px;
	}

	/* 评论提示样式 */
	.comment-hint {
		color: #999;
		font-size: 14px;
		padding-top: 5px;
	}

	/* 评论列表样式 */
	.comments {
		font-size: 12pt;
		border: 1rpx solid #ccc;
		padding: 20rpx;
	}
</style>