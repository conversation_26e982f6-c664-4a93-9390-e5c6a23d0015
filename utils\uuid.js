function uuid(len, radix) {
    // 定义包含数字0-9的字符数组
    var chars = '0123456789'.split('');
    // 初始化UUID数组和索引变量
    var uuid = [], i;
    // 设置基数，如果未提供则使用字符数组长度
    radix = radix || chars.length;

    if (len) {
        // 如果提供了长度，生成紧凑形式的UUID
        for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
    } else {
        // 否则生成符合rfc4122版本4的UUID
        var r;

        // 根据rfc4122规范设置特定位置的字符
        uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
        uuid[14] = '4';

        // 填充随机数据，在i==19时设置时钟序列的高位
        // 符合rfc4122第4.1.5节的要求
        for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
                r = 0 | Math.random() * 16;
                uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
            }
        }
    }

    // 将UUID数组连接成字符串并返回
    return uuid.join('');
}