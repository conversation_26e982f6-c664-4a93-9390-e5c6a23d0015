<!-- 
	警营110页面 
-->
<template>
	<view>
		<!-- 水印画布 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 显示警营相关设置 -->
		<view v-if="setting.jingying"
			style="margin:5rpx;padding:20rpx; display: block;vertical-align: middle;text-align: center;word-wrap: break-word;">
			{{setting.jingying}}
		</view>

		<!-- 表单区域 -->
		<view style="padding:10rpx;">
			<form>
				<!-- 姓名输入框 -->
				<view class="cu-form-group margin-top-xs">
					<view class="title">姓名</view>
					<uni-easyinput placeholder="请输入姓名" v-model="xm"></uni-easyinput>
				</view>
				<!-- 手机号输入框 -->
				<view class="cu-form-group ">
					<view class="title">手机号</view>
					<uni-easyinput placeholder="请输入手机号码" v-model="tel"></uni-easyinput>
				</view>
				<!-- 服务选择下拉框 -->
				<view class="cu-form-group ">
					<view class="title">服务选择</view>
					<custom-picker ref="customPicker" :options="typeList" @change="handlePickerChange"></custom-picker>
				</view>
				<!-- 述求申报输入框 -->
				<view class="cu-form-group">
					<view class="title">述求申报</view>
					<uni-easyinput type="textarea" autoHeight v-model="sq" placeholder="请输入述求"></uni-easyinput>
				</view>

				<!-- 所在区域输入框 -->
				<view class="cu-form-group ">
					<view class="title">所在区域</view>
					<uni-easyinput placeholder="请输入所在镇、街道、社区或小区等具体位置" v-model="szqy"></uni-easyinput>
				</view>
				<!-- 商家选择下拉框 -->
				<view class="cu-form-group ">
					<view class="title">商家选择</view>
					<custom-picker ref="customPicker" :options="typeList2"
						@change="handlePickerChange2"></custom-picker>
				</view>
				<!-- 显示商家信息 -->
				<view v-if="item" style="margin-left:100rpx;color:#555;">
					<view>
						<lable style="margin-right:30rpx;">联系电话:</lable>{{item.tel}}
					</view>
					<view>
						<lable style="margin-right:30rpx;">服务区域:</lable>{{item.fwqy}}
					</view>
				</view>

				<!-- 故障点位图片上传 -->
				<view class="cu-form-group padding-bottom-xs">
					<view class="title">故障点位图片</view>
					<view style="float:left;width:100%;">
						<image style="width:280rpx;height:280rpx;float:left;" :src="image?image:defaultImg"
							mode="aspectFit" @click="selImg()"></image>
					</view>
				</view>
			</form>
		</view>
		<!-- 提交和返回按钮 -->
		<view style="display: flex;">
			<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:8%;"
				@click='submit'>提交</button>
			<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:5%;"
				@click="clear">返回</button>
		</view>
		<!-- <tab-bar :pagePath="currentPage" :isgm="isgm"></tab-bar> -->

	</view>
</template>

<script>
	import {
		pathToBase64,
		base64ToPath
	} from '/utils/image-tools.js'
	import CustomPicker from '../../../components/CustomPicker/CustomPicker.vue';
	import {
		callCloudFunction,
	} from '/utils/utils.js';

	export default {
		components: {
			CustomPicker
		},
		data() {
			return {
				// 横幅图片来源
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片来源
				bgSrc: uni.getStorageSync('bg'),
				// 是否为功模模式
				isgm: uni.getStorageSync('uni_isgm'),
				// 当前选中的索引
				index: -1,
				// 当前选中的值
				value: '',
				// 所在区域
				szqy: '',
				// 姓名
				xm: '',
				// 服务类型
				type: '',
				// 设置
				setting: uni.getStorageSync("setting"),
				// 默认图片
				defaultImg: '/static/nopic.jpg',
				// 上传的图片
				image: null,

				// 述求
				sq: '',
				// 手机号码
				tel: '',
				// 图片路径
				tp: '',
				// tabBar第二项的左边距
				tabBarSecondItemLeft: 300,
				// 选择的商家
				shes: uni.getStorageSync("shes"),
				// 过滤后的商家列表
				shesfilter: [],
				// 服务类型列表
				typeList: uni.getStorageSync("setting").jingyingList ? uni.getStorageSync("setting").jingyingList.split(
					';') : ["11111", "2222"],
				// 商家选择列表
				typeList2: [],
				// 选中的商家类型
				type2: '',
				// 当前选中的商家
				item: null
			};
		},
		onLoad() {

		},
		onShow() {
			// 获取用户信息
			this.xm = uni.getStorageSync("uni_truename") || "";
			this.tel = uni.getStorageSync("uni_tel") || "";
		},
		methods: {
			// 选择图片
			selImg() {
				let _this = this;
				uni.chooseImage({
					count: 1,
					success: (res) => {
						this.image = res.tempFilePaths[0]
						// 获取图片路径
						const path = res.tempFilePaths[0];
						uni.getImageInfo({
							src: res.tempFilePaths[0],
							success: (path => {
								// 将图片转换为base64格式
								pathToBase64(path.path).then(base64 => {
										_this.tp = base64;
									})
									.catch(error => {
										console.error(error)
									})
							})
						});
						this.$forceUpdate() // 强制刷新视图
					}
				})
			},
			// 处理服务选择变化
			handlePickerChange(value) {
				this.item = null;
				this.type = value;
				// 过滤商家列表
				this.shesfilter = this.shes.filter(item => item.fl === value);
				this.typeList2 = this.shesfilter.map(item => item.name);
				this.typeList2.push("其它")
			},
			// 处理商家选择变化
			handlePickerChange2(value) {
				this.type2 = value;
				this.item = this.shesfilter.find(item => item.name === value);
			},
			// 清除表单
			clear() {
				this.type = '';
				this.sq = '';
				this.tp = '';
				this.xm = uni.getStorageSync("uni_truename") || "";
				this.tel = uni.getStorageSync("uni_tel") || "";
				if (this.$refs.customPicker.clearSelection()) {
					this.$refs.customPicker.clearSelection(); // 调用子组件的方法清除选择
				}
				uni.navigateBack({
					delta: 1 // 返回上一层页面
				});
			},
			// 显示提示框
			showModal(content, title) {
				if (title == '')
					title = '提示';
				uni.showModal({
					title: title,
					content: content,
					showCancel: false
				})
			},
			// 提交表单
			async submit() {
				// 验证述求和区域是否填写
				if (this.sq == '') {
					this.showModal("必须填写述求信息！", '提示');
					return;
				} else if (this.szqy == '') {
					this.showModal("所在区域不能为空！", '提示');
					return;
				} else {
					// 调用云函数提交数据
					await callCloudFunction({
						name: 'admin',
						data: {
							action: 'xcx/saveJY110',
							params: {
								"userId": uni.getStorageSync("uid"),
								"dept": uni.getStorageSync("dept"),
								xm: this.xm,
								sq: this.sq,
								type: this.type,
								mtel: this.tel,
								tp: this.tp,
								sqrxm: uni.getStorageSync("uni_truename"),
								tel: uni.getStorageSync("uni_tel"),
								szqy: this.szqy,
								sh: this.item
							},
							"uniIdToken": uni.getStorageSync("token")
						}
					});

					// 显示提交成功提示
					uni.showModal({
						title: '确认',
						content: '已发送，点击确定返回上个页面！',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								uni.navigateBack({
									delta: 1 // 返回上一层页面
								});
							}
						}
					});
				}
			}
		}

	}
</script>

<style>
	.cu-form-group .title {
		min-width: calc(4em + 15px);
	}

	.cu-form-group {
		border-top: none;
	}
</style>