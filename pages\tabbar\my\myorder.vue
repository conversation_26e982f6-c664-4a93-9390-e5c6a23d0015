<!-- 
	我的更多订单页面 
-->
<template>
	<view>
		<!-- 背景图片和水印 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<view class='box'>
			<!-- 订单标题 -->
			<view style="width:100%;display: block;text-align:center;margin-top:50rpx;font-size: 60rpx;;">
				<view class='subtitle'>
					{{item.title}}
				</view>
			</view>
			<!-- 返回按钮 -->
			<view style="display: block;right:50rpx;float:right;margin-right:20rpx;">
				<button size="mini" style="height:60rpx;line-height:60rpx;text-align:center;" @click="back">返回</button>
			</view>
			<!-- 支付状态 -->
			<view style="margin-top:120rpx;font-size:60rpx;text-align: center;font-weight: 800;font-style: italic;">
				已支付
			</view>
			<!-- 订单详情 -->
			<view style="margin-top:50rpx;font-size:40rpx;margin-left:120rpx;">
				<view style="width:750rpx;display:inline-flex;">
					<view style="width:180rpx;text-align:right;margin-right:20rpx;">单价:</view>
					<view>{{item.group.price}}</view>
				</view>
				<view style="width:750rpx;display:inline-flex;">
					<view style="width:180rpx;text-align:right;margin-right:20rpx;">数量:</view>
					<view>{{item.sl}}</view>
				</view>
				<view style="width:750rpx;display:inline-flex;">
					<view style="width:180rpx;text-align:right;margin-right:20rpx;">订单金额:</view>
					<view>{{item.group.price * item.sl}}</view>
				</view>
			</view>

			<!-- 订单图片 -->
			<view style="display:block;text-align: center;width:750rpx;margin-top:100rpx;">
				<image :src="item.pic" style="width:300rpx;height:300rpx;" />
			</view>

		</view>
	</view>
</template>

<script>
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				bgSrc: uni.getStorageSync('bg'),
				item: {},
				groupid: ''

			}
		},
		// 在页面加载时执行
		onLoad(options) {
			// 获取订单号
			this.groupid = options.order_no;
			console.log(this.groupid);
			// 加载订单数据
			this.loadData();
			this.item = {};

		},
		methods: {
			// 加载订单数据
			async loadData() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/myGroupOrderDetail',
						params: {
							'_id': this.groupid
						},
						"uniIdToken": uni.getStorageSync("token")

					}
				});
				console.log(res);
				this.item = res.result;

			},
			// 返回上一页
			back() {
				uni.navigateTo({
					url:"/pages/tabbar/group/index"
				})
			}
		}
	}
</script>

<style>
</style>