"use strict";function t(t){return t&&"object"==typeof t&&"default"in t?t.default:t}var e=t(require("fs")),n=t(require("util")),r=t(require("stream")),i=t(require("zlib")),o=t(require("assert")),s=t(require("buffer")),a=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then};let h;const l=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];var u=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},f=function(t){return l[t]},c=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},d=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');h=t},p=function(){return void 0!==h},g=function(t){return h(t)};function _(t,e){return t(e={exports:{}},e.exports),e.exports}var m=_((function(t,e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(t){return n}}}));m.L,m.M,m.Q,m.H,m.isValid;function w(){this.buffer=[],this.length=0}w.prototype={get:function(t){const e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var y=w;function E(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}E.prototype.set=function(t,e,n,r){const i=t*this.size+e;this.data[i]=n,r&&(this.reservedBit[i]=!0)},E.prototype.get=function(t,e){return this.data[t*this.size+e]},E.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},E.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var b=E,T=_((function(t,e){const n=u;e.getRowColCoords=function(t){if(1===t)return[];const e=Math.floor(t/7)+2,r=n(t),i=145===r?26:2*Math.ceil((r-13)/(2*e-2)),o=[r-7];for(let t=1;t<e-1;t++)o[t]=o[t-1]-i;return o.push(6),o.reverse()},e.getPositions=function(t){const n=[],r=e.getRowColCoords(t),i=r.length;for(let t=0;t<i;t++)for(let e=0;e<i;e++)0===t&&0===e||0===t&&e===i-1||t===i-1&&0===e||n.push([r[t],r[e]]);return n}}));T.getRowColCoords,T.getPositions;const C=u;var A=function(t){const e=C(t);return[[0,0],[e-7,0],[0,e-7]]},I=_((function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const n=3,r=3,i=40,o=10;function s(t,n,r){switch(t){case e.Patterns.PATTERN000:return(n+r)%2==0;case e.Patterns.PATTERN001:return n%2==0;case e.Patterns.PATTERN010:return r%3==0;case e.Patterns.PATTERN011:return(n+r)%3==0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case e.Patterns.PATTERN101:return n*r%2+n*r%3==0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){const e=t.size;let r=0,i=0,o=0,s=null,a=null;for(let h=0;h<e;h++){i=o=0,s=a=null;for(let l=0;l<e;l++){let e=t.get(h,l);e===s?i++:(i>=5&&(r+=n+(i-5)),s=e,i=1),e=t.get(l,h),e===a?o++:(o>=5&&(r+=n+(o-5)),a=e,o=1)}i>=5&&(r+=n+(i-5)),o>=5&&(r+=n+(o-5))}return r},e.getPenaltyN2=function(t){const e=t.size;let n=0;for(let r=0;r<e-1;r++)for(let i=0;i<e-1;i++){const e=t.get(r,i)+t.get(r,i+1)+t.get(r+1,i)+t.get(r+1,i+1);4!==e&&0!==e||n++}return n*r},e.getPenaltyN3=function(t){const e=t.size;let n=0,r=0,o=0;for(let i=0;i<e;i++){r=o=0;for(let s=0;s<e;s++)r=r<<1&2047|t.get(i,s),s>=10&&(1488===r||93===r)&&n++,o=o<<1&2047|t.get(s,i),s>=10&&(1488===o||93===o)&&n++}return n*i},e.getPenaltyN4=function(t){let e=0;const n=t.data.length;for(let r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*o},e.applyMask=function(t,e){const n=e.size;for(let r=0;r<n;r++)for(let i=0;i<n;i++)e.isReserved(i,r)||e.xor(i,r,s(t,i,r))},e.getBestMask=function(t,n){const r=Object.keys(e.Patterns).length;let i=0,o=1/0;for(let s=0;s<r;s++){n(s),e.applyMask(s,t);const r=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(s,t),r<o&&(o=r,i=s)}return i}}));I.Patterns,I.isValid,I.getPenaltyN1,I.getPenaltyN2,I.getPenaltyN3,I.getPenaltyN4,I.applyMask,I.getBestMask;const P=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],L=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];var R=function(t,e){switch(e){case m.L:return P[4*(t-1)+0];case m.M:return P[4*(t-1)+1];case m.Q:return P[4*(t-1)+2];case m.H:return P[4*(t-1)+3];default:return}},B=function(t,e){switch(e){case m.L:return L[4*(t-1)+0];case m.M:return L[4*(t-1)+1];case m.Q:return L[4*(t-1)+2];case m.H:return L[4*(t-1)+3];default:return}};const v=new Uint8Array(512),O=new Uint8Array(256);!function(){let t=1;for(let e=0;e<255;e++)v[e]=t,O[t]=e,t<<=1,256&t&&(t^=285);for(let t=255;t<512;t++)v[t]=v[t-255]}();var k=function(t){return v[t]},M=function(t,e){return 0===t||0===e?0:v[O[t]+O[e]]},N=_((function(t,e){e.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let r=0;r<t.length;r++)for(let i=0;i<e.length;i++)n[r+i]^=M(t[r],e[i]);return n},e.mod=function(t,e){let n=new Uint8Array(t);for(;n.length-e.length>=0;){const t=n[0];for(let r=0;r<e.length;r++)n[r]^=M(e[r],t);let r=0;for(;r<n.length&&0===n[r];)r++;n=n.slice(r)}return n},e.generateECPolynomial=function(t){let n=new Uint8Array([1]);for(let r=0;r<t;r++)n=e.mul(n,new Uint8Array([1,k(r)]));return n}}));N.mul,N.mod,N.generateECPolynomial;function D(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}D.prototype.initialize=function(t){this.degree=t,this.genPoly=N.generateECPolynomial(this.degree)},D.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=N.mod(e,this.genPoly),r=this.degree-n.length;if(r>0){const t=new Uint8Array(this.degree);return t.set(n,r),t}return n};var x=D,U=function(t){return!isNaN(t)&&t>=1&&t<=40};let S="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";S=S.replace(/u/g,"\\u");const Y="(?:(?![A-Z0-9 $%*+\\-./:]|"+S+")(?:.|[\r\n]))+";var F=new RegExp(S,"g"),H=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),z=new RegExp(Y,"g"),G=new RegExp("[0-9]+","g"),W=new RegExp("[A-Z $%*+\\-./:]+","g");const q=new RegExp("^"+S+"$"),K=new RegExp("^[0-9]+$"),j=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");var J={KANJI:F,BYTE_KANJI:H,BYTE:z,NUMERIC:G,ALPHANUMERIC:W,testKanji:function(t){return q.test(t)},testNumeric:function(t){return K.test(t)},testAlphanumeric:function(t){return j.test(t)}},V=_((function(t,e){e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!U(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return J.testNumeric(t)?e.NUMERIC:J.testAlphanumeric(t)?e.ALPHANUMERIC:J.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}(t)}catch(t){return n}}})),Q=(V.NUMERIC,V.ALPHANUMERIC,V.BYTE,V.KANJI,V.MIXED,V.getCharCountIndicator,V.getBestModeForData,V.isValid,_((function(t,e){const n=c(7973);function r(t,e){return V.getCharCountIndicator(t,e)+4}function i(t,e){let n=0;return t.forEach((function(t){const i=r(t.mode,e);n+=i+t.getBitsLength()})),n}e.from=function(t,e){return U(t)?parseInt(t,10):e},e.getCapacity=function(t,e,n){if(!U(t))throw new Error("Invalid QR Code version");void 0===n&&(n=V.BYTE);const i=8*(f(t)-B(t,e));if(n===V.MIXED)return i;const o=i-r(n,t);switch(n){case V.NUMERIC:return Math.floor(o/10*3);case V.ALPHANUMERIC:return Math.floor(o/11*2);case V.KANJI:return Math.floor(o/13);case V.BYTE:default:return Math.floor(o/8)}},e.getBestVersionForData=function(t,n){let r;const o=m.from(n,m.M);if(Array.isArray(t)){if(t.length>1)return function(t,n){for(let r=1;r<=40;r++){if(i(t,r)<=e.getCapacity(r,n,V.MIXED))return r}}(t,o);if(0===t.length)return 1;r=t[0]}else r=t;return function(t,n,r){for(let i=1;i<=40;i++)if(n<=e.getCapacity(i,r,t))return i}(r.mode,r.getLength(),o)},e.getEncodedBits=function(t){if(!U(t)||t<7)throw new Error("Invalid QR Code version");let e=t<<12;for(;c(e)-n>=0;)e^=7973<<c(e)-n;return t<<12|e}})));Q.getCapacity,Q.getBestVersionForData,Q.getEncodedBits;const Z=c(1335);var $=function(t,e){const n=t.bit<<3|e;let r=n<<10;for(;c(r)-Z>=0;)r^=1335<<c(r)-Z;return 21522^(n<<10|r)};function X(t){this.mode=V.NUMERIC,this.data=t.toString()}X.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},X.prototype.getLength=function(){return this.data.length},X.prototype.getBitsLength=function(){return X.getBitsLength(this.data.length)},X.prototype.write=function(t){let e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);const i=this.data.length-e;i>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*i+1))};var tt=X;const et=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function nt(t){this.mode=V.ALPHANUMERIC,this.data=t}nt.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},nt.prototype.getLength=function(){return this.data.length},nt.prototype.getBitsLength=function(){return nt.getBitsLength(this.data.length)},nt.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let n=45*et.indexOf(this.data[e]);n+=et.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(et.indexOf(this.data[e]),6)};var rt=nt;function it(t){this.mode=V.BYTE,"string"==typeof t&&(t=function(t){for(var e=[],n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);if(i>=55296&&i<=56319&&n>r+1){var o=t.charCodeAt(r+1);o>=56320&&o<=57343&&(i=1024*(i-55296)+o-56320+65536,r+=1)}i<128?e.push(i):i<2048?(e.push(i>>6|192),e.push(63&i|128)):i<55296||i>=57344&&i<65536?(e.push(i>>12|224),e.push(i>>6&63|128),e.push(63&i|128)):i>=65536&&i<=1114111?(e.push(i>>18|240),e.push(i>>12&63|128),e.push(i>>6&63|128),e.push(63&i|128)):e.push(239,191,189)}return new Uint8Array(e).buffer}(t)),this.data=new Uint8Array(t)}it.getBitsLength=function(t){return 8*t},it.prototype.getLength=function(){return this.data.length},it.prototype.getBitsLength=function(){return it.getBitsLength(this.data.length)},it.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)};var ot=it;function st(t){this.mode=V.KANJI,this.data=t}st.getBitsLength=function(t){return 13*t},st.prototype.getLength=function(){return this.data.length},st.prototype.getBitsLength=function(){return st.getBitsLength(this.data.length)},st.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=g(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}};var at=st,ht=_((function(t){var e={single_source_shortest_paths:function(t,n,r){var i={},o={};o[n]=0;var s,a,h,l,u,f,c,d=e.PriorityQueue.make();for(d.push(n,0);!d.empty();)for(h in a=(s=d.pop()).value,l=s.cost,u=t[a]||{})u.hasOwnProperty(h)&&(f=l+u[h],c=o[h],(void 0===o[h]||c>f)&&(o[h]=f,d.push(h,f),i[h]=a));if(void 0!==r&&void 0===o[r]){var p=["Could not find a path from ",n," to ",r,"."].join("");throw new Error(p)}return i},extract_shortest_path_from_predecessor_list:function(t,e){for(var n=[],r=e;r;)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,n,r){var i=e.single_source_shortest_paths(t,n,r);return e.extract_shortest_path_from_predecessor_list(i,r)},PriorityQueue:{make:function(t){var n,r=e.PriorityQueue,i={};for(n in t=t||{},r)r.hasOwnProperty(n)&&(i[n]=r[n]);return i.queue=[],i.sorter=t.sorter||r.default_sorter,i},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e})),lt=_((function(t,e){function n(t){return unescape(encodeURIComponent(t)).length}function r(t,e,n){const r=[];let i;for(;null!==(i=t.exec(n));)r.push({data:i[0],index:i.index,mode:e,length:i[0].length});return r}function i(t){const e=r(J.NUMERIC,V.NUMERIC,t),n=r(J.ALPHANUMERIC,V.ALPHANUMERIC,t);let i,o;p()?(i=r(J.BYTE,V.BYTE,t),o=r(J.KANJI,V.KANJI,t)):(i=r(J.BYTE_KANJI,V.BYTE,t),o=[]);return e.concat(n,i,o).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function o(t,e){switch(e){case V.NUMERIC:return tt.getBitsLength(t);case V.ALPHANUMERIC:return rt.getBitsLength(t);case V.KANJI:return at.getBitsLength(t);case V.BYTE:return ot.getBitsLength(t)}}function s(t,e){let n;const r=V.getBestModeForData(t);if(n=V.from(e,r),n!==V.BYTE&&n.bit<r.bit)throw new Error('"'+t+'" cannot be encoded with mode '+V.toString(n)+".\n Suggested mode is: "+V.toString(r));switch(n!==V.KANJI||p()||(n=V.BYTE),n){case V.NUMERIC:return new tt(t);case V.ALPHANUMERIC:return new rt(t);case V.KANJI:return new at(t);case V.BYTE:return new ot(t)}}e.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(s(e,null)):e.data&&t.push(s(e.data,e.mode)),t}),[])},e.fromString=function(t,r){const s=function(t,e){const n={},r={start:{}};let i=["start"];for(let s=0;s<t.length;s++){const a=t[s],h=[];for(let t=0;t<a.length;t++){const l=a[t],u=""+s+t;h.push(u),n[u]={node:l,lastCount:0},r[u]={};for(let t=0;t<i.length;t++){const s=i[t];n[s]&&n[s].node.mode===l.mode?(r[s][u]=o(n[s].lastCount+l.length,l.mode)-o(n[s].lastCount,l.mode),n[s].lastCount+=l.length):(n[s]&&(n[s].lastCount=l.length),r[s][u]=o(l.length,l.mode)+4+V.getCharCountIndicator(l.mode,e))}}i=h}for(let t=0;t<i.length;t++)r[i[t]].end=0;return{map:r,table:n}}(function(t){const e=[];for(let r=0;r<t.length;r++){const i=t[r];switch(i.mode){case V.NUMERIC:e.push([i,{data:i.data,mode:V.ALPHANUMERIC,length:i.length},{data:i.data,mode:V.BYTE,length:i.length}]);break;case V.ALPHANUMERIC:e.push([i,{data:i.data,mode:V.BYTE,length:i.length}]);break;case V.KANJI:e.push([i,{data:i.data,mode:V.BYTE,length:n(i.data)}]);break;case V.BYTE:e.push([{data:i.data,mode:V.BYTE,length:n(i.data)}])}}return e}(i(t,p())),r),a=ht.find_path(s.map,"start","end"),h=[];for(let t=1;t<a.length-1;t++)h.push(s.table[a[t]].node);return e.fromArray(function(t){return t.reduce((function(t,e){const n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(h))},e.rawSplit=function(t){return e.fromArray(i(t,p()))}}));lt.fromArray,lt.fromString,lt.rawSplit;function ut(t,e,n){const r=t.size,i=$(e,n);let o,s;for(o=0;o<15;o++)s=1==(i>>o&1),o<6?t.set(o,8,s,!0):o<8?t.set(o+1,8,s,!0):t.set(r-15+o,8,s,!0),o<8?t.set(8,r-o-1,s,!0):o<9?t.set(8,15-o-1+1,s,!0):t.set(8,15-o-1,s,!0);t.set(r-8,8,1,!0)}function ft(t,e,n){const r=new y;n.forEach((function(e){r.put(e.mode.bit,4),r.put(e.getLength(),V.getCharCountIndicator(e.mode,t)),e.write(r)}));const i=8*(f(t)-B(t,e));for(r.getLengthInBits()+4<=i&&r.put(0,4);r.getLengthInBits()%8!=0;)r.putBit(0);const o=(i-r.getLengthInBits())/8;for(let t=0;t<o;t++)r.put(t%2?17:236,8);return function(t,e,n){const r=f(e),i=B(e,n),o=r-i,s=R(e,n),a=s-r%s,h=Math.floor(r/s),l=Math.floor(o/s),u=l+1,c=h-l,d=new x(c);let p=0;const g=new Array(s),_=new Array(s);let m=0;const w=new Uint8Array(t.buffer);for(let t=0;t<s;t++){const e=t<a?l:u;g[t]=w.slice(p,p+e),_[t]=d.encode(g[t]),p+=e,m=Math.max(m,e)}const y=new Uint8Array(r);let E,b,T=0;for(E=0;E<m;E++)for(b=0;b<s;b++)E<g[b].length&&(y[T++]=g[b][E]);for(E=0;E<c;E++)for(b=0;b<s;b++)y[T++]=_[b][E];return y}(r,t,e)}function ct(t,e,n,r){let i;if(Array.isArray(t))i=lt.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");{let r=e;if(!r){const e=lt.rawSplit(t);r=Q.getBestVersionForData(e,n)}i=lt.fromString(t,r||40)}}const o=Q.getBestVersionForData(i,n);if(!o)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<o)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+o+".\n")}else e=o;const s=ft(e,n,i),a=u(e),h=new b(a);return function(t,e){const n=t.size,r=A(e);for(let e=0;e<r.length;e++){const i=r[e][0],o=r[e][1];for(let e=-1;e<=7;e++)if(!(i+e<=-1||n<=i+e))for(let r=-1;r<=7;r++)o+r<=-1||n<=o+r||(e>=0&&e<=6&&(0===r||6===r)||r>=0&&r<=6&&(0===e||6===e)||e>=2&&e<=4&&r>=2&&r<=4?t.set(i+e,o+r,!0,!0):t.set(i+e,o+r,!1,!0))}}(h,e),function(t){const e=t.size;for(let n=8;n<e-8;n++){const e=n%2==0;t.set(n,6,e,!0),t.set(6,n,e,!0)}}(h),function(t,e){const n=T.getPositions(e);for(let e=0;e<n.length;e++){const r=n[e][0],i=n[e][1];for(let e=-2;e<=2;e++)for(let n=-2;n<=2;n++)-2===e||2===e||-2===n||2===n||0===e&&0===n?t.set(r+e,i+n,!0,!0):t.set(r+e,i+n,!1,!0)}}(h,e),ut(h,n,0),e>=7&&function(t,e){const n=t.size,r=Q.getEncodedBits(e);let i,o,s;for(let e=0;e<18;e++)i=Math.floor(e/3),o=e%3+n-8-3,s=1==(r>>e&1),t.set(i,o,s,!0),t.set(o,i,s,!0)}(h,e),function(t,e){const n=t.size;let r=-1,i=n-1,o=7,s=0;for(let a=n-1;a>0;a-=2)for(6===a&&a--;;){for(let n=0;n<2;n++)if(!t.isReserved(i,a-n)){let r=!1;s<e.length&&(r=1==(e[s]>>>o&1)),t.set(i,a-n,r),o--,-1===o&&(s++,o=7)}if(i+=r,i<0||n<=i){i-=r,r=-r;break}}}(h,s),isNaN(r)&&(r=I.getBestMask(h,ut.bind(null,h,n))),I.applyMask(r,h),ut(h,n,r),{modules:h,version:e,errorCorrectionLevel:n,maskPattern:r,segments:i}}var dt=function(t,e){if(void 0===t||""===t)throw new Error("No input text");let n,r,i=m.M;return void 0!==e&&(i=m.from(e.errorCorrectionLevel,m.M),n=Q.from(e.version),r=I.from(e.maskPattern),e.toSJISFunc&&d(e.toSJISFunc)),ct(t,n,i,r)},pt=_((function(t){let e=t.exports=function(){r.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};n.inherits(e,r),e.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))},e.prototype.write=function(t,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let n;return n=Buffer.isBuffer(t)?t:Buffer.from(t,e||this._encoding),this._buffers.push(n),this._buffered+=n.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused},e.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},e.prototype.destroySoon=e.prototype.end,e.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()},e.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},e.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},e.prototype._processRead=function(t){this._reads.shift();let e=0,n=0,r=Buffer.alloc(t.length);for(;e<t.length;){let i=this._buffers[n++],o=Math.min(i.length,t.length-e);i.copy(r,e,0,o),e+=o,o!==i.length&&(this._buffers[--n]=i.slice(o))}n>0&&this._buffers.splice(0,n),this._buffered-=t.length,t.func.call(this,r)},e.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else{if(!(this._buffered>=t.length))break;this._processRead(t)}}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}}));let gt=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];var _t=function(t,e){let n=[],r=t%8,i=e%8,o=(t-r)/8,s=(e-i)/8;for(let t=0;t<gt.length;t++){let e=gt[t],a=o*e.x.length,h=s*e.y.length;for(let t=0;t<e.x.length&&e.x[t]<r;t++)a++;for(let t=0;t<e.y.length&&e.y[t]<i;t++)h++;a>0&&h>0&&n.push({width:a,height:h,index:t})}return n},mt=function(t){return function(e,n,r){let i=e%gt[r].x.length,o=(e-i)/gt[r].x.length*8+gt[r].x[i],s=n%gt[r].y.length;return 4*o+((n-s)/gt[r].y.length*8+gt[r].y[s])*t*4}},wt=function(t,e,n){let r=t+e-n,i=Math.abs(r-t),o=Math.abs(r-e),s=Math.abs(r-n);return i<=o&&i<=s?t:o<=s?e:n},yt=_((function(t){function e(t,e,n){let r=t*e;return 8!==n&&(r=Math.ceil(r/(8/n))),r}let n=t.exports=function(t,n){let r=t.width,i=t.height,o=t.interlace,s=t.bpp,a=t.depth;if(this.read=n.read,this.write=n.write,this.complete=n.complete,this._imageIndex=0,this._images=[],o){let t=_t(r,i);for(let n=0;n<t.length;n++)this._images.push({byteWidth:e(t[n].width,s,a),height:t[n].height,lineIndex:0})}else this._images.push({byteWidth:e(r,s,a),height:i,lineIndex:0});this._xComparison=8===a?s:16===a?2*s:1};n.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},n.prototype._unFilterType1=function(t,e,n){let r=this._xComparison,i=r-1;for(let o=0;o<n;o++){let n=t[1+o],s=o>i?e[o-r]:0;e[o]=n+s}},n.prototype._unFilterType2=function(t,e,n){let r=this._lastLine;for(let i=0;i<n;i++){let n=t[1+i],o=r?r[i]:0;e[i]=n+o}},n.prototype._unFilterType3=function(t,e,n){let r=this._xComparison,i=r-1,o=this._lastLine;for(let s=0;s<n;s++){let n=t[1+s],a=o?o[s]:0,h=s>i?e[s-r]:0,l=Math.floor((h+a)/2);e[s]=n+l}},n.prototype._unFilterType4=function(t,e,n){let r=this._xComparison,i=r-1,o=this._lastLine;for(let s=0;s<n;s++){let n=t[1+s],a=o?o[s]:0,h=s>i?e[s-r]:0,l=s>i&&o?o[s-r]:0,u=wt(h,a,l);e[s]=n+u}},n.prototype._reverseFilterLine=function(t){let e,n=t[0],r=this._images[this._imageIndex],i=r.byteWidth;if(0===n)e=t.slice(1,i+1);else switch(e=Buffer.alloc(i),n){case 1:this._unFilterType1(t,e,i);break;case 2:this._unFilterType2(t,e,i);break;case 3:this._unFilterType3(t,e,i);break;case 4:this._unFilterType4(t,e,i);break;default:throw new Error("Unrecognised filter type - "+n)}this.write(e),r.lineIndex++,r.lineIndex>=r.height?(this._lastLine=null,this._imageIndex++,r=this._images[this._imageIndex]):this._lastLine=e,r?this.read(r.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}})),Et=_((function(t){let e=t.exports=function(t){pt.call(this);let e=[],n=this;this._filter=new yt(t,{read:this.read.bind(this),write:function(t){e.push(t)},complete:function(){n.emit("complete",Buffer.concat(e))}}),this._filter.start()};n.inherits(e,pt)})),bt={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5},Tt=_((function(t){let e=[];!function(){for(let t=0;t<256;t++){let n=t;for(let t=0;t<8;t++)1&n?n=3988292384^n>>>1:n>>>=1;e[t]=n}}();let n=t.exports=function(){this._crc=-1};n.prototype.write=function(t){for(let n=0;n<t.length;n++)this._crc=e[255&(this._crc^t[n])]^this._crc>>>8;return!0},n.prototype.crc32=function(){return-1^this._crc},n.crc32=function(t){let n=-1;for(let r=0;r<t.length;r++)n=e[255&(n^t[r])]^n>>>8;return-1^n}})),Ct=_((function(t){let e=t.exports=function(t,e){this._options=t,t.checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[bt.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[bt.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[bt.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[bt.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[bt.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[bt.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};e.prototype.start=function(){this.read(bt.PNG_SIGNATURE.length,this._parseSignature.bind(this))},e.prototype._parseSignature=function(t){let e=bt.PNG_SIGNATURE;for(let n=0;n<e.length;n++)if(t[n]!==e[n])return void this.error(new Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},e.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),n=t.readUInt32BE(4),r="";for(let e=4;e<8;e++)r+=String.fromCharCode(t[e]);let i=Boolean(32&t[4]);if(this._hasIHDR||n===bt.TYPE_IHDR){if(this._crc=new Tt,this._crc.write(Buffer.from(r)),this._chunks[n])return this._chunks[n](e);i?this.read(e+4,this._skipChunk.bind(this)):this.error(new Error("Unsupported critical chunk type "+r))}else this.error(new Error("Expected IHDR on beggining"))},e.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},e.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},e.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),n=this._crc.crc32();this._options.checkCRC&&n!==e?this.error(new Error("Crc error - "+e+" - "+n)):this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},e.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},e.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),n=t.readUInt32BE(4),r=t[8],i=t[9],o=t[10],s=t[11],a=t[12];if(8!==r&&4!==r&&2!==r&&1!==r&&16!==r)return void this.error(new Error("Unsupported bit depth "+r));if(!(i in bt.COLORTYPE_TO_BPP_MAP))return void this.error(new Error("Unsupported color type"));if(0!==o)return void this.error(new Error("Unsupported compression method"));if(0!==s)return void this.error(new Error("Unsupported filter method"));if(0!==a&&1!==a)return void this.error(new Error("Unsupported interlace method"));this._colorType=i;let h=bt.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:n,depth:r,interlace:Boolean(a),palette:Boolean(i&bt.COLORTYPE_PALETTE),color:Boolean(i&bt.COLORTYPE_COLOR),alpha:Boolean(i&bt.COLORTYPE_ALPHA),bpp:h,colorType:i}),this._handleChunkEnd()},e.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},e.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let n=0;n<e;n++)this._palette.push([t[3*n],t[3*n+1],t[3*n+2],255]);this.palette(this._palette),this._handleChunkEnd()},e.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))},e.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===bt.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(new Error("Transparency chunk must be after palette"));if(t.length>this._palette.length)return void this.error(new Error("More transparent colors than palette size"));for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===bt.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===bt.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},e.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},e.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/bt.GAMMA_DIVISION),this._handleChunkEnd()},e.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))},e.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===bt.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw new Error("Expected palette not found");this.inflateData(e);let n=t-e.length;n>0?this._handleIDAT(n):this._handleChunkEnd()},e.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},e.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}}));let At=[function(){},function(t,e,n,r){if(r===e.length)throw new Error("Ran out of data");let i=e[r];t[n]=i,t[n+1]=i,t[n+2]=i,t[n+3]=255},function(t,e,n,r){if(r+1>=e.length)throw new Error("Ran out of data");let i=e[r];t[n]=i,t[n+1]=i,t[n+2]=i,t[n+3]=e[r+1]},function(t,e,n,r){if(r+2>=e.length)throw new Error("Ran out of data");t[n]=e[r],t[n+1]=e[r+1],t[n+2]=e[r+2],t[n+3]=255},function(t,e,n,r){if(r+3>=e.length)throw new Error("Ran out of data");t[n]=e[r],t[n+1]=e[r+1],t[n+2]=e[r+2],t[n+3]=e[r+3]}],It=[function(){},function(t,e,n,r){let i=e[0];t[n]=i,t[n+1]=i,t[n+2]=i,t[n+3]=r},function(t,e,n){let r=e[0];t[n]=r,t[n+1]=r,t[n+2]=r,t[n+3]=e[1]},function(t,e,n,r){t[n]=e[0],t[n+1]=e[1],t[n+2]=e[2],t[n+3]=r},function(t,e,n){t[n]=e[0],t[n+1]=e[1],t[n+2]=e[2],t[n+3]=e[3]}];function Pt(t,e,n,r,i,o){let s=t.width,a=t.height,h=t.index;for(let t=0;t<a;t++)for(let a=0;a<s;a++){let s=n(a,t,h);At[r](e,i,s,o),o+=r}return o}function Lt(t,e,n,r,i,o){let s=t.width,a=t.height,h=t.index;for(let t=0;t<a;t++){for(let a=0;a<s;a++){let s=i.get(r),l=n(a,t,h);It[r](e,s,l,o)}i.resetAfterLine()}}var Rt=function(t,e){let n,r,i=e.width,o=e.height,s=e.depth,a=e.bpp,h=e.interlace;8!==s&&(n=function(t,e){let n=[],r=0;function i(){if(r===t.length)throw new Error("Ran out of data");let i,o,s,a,h,l,u,f,c=t[r];switch(r++,e){default:throw new Error("unrecognised depth");case 16:u=t[r],r++,n.push((c<<8)+u);break;case 4:u=15&c,f=c>>4,n.push(f,u);break;case 2:h=3&c,l=c>>2&3,u=c>>4&3,f=c>>6&3,n.push(f,u,l,h);break;case 1:i=1&c,o=c>>1&1,s=c>>2&1,a=c>>3&1,h=c>>4&1,l=c>>5&1,u=c>>6&1,f=c>>7&1,n.push(f,u,l,h,a,s,o,i)}}return{get:function(t){for(;n.length<t;)i();let e=n.slice(0,t);return n=n.slice(t),e},resetAfterLine:function(){n.length=0},end:function(){if(r!==t.length)throw new Error("extra data found")}}}(t,s)),r=s<=8?Buffer.alloc(i*o*4):new Uint16Array(i*o*4);let l,u,f=Math.pow(2,s)-1,c=0;if(h)l=_t(i,o),u=mt(i,o);else{let t=0;u=function(){let e=t;return t+=4,e},l=[{width:i,height:o}]}for(let e=0;e<l.length;e++)8===s?c=Pt(l[e],r,u,a,t,c):Lt(l[e],r,u,a,n,f);if(8===s){if(c!==t.length)throw new Error("extra data found")}else n.end();return r};var Bt=function(t,e){let n=e.depth,r=e.width,i=e.height,o=e.colorType,s=e.transColor,a=e.palette,h=t;return 3===o?function(t,e,n,r,i){let o=0;for(let s=0;s<r;s++)for(let r=0;r<n;r++){let n=i[t[o]];if(!n)throw new Error("index "+t[o]+" not in palette");for(let t=0;t<4;t++)e[o+t]=n[t];o+=4}}(t,h,r,i,a):(s&&function(t,e,n,r,i){let o=0;for(let s=0;s<r;s++)for(let r=0;r<n;r++){let n=!1;if(1===i.length?i[0]===t[o]&&(n=!0):i[0]===t[o]&&i[1]===t[o+1]&&i[2]===t[o+2]&&(n=!0),n)for(let t=0;t<4;t++)e[o+t]=0;o+=4}}(t,h,r,i,s),8!==n&&(16===n&&(h=Buffer.alloc(r*i*4)),function(t,e,n,r,i){let o=Math.pow(2,i)-1,s=0;for(let i=0;i<r;i++)for(let r=0;r<n;r++){for(let n=0;n<4;n++)e[s+n]=Math.floor(255*t[s+n]/o+.5);s+=4}}(t,h,r,i,n))),h},vt=_((function(t){let e=t.exports=function(t){pt.call(this),this._parser=new Ct(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};n.inherits(e,pt),e.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",(function(){}))),this.errord=!0},e.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=i.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let t=(1+(this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3))*this._bitmapInfo.height,e=Math.max(t,i.Z_MIN_CHUNK);this._inflate=i.createInflate({chunkSize:e});let n=t,r=this.emit.bind(this,"error");this._inflate.on("error",(function(t){n&&r(t)})),this._filter.on("complete",this._complete.bind(this));let o=this._filter.write.bind(this._filter);this._inflate.on("data",(function(t){n&&(t.length>n&&(t=t.slice(0,n)),n-=t.length,o(t))})),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)},e.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new Et(this._bitmapInfo)},e.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},e.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},e.prototype._simpleTransparency=function(){this._metaData.alpha=!0},e.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},e.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},e.prototype._complete=function(t){if(this.errord)return;let e;try{let n=Rt(t,this._bitmapInfo);e=Bt(n,this._bitmapInfo),n=null}catch(t){return void this._handleError(t)}this.emit("parsed",e)}}));let Ot={0:function(t,e,n,r,i){for(let o=0;o<n;o++)r[i+o]=t[e+o]},1:function(t,e,n,r,i,o){for(let s=0;s<n;s++){let n=s>=o?t[e+s-o]:0,a=t[e+s]-n;r[i+s]=a}},2:function(t,e,n,r,i){for(let o=0;o<n;o++){let s=e>0?t[e+o-n]:0,a=t[e+o]-s;r[i+o]=a}},3:function(t,e,n,r,i,o){for(let s=0;s<n;s++){let a=s>=o?t[e+s-o]:0,h=e>0?t[e+s-n]:0,l=t[e+s]-(a+h>>1);r[i+s]=l}},4:function(t,e,n,r,i,o){for(let s=0;s<n;s++){let a=s>=o?t[e+s-o]:0,h=e>0?t[e+s-n]:0,l=e>0&&s>=o?t[e+s-(n+o)]:0,u=t[e+s]-wt(a,h,l);r[i+s]=u}}},kt={0:function(t,e,n){let r=0,i=e+n;for(let n=e;n<i;n++)r+=Math.abs(t[n]);return r},1:function(t,e,n,r){let i=0;for(let o=0;o<n;o++){let n=o>=r?t[e+o-r]:0,s=t[e+o]-n;i+=Math.abs(s)}return i},2:function(t,e,n){let r=0,i=e+n;for(let o=e;o<i;o++){let i=e>0?t[o-n]:0,s=t[o]-i;r+=Math.abs(s)}return r},3:function(t,e,n,r){let i=0;for(let o=0;o<n;o++){let s=o>=r?t[e+o-r]:0,a=e>0?t[e+o-n]:0,h=t[e+o]-(s+a>>1);i+=Math.abs(h)}return i},4:function(t,e,n,r){let i=0;for(let o=0;o<n;o++){let s=o>=r?t[e+o-r]:0,a=e>0?t[e+o-n]:0,h=e>0&&o>=r?t[e+o-(n+r)]:0,l=t[e+o]-wt(s,a,h);i+=Math.abs(l)}return i}};var Mt=_((function(t){let e=t.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||i.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"==typeof t.colorType?t.colorType:bt.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"==typeof t.inputColorType?t.inputColorType:bt.COLORTYPE_COLOR_ALPHA,-1===[bt.COLORTYPE_GRAYSCALE,bt.COLORTYPE_COLOR,bt.COLORTYPE_COLOR_ALPHA,bt.COLORTYPE_ALPHA].indexOf(t.colorType))throw new Error("option color type:"+t.colorType+" is not supported at present");if(-1===[bt.COLORTYPE_GRAYSCALE,bt.COLORTYPE_COLOR,bt.COLORTYPE_COLOR_ALPHA,bt.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};e.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},e.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},e.prototype.filterData=function(t,e,n){let r=function(t,e,n,r){let i=-1!==[bt.COLORTYPE_COLOR_ALPHA,bt.COLORTYPE_ALPHA].indexOf(r.colorType);if(r.colorType===r.inputColorType){let e=function(){let t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256!==new Int16Array(t)[0]}();if(8===r.bitDepth||16===r.bitDepth&&e)return t}let o=16!==r.bitDepth?t:new Uint16Array(t.buffer),s=255,a=bt.COLORTYPE_TO_BPP_MAP[r.inputColorType];4!==a||r.inputHasAlpha||(a=3);let h=bt.COLORTYPE_TO_BPP_MAP[r.colorType];16===r.bitDepth&&(s=65535,h*=2);let l=Buffer.alloc(e*n*h),u=0,f=0,c=r.bgColor||{};function d(){let t,e,n,a=s;switch(r.inputColorType){case bt.COLORTYPE_COLOR_ALPHA:a=o[u+3],t=o[u],e=o[u+1],n=o[u+2];break;case bt.COLORTYPE_COLOR:t=o[u],e=o[u+1],n=o[u+2];break;case bt.COLORTYPE_ALPHA:a=o[u+1],t=o[u],e=t,n=t;break;case bt.COLORTYPE_GRAYSCALE:t=o[u],e=t,n=t;break;default:throw new Error("input color type:"+r.inputColorType+" is not supported at present")}return r.inputHasAlpha&&(i||(a/=s,t=Math.min(Math.max(Math.round((1-a)*c.red+a*t),0),s),e=Math.min(Math.max(Math.round((1-a)*c.green+a*e),0),s),n=Math.min(Math.max(Math.round((1-a)*c.blue+a*n),0),s))),{red:t,green:e,blue:n,alpha:a}}void 0===c.red&&(c.red=s),void 0===c.green&&(c.green=s),void 0===c.blue&&(c.blue=s);for(let t=0;t<n;t++)for(let t=0;t<e;t++){let t=d();switch(r.colorType){case bt.COLORTYPE_COLOR_ALPHA:case bt.COLORTYPE_COLOR:8===r.bitDepth?(l[f]=t.red,l[f+1]=t.green,l[f+2]=t.blue,i&&(l[f+3]=t.alpha)):(l.writeUInt16BE(t.red,f),l.writeUInt16BE(t.green,f+2),l.writeUInt16BE(t.blue,f+4),i&&l.writeUInt16BE(t.alpha,f+6));break;case bt.COLORTYPE_ALPHA:case bt.COLORTYPE_GRAYSCALE:{let e=(t.red+t.green+t.blue)/3;8===r.bitDepth?(l[f]=e,i&&(l[f+1]=t.alpha)):(l.writeUInt16BE(e,f),i&&l.writeUInt16BE(t.alpha,f+2));break}default:throw new Error("unrecognised color Type "+r.colorType)}u+=a,f+=h}return l}(t,e,n,this._options),i=bt.COLORTYPE_TO_BPP_MAP[this._options.colorType];return function(t,e,n,r,i){let o;if("filterType"in r&&-1!==r.filterType){if("number"!=typeof r.filterType)throw new Error("unrecognised filter types");o=[r.filterType]}else o=[0,1,2,3,4];16===r.bitDepth&&(i*=2);let s=e*i,a=0,h=0,l=Buffer.alloc((s+1)*n),u=o[0];for(let e=0;e<n;e++){if(o.length>1){let e=1/0;for(let n=0;n<o.length;n++){let r=kt[o[n]](t,h,s,i);r<e&&(u=o[n],e=r)}}l[a]=u,a++,Ot[u](t,h,s,l,a,i),a+=s,h+=s}return l}(r,e,n,this._options,i)},e.prototype._packChunk=function(t,e){let n=e?e.length:0,r=Buffer.alloc(n+12);return r.writeUInt32BE(n,0),r.writeUInt32BE(t,4),e&&e.copy(r,8),r.writeInt32BE(Tt.crc32(r.slice(4,r.length-4)),r.length-4),r},e.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*bt.GAMMA_DIVISION),0),this._packChunk(bt.TYPE_gAMA,e)},e.prototype.packIHDR=function(t,e){let n=Buffer.alloc(13);return n.writeUInt32BE(t,0),n.writeUInt32BE(e,4),n[8]=this._options.bitDepth,n[9]=this._options.colorType,n[10]=0,n[11]=0,n[12]=0,this._packChunk(bt.TYPE_IHDR,n)},e.prototype.packIDAT=function(t){return this._packChunk(bt.TYPE_IDAT,t)},e.prototype.packIEND=function(){return this._packChunk(bt.TYPE_IEND,null)}})),Nt=_((function(t){let e=t.exports=function(t){r.call(this);let e=t||{};this._packer=new Mt(e),this._deflate=this._packer.createDeflate(),this.readable=!0};n.inherits(e,r),e.prototype.pack=function(t,e,n,r){this.emit("data",Buffer.from(bt.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,n)),r&&this.emit("data",this._packer.packGAMA(r));let i=this._packer.filterData(t,e,n);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(t){this.emit("data",this._packer.packIDAT(t))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(i)}})),Dt=_((function(t,e){let r=o.ok,a=s.kMaxLength;function h(t){if(!(this instanceof h))return new h(t);t&&t.chunkSize<i.Z_MIN_CHUNK&&(t.chunkSize=i.Z_MIN_CHUNK),i.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function l(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function u(t,e){return function(t,e){if("string"==typeof e&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");let n=t._finishFlushFlag;return null==n&&(n=i.Z_FINISH),t._processChunk(e,n)}(new h(e),t)}h.prototype._processChunk=function(t,e,n){if("function"==typeof n)return i.Inflate._processChunk.call(this,t,e,n);let o,s,h=this,u=t&&t.length,f=this._chunkSize-this._offset,c=this._maxLength,d=0,p=[],g=0;function _(t,e){if(h._hadError)return;let n=f-e;if(r(n>=0,"have should not go down"),n>0){let t=h._buffer.slice(h._offset,h._offset+n);if(h._offset+=n,t.length>c&&(t=t.slice(0,c)),p.push(t),g+=t.length,c-=t.length,0===c)return!1}return(0===e||h._offset>=h._chunkSize)&&(f=h._chunkSize,h._offset=0,h._buffer=Buffer.allocUnsafe(h._chunkSize)),0===e&&(d+=u-t,u=t,!0)}this.on("error",(function(t){o=t})),r(this._handle,"zlib binding closed");do{s=this._handle.writeSync(e,t,d,u,this._buffer,this._offset,f),s=s||this._writeState}while(!this._hadError&&_(s[0],s[1]));if(this._hadError)throw o;if(g>=a)throw l(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+a.toString(16)+" bytes");let m=Buffer.concat(p,g);return l(this),m},n.inherits(h,i.Inflate),t.exports=e=u,e.Inflate=h,e.createInflate=function(t){return new h(t)},e.inflateSync=u})),xt=(Dt.Inflate,Dt.createInflate,Dt.inflateSync,_((function(t){let e=t.exports=function(t){this._buffer=t,this._reads=[]};e.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},e.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(!this._buffer.length||!(this._buffer.length>=t.length||t.allowLess))break;{this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}}return this._reads.length>0?new Error("There are some read requests waitng on finished stream"):this._buffer.length>0?new Error("unrecognised content at end of stream"):void 0}}))),Ut=function(t,e){let n=[],r=new xt(t);return new yt(e,{read:r.read.bind(r),write:function(t){n.push(t)},complete:function(){}}).start(),r.process(),Buffer.concat(n)};let St=!0;i.deflateSync||(St=!1);let Yt=!0;i.deflateSync||(Yt=!1);var Ft={read:function(t,e){return function(t,e){if(!St)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let n,r,o;let s=[];let a=new xt(t);if(new Ct(e,{read:a.read.bind(a),error:function(t){n=t},metadata:function(t){r=t},gamma:function(t){o=t},palette:function(t){r.palette=t},transColor:function(t){r.transColor=t},inflateData:function(t){s.push(t)},simpleTransparency:function(){r.alpha=!0}}).start(),a.process(),n)throw n;let h,l=Buffer.concat(s);if(s.length=0,r.interlace)h=i.inflateSync(l);else{let t=(1+(r.width*r.bpp*r.depth+7>>3))*r.height;h=Dt(l,{chunkSize:t,maxLength:t})}if(l=null,!h||!h.length)throw new Error("bad png - invalid inflate data response");let u=Ut(h,r);l=null;let f=Rt(u,r);u=null;let c=Bt(f,r);return r.data=c,r.gamma=o||0,r}(t,e||{})},write:function(t,e){return function(t,e){if(!Yt)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let n=new Mt(e||{}),r=[];r.push(Buffer.from(bt.PNG_SIGNATURE)),r.push(n.packIHDR(t.width,t.height)),t.gamma&&r.push(n.packGAMA(t.gamma));let o=n.filterData(t.data,t.width,t.height),s=i.deflateSync(o,n.getDeflateOptions());if(o=null,!s||!s.length)throw new Error("bad png - invalid compressed data response");return r.push(n.packIDAT(s)),r.push(n.packIEND()),Buffer.concat(r)}(t,e)}},Ht=_((function(t,e){let i=e.PNG=function(t){r.call(this),t=t||{},this.width=0|t.width,this.height=0|t.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new vt(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(t){this.data=t,this.emit("parsed",t)}.bind(this)),this._packer=new Nt(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};n.inherits(i,r),i.sync=Ft,i.prototype.pack=function(){return this.data&&this.data.length?(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this):(this.emit("error","No data provided"),this)},i.prototype.parse=function(t,e){if(e){let t,n;t=function(t){this.removeListener("error",n),this.data=t,e(null,this)}.bind(this),n=function(n){this.removeListener("parsed",t),e(n,null)}.bind(this),this.once("parsed",t),this.once("error",n)}return this.end(t),this},i.prototype.write=function(t){return this._parser.write(t),!0},i.prototype.end=function(t){this._parser.end(t)},i.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},i.prototype._gamma=function(t){this.gamma=t},i.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},i.bitblt=function(t,e,n,r,i,o,s,a){if(r|=0,i|=0,o|=0,s|=0,a|=0,(n|=0)>t.width||r>t.height||n+i>t.width||r+o>t.height)throw new Error("bitblt reading outside image");if(s>e.width||a>e.height||s+i>e.width||a+o>e.height)throw new Error("bitblt writing outside image");for(let h=0;h<o;h++)t.data.copy(e.data,(a+h)*e.width+s<<2,(r+h)*t.width+n<<2,(r+h)*t.width+n+i<<2)},i.prototype.bitblt=function(t,e,n,r,o,s,a){return i.bitblt(this,t,e,n,r,o,s,a),this},i.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let n=0;n<t.width;n++){let r=t.width*e+n<<2;for(let e=0;e<3;e++){let n=t.data[r+e]/255;n=Math.pow(n,1/2.2/t.gamma),t.data[r+e]=Math.round(255*n)}}t.gamma=0}},i.prototype.adjustGamma=function(){i.adjustGamma(this)}})),zt=(Ht.PNG,_((function(t,e){function n(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});const e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:r,scale:r?4:i,margin:e,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,n){const r=e.getScale(t,n);return Math.floor((t+2*n.margin)*r)},e.qrToImageData=function(t,n,r){const i=n.modules.size,o=n.modules.data,s=e.getScale(i,r),a=Math.floor((i+2*r.margin)*s),h=r.margin*s,l=[r.color.light,r.color.dark];for(let e=0;e<a;e++)for(let n=0;n<a;n++){let u=4*(e*a+n),f=r.color.light;if(e>=h&&n>=h&&e<a-h&&n<a-h){f=l[o[Math.floor((e-h)/s)*i+Math.floor((n-h)/s)]?1:0]}t[u++]=f.r,t[u++]=f.g,t[u++]=f.b,t[u]=f.a}}}))),Gt=(zt.getOptions,zt.getScale,zt.getImageWidth,zt.qrToImageData,_((function(t,n){const r=Ht.PNG;n.render=function(t,e){const n=zt.getOptions(e),i=n.rendererOpts,o=zt.getImageWidth(t.modules.size,n);i.width=o,i.height=o;const s=new r(i);return zt.qrToImageData(s.data,t,n),s},n.renderToDataURL=function(t,e,r){void 0===r&&(r=e,e=void 0),n.renderToBuffer(t,e,(function(t,e){t&&r(t);let n="data:image/png;base64,";n+=e.toString("base64"),r(null,n)}))},n.renderToBuffer=function(t,e,r){void 0===r&&(r=e,e=void 0);const i=n.render(t,e),o=[];i.on("error",r),i.on("data",(function(t){o.push(t)})),i.on("end",(function(){r(null,Buffer.concat(o))})),i.pack()},n.renderToFile=function(t,r,i,o){void 0===o&&(o=i,i=void 0);let s=!1;const a=(...t)=>{s||(s=!0,o.apply(null,t))},h=e.createWriteStream(t);h.on("error",a),h.on("close",a),n.renderToFileStream(h,r,i)},n.renderToFileStream=function(t,e,r){n.render(e,r).pack().pipe(t)}}))),Wt=(Gt.render,Gt.renderToDataURL,Gt.renderToBuffer,Gt.renderToFile,Gt.renderToFileStream,_((function(t,n){const r={WW:" ",WB:"▄",BB:"█",BW:"▀"},i={BB:" ",BW:"▄",WW:"█",WB:"▀"};function o(t,e,n){return t&&e?n.BB:t&&!e?n.BW:!t&&e?n.WB:n.WW}n.render=function(t,e,n){const s=zt.getOptions(e);let a=r;"#ffffff"!==s.color.dark.hex&&"#000000"!==s.color.light.hex||(a=i);const h=t.modules.size,l=t.modules.data;let u="",f=Array(h+2*s.margin+1).join(a.WW);f=Array(s.margin/2+1).join(f+"\n");const c=Array(s.margin+1).join(a.WW);u+=f;for(let t=0;t<h;t+=2){u+=c;for(let e=0;e<h;e++){u+=o(l[t*h+e],l[(t+1)*h+e],a)}u+=c+"\n"}return u+=f.slice(0,-1),"function"==typeof n&&n(null,u),u},n.renderToFile=function(t,r,i,o){void 0===o&&(o=i,i=void 0);const s=e,a=n.render(r,i);s.writeFile(t,a,o)}}))),qt=(Wt.render,Wt.renderToFile,function(t,e,n){const r=t.modules.size,i=t.modules.data,o="[47m  [0m";let s="";const a=Array(r+3).join(o),h=Array(2).join(o);s+=a+"\n";for(let t=0;t<r;++t){s+=o;for(let e=0;e<r;e++)s+=i[t*r+e]?"[40m  [0m":o;s+=h+"\n"}return s+=a+"\n","function"==typeof n&&n(null,s),s});const Kt=function(t,e,n,r){const i=e+1;if(n>=i||r>=i||r<-1||n<-1)return"0";if(n>=e||r>=e||r<0||n<0)return"1";return t[r*e+n]?"2":"1"},jt=function(t,e,n,r){return Kt(t,e,n,r)+Kt(t,e,n,r+1)};var Jt=function(t,e,n){const r=t.modules.size,i=t.modules.data,o=!(!e||!e.inverse),s=e&&e.inverse?"[40m[37m":"[47m[30m",a=function(t,e,n){return{"00":"[0m "+t,"01":"[0m"+e+"▄"+t,"02":"[0m"+n+"▄"+t,10:"[0m"+e+"▀"+t,11:" ",12:"▄",20:"[0m"+n+"▀"+t,21:"▀",22:"█"}}(s,o?"[30m":"[37m",o?"[37m":"[30m"),h="[0m\n"+s;let l=s;for(let t=-1;t<r+1;t+=2){for(let e=-1;e<r;e++)l+=a[jt(i,r,e,t)];l+=a[jt(i,r,r,t)]+h}return l+="[0m","function"==typeof n&&n(null,l),l},Vt={render:function(t,e,n){return e&&e.small?Jt(t,e,n):qt(t,e,n)}};function Qt(t,e){const n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function Zt(t,e,n){let r=t+e;return void 0!==n&&(r+=" "+n),r}var $t=function(t,e,n){const r=zt.getOptions(e),i=t.modules.size,o=t.modules.data,s=i+2*r.margin,a=r.color.light.a?"<path "+Qt(r.color.light,"fill")+' d="M0 0h'+s+"v"+s+'H0z"/>':"",h="<path "+Qt(r.color.dark,"stroke")+' d="'+function(t,e,n){let r="",i=0,o=!1,s=0;for(let a=0;a<t.length;a++){const h=Math.floor(a%e),l=Math.floor(a/e);h||o||(o=!0),t[a]?(s++,a>0&&h>0&&t[a-1]||(r+=o?Zt("M",h+n,.5+l+n):Zt("m",i,0),i=0,o=!1),h+1<e&&t[a+1]||(r+=Zt("h",s),s=0)):i++}return r}(o,i,r.margin)+'"/>',l='viewBox="0 0 '+s+" "+s+'"',u='<svg xmlns="http://www.w3.org/2000/svg" '+(r.width?'width="'+r.width+'" height="'+r.width+'" ':"")+l+' shape-rendering="crispEdges">'+a+h+"</svg>\n";return"function"==typeof n&&n(null,u),u},Xt=_((function(t,n){n.render=$t,n.renderToFile=function(t,r,i,o){void 0===o&&(o=i,i=void 0);const s=e,a='<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+n.render(r,i);s.writeFile(t,a,o)}})),te=(Xt.render,Xt.renderToFile,_((function(t,e){e.render=function(t,e,n){let r=n,i=e;void 0!==r||e&&e.getContext||(r=e,e=void 0),e||(i=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),r=zt.getOptions(r);const o=zt.getImageWidth(t.modules.size,r),s=i.getContext("2d"),a=s.createImageData(o,o);return zt.qrToImageData(a.data,t,r),function(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}(s,i,o),s.putImageData(a,0,0),i},e.renderToDataURL=function(t,n,r){let i=r;void 0!==i||n&&n.getContext||(i=n,n=void 0),i||(i={});const o=e.render(t,n,i),s=i.type||"image/png",a=i.rendererOpts||{};return o.toDataURL(s,a.quality)}})));te.render,te.renderToDataURL;function ee(t,e,n,r,i){const o=[].slice.call(arguments,1),s=o.length,h="function"==typeof o[s-1];if(!h&&!a())throw new Error("Callback required as last argument");if(!h){if(s<1)throw new Error("Too few arguments provided");return 1===s?(n=e,e=r=void 0):2!==s||e.getContext||(r=n,n=e,e=void 0),new Promise((function(i,o){try{const o=dt(n,r);i(t(o,e,r))}catch(t){o(t)}}))}if(s<2)throw new Error("Too few arguments provided");2===s?(i=n,n=e,e=r=void 0):3===s&&(e.getContext&&void 0===i?(i=r,r=void 0):(i=r,r=n,n=e,e=void 0));try{const o=dt(n,r);i(null,t(o,e,r))}catch(t){i(t)}}var ne=dt,re=ee.bind(null,te.render),ie=ee.bind(null,te.renderToDataURL),oe=ee.bind(null,(function(t,e,n){return $t(t,n)}));function se(t,e,n){if(void 0===t)throw new Error("String required as first argument");if(void 0===n&&(n=e,e={}),"function"!=typeof n){if(!a())throw new Error("Callback required as last argument");e=n||{},n=null}return{opts:e,cb:n}}function ae(t){return t.slice(2+(t.lastIndexOf(".")-1>>>0)).toLowerCase()}function he(t){switch(t){case"svg":return Xt;case"txt":case"utf8":return Wt;case"png":case"image/png":default:return Gt}}function le(t,e,n){if(!n.cb)return new Promise((function(r,i){try{const o=dt(e,n.opts);return t(o,n.opts,(function(t,e){return t?i(t):r(e)}))}catch(t){i(t)}}));try{const r=dt(e,n.opts);return t(r,n.opts,n.cb)}catch(t){n.cb(t)}}var ue={create:dt,toCanvas:{create:ne,toCanvas:re,toDataURL:ie,toString:oe}.toCanvas,toString:function(t,e,n){const r=se(t,e,n);return le(function(t){switch(t){case"svg":return Xt;case"terminal":return Vt;case"utf8":default:return Wt}}(r.opts?r.opts.type:void 0).render,t,r)},toDataURL:function(t,e,n){const r=se(t,e,n);return le(he(r.opts.type).renderToDataURL,t,r)},toBuffer:function(t,e,n){const r=se(t,e,n);return le(he(r.opts.type).renderToBuffer,t,r)},toFile:function(t,e,n,r){if("string"!=typeof t||"string"!=typeof e&&"object"!=typeof e)throw new Error("Invalid argument");if(arguments.length<3&&!a())throw new Error("Too few arguments provided");const i=se(e,n,r),o=i.opts.type||ae(t),s=he(o),h=s.renderToFile.bind(null,t);return le(h,e,i)},toFileStream:function(t,e,n){if(arguments.length<2)throw new Error("Too few arguments provided");const r=se(e,n,t.emit.bind(t,"error")),i=he("png"),o=i.renderToFileStream.bind(null,t);le(o,e,r)}};module.exports=ue;
