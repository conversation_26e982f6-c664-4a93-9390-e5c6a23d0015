<!-- 
	就业页面 
-->
<template>
	<view>
		<!-- 水印画布 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 显示设置中的就业信息 -->
		<view v-if="setting.jiuye"
			style="margin:5rpx;padding:20rpx; display: block;vertical-align: middle;text-align: center;word-wrap: break-word;">
			{{setting.jiuye}}
		</view>
		<view>
			<!-- 如果标签为1，显示表单 -->
			<view style="padding:10rpx;" v-if="tag ==1">
				<form>
					<!-- 姓名输入 -->
					<view class=" cu-form-group margin-top-xs">
						<view class="title">姓名</view>
						<uni-easyinput placeholder="请输入姓名" v-model="xm"></uni-easyinput>
					</view>
					<!-- 手机号码输入 -->
					<view class="cu-form-group ">
						<view class="title">手机号码</view>
						<uni-easyinput placeholder="请输入手机号码" v-model="tel"></uni-easyinput>
					</view>
					<!-- 关系输入 -->
					<view class="cu-form-group ">
						<view class="title">关系</view>
						<uni-easyinput placeholder="请输入关系" v-model="gx"></uni-easyinput>
					</view>
					<!-- 学历输入 -->
					<view class="cu-form-group ">
						<view class="title">学历</view>
						<uni-easyinput placeholder="请输入学历" v-model="xl"></uni-easyinput>
					</view>
					<!-- 学校输入 -->
					<view class="cu-form-group ">
						<view class="title">学校</view>
						<uni-easyinput placeholder="请输入学校" v-model="xx"></uni-easyinput>
					</view>
					<!-- 年龄输入 -->
					<view class="cu-form-group ">
						<view class="title">年龄</view>
						<uni-easyinput placeholder="请输入年龄" v-model="nl"></uni-easyinput>
					</view>
					<!-- 专业输入 -->
					<view class="cu-form-group ">
						<view class="title">专业</view>
						<uni-easyinput placeholder="请输入专业" v-model="zy"></uni-easyinput>
					</view>
					<!-- 性别输入 -->
					<view class="cu-form-group ">
						<view class="title">性别</view>
						<uni-easyinput placeholder="请输入性别" v-model="xb"></uni-easyinput>
					</view>
					<!-- 述求输入 -->
					<view class="cu-form-group padding-bottom-xs">
						<view class="title">述求</view>
						<uni-easyinput type="textarea" autoHeight v-model="sq" placeholder="请输入述求"></uni-easyinput>
					</view>
				</form>
				<!-- 提交和清除按钮 -->
				<view style="display: flex;">
					<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:8%;"
						@click='submit'>提交</button>
					<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:5%;"
						@click="clear">清除</button>
				</view>
			</view>
			<!-- 如果标签不是1，显示就业信息列表 -->
			<view v-else>
				<view v-for="jyxx in list" :key="jyxx._id"
					style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx; background-color: #fff;"
					@click="show(jyxx)">
					<view style="display: inline-flex;">
						<!-- 显示图片 -->
						<view v-if="jyxx.pic">
							<img class="img" :src='jyxx.pic' style="width:180rpx;height:180rpx;" />
						</view>
						<view>
							<!-- 显示标题 -->
							<view style="font-size:30rpx;margin-top:10rpx;margin-left:40rpx;font-weight: 800;">
								{{jyxx.title}}
							</view>

							<!-- 显示发布日期 -->
							<view
								style="margin-left:40rpx;margin-top:10rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
								<view>发布日期：</view>
								<view>{{formatDate(jyxx.createTime)}}</view>
								<!-- 如果是新发布的信息，显示新标识 -->
								<view style="margin-left:30rpx;margin-top:-10rpx;" v-if="jyxx.isnew">
									<img style="height:40rpx;width:40rpx;" src='/static/images/new.png' />
								</view>
							</view>
							<!-- 显示浏览和订单数量 -->
							<view
								style="margin-left:30rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
								<view><img style="height:40rpx;width:40rpx;" src='/static/images/review.png' /></view>
								<view style="margin-left:10rpx;">{{jyxx.hits?jyxx.hits:0}}</view>
								<view><img style="height:40rpx;width:40rpx;margin-left:50rpx;"
										src='/static/images/orders.png' /></view>
								<view style="margin-left:10rpx;">{{jyxx.yd?jyxx.yd:0}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 显示子菜单 -->
		<view v-if="showMenu" class="submenu" :style="submenuStyle" style="left:60vw">
			<view @click.stop="selectTag(1)">就业需求</view>
			<view @click.stop="selectTag(2)">就业资讯</view>
		</view>
	</view>
</template>
<script>
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				// 横幅图片来源
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片来源
				bgSrc: uni.getStorageSync('bg'),
				// 是否为公众号
				isgm: uni.getStorageSync('uni_isgm'),
				// 当前页面路径
				currentPage: '../../../pages/tabbar/jiuye/index',
				index: -1,
				value: '',
				// 表单字段
				xm: '',
				xl: '',
				xx: '',
				nl: '',
				xb: '',
				zy: '',
				// 设置
				setting: uni.getStorageSync("setting"),
				sq: '',
				tag: 1,
				gx: '',
				tel: '',
				// 就业信息列表
				list:[],
				tabBarSecondItemLeft: 300,
				isWeChatMiniProgram:false,
				showMenu: false

			};
		},
		onShow() {

		},
		mounted() {
			// 检查环境
			this.checkEnvironment();
		},
		onTabItemTap(item) {
			// 显示子菜单
			this.showSubMenu();
		},
		onPullDownRefresh() {
			console.log('下拉刷新');

			// 模拟数据请求
			setTimeout(() => {
				uni.stopPullDownRefresh(); // 停止下拉刷新
			}, 1500);
		},
		onReachBottom() {
			// 调用 Watermark 组件的方法重新绘制水印

			console.log('上拉触底');
			// 这里增加你的加载更多数据的函数
			this.loadData(this.tag)
			//this.$refs.watermarkComponent.applyWatermark();
		},
		computed: {
			// 子菜单样式
			submenuStyle() {
				return this.isWeChatMiniProgram ? {
					bottom: '10rpx'
				} : {
					bottom: '110rpx'
				};
			}
		},
		methods: {
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 检查环境
			checkEnvironment() {
				if (process.env.VUE_APP_PLATFORM === 'mp-weixin') {
					this.isWeChatMiniProgram = true;
				} else {
					this.isWeChatMiniProgram = false;
				}
			},
			// 选择标签
			selectTag(tag) {
				this.tag = tag;
				if (tag == 1) {
					this.clear();
					this.title = '就业需求（请填写被申请人资料）';
				} else {
					this.loadData(tag);
				}
				this.hideSubMenu();
				this.clear();
			},
			// 显示就业信息
			async show(item) {
				console.log(item._id);
				let action = 'xcx/addHitJiuYe';

				await callCloudFunction({
					name: 'admin',
					data: {
						action: action,
						params: {
							'_id': item._id
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				})
				uni.navigateTo({
					url: "/pages/tabbar/jiuye/view?params=" + encodeURIComponent(JSON
						.stringify(item))
				})
			},
			// 加载数据
			async loadData(v) {

				switch (v) {
					case 1:
					return;
					
					case 2:
						this.action = 'xcx/jyxxlist';
						break;
					default:
						console.warn('未知的 tag 值:', this.tag);
						return;
				}

				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: this.action,
						params: {
							pageSize: this.pageSize,
							pageNumber: this.pageNumber,
							sc: ''
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				console.log(res);
				if (res.result && res.result.page) {
					const now = Date.now();
					const threeDaysInMs = 72 * 60 * 60 * 1000; // 72小时的毫秒数


					res.result.page.list = res.result.page.list.map(item => {
						item.isnew = (now - new Date(item.createTime).getTime()) <= threeDaysInMs;
						return item;
					});


					if (this.pageNumber === 1) {
						this.list = res.result.page.list;
					} else {
						// 如果是上拉加载更多，追加数据到现有列表
						this.list = this.list.concat(res.result.page.list);
					}
					// 增加页码准备下次加载
					if (res.result.page.list.length > 0) {
						this.pageNumber++;
					}


				} else {
					console.warn('无效的响应数据:', res);
				}

			},
			// 显示子菜单
			showSubMenu() {
				this.showMenu = true;
				if (this.menuTimeout) {
					clearTimeout(this.menuTimeout);
				}
				this.menuTimeout = setTimeout(() => {
					this.showMenu = false;
				}, 3000); // 子菜单3秒后自动关闭
			},
			// 隐藏子菜单
			hideSubMenu() {
				this.showMenu = false;
				if (this.menuTimeout) {
					clearTimeout(this.menuTimeout);
				}
			},

			// 清除表单
			clear() {
				this.xm = '';
				this.xl = '';
				this.xx = '';
				this.nl = '';
				this.xb = '';
				this.zy = '';
				this.gx = '';
				this.sq = '';
				this.tel = '';
			},
			// 提交表单
			async submit() {
				await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveJiuYe',
						params: {
							"userId": uni.getStorageSync("uid"),
							"dept": uni.getStorageSync("dept"),
							xm: this.xm,
							xl: this.xl,
							xx: this.xx,
							nl: this.nl,
							xb: this.xb,
							zy: this.zy,
							sq: this.sq,
							gx: this.gx,
							mtel: this.tel,
							sqrxm: uni.getStorageSync("uni_truename"),
							tel: uni.getStorageSync("uni_tel")

						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});

				// 显示确认模态框
				uni.showModal({
					title: '确认',
					content: '已发送，点击确定返回上个页面！',
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							console.log('用户点击确定');
							uni.navigateBack({
								delta: 1 // 返回上一层页面
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			}
		}

	}
</script>

<style>
	.cu-form-group .title {
		min-width: calc(4em + 15px);
	}

	.cu-form-group {
		border-top: none;
	}
</style>