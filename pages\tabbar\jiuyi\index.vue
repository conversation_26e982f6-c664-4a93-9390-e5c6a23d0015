<!-- 
	就医页面 
-->
<template>
	<view>
		<!-- 水印背景 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 显示就医相关说明 -->
		<view v-if="setting.jiuyi"
			style="margin:5rpx;padding:20rpx; display: block;vertical-align: middle;text-align: center;word-wrap: break-word;">
			{{ setting.jiuyi }}
		</view>

		<!-- 沪上直通车和本地直通车表单 -->
		<view v-if="tag == 1 || tag ==2">
			<view style="padding:10rpx;">
				<form>
					<!-- 被申请人姓名输入框 -->
					<view class="cu-form-group margin-top-xs">
						<view class="title">被申请人姓名</view>
						<uni-easyinput placeholder="请输入被申请人姓名" v-model="xm"></uni-easyinput>
					</view>
					<!-- 手机号码输入框 -->
					<view class="cu-form-group ">
						<view class="title">手机号码</view>
						<uni-easyinput placeholder="请输入手机号码" v-model="tel"></uni-easyinput>
					</view>
					<!-- 关系输入框 -->
					<view class="cu-form-group ">
						<view class="title">关系</view>
						<uni-easyinput placeholder="请输入关系" v-model="gx"></uni-easyinput>
					</view>
					<!-- 沪上直通车类别选择 -->
					<view v-if="tag==1" class="cu-form-group ">
						<view class="title">类别</view>
						<custom-picker ref="customPicker" :options="typeList"
							@change="handlePickerChange"></custom-picker>
					</view>
					<!-- 本地直通车医院选择 -->
					<view v-else-if="tag==2" class="cu-form-group ">
						<view class="title">医院</view>
						<custom-picker ref="customPicker" :options="yyList"
							@change="handlePickerChange2"></custom-picker>
					</view>
					<!-- 述求信息输入框 -->
					<view class="cu-form-group padding-bottom-xs">
						<view class="title">述求信息</view>
						<uni-easyinput type="textarea" autoHeight v-model="sq" placeholder="请输入说明信息"></uni-easyinput>
					</view>
				</form>
			</view>
			<!-- 提交和清除按钮 -->
			<view style="display: flex;">
				<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:8%;"
					@click="submit">提交</button>
				<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:5%;"
					@click="clear">清除</button>
			</view>
		</view>

		<!-- 健康资讯列表 -->
		<view v-else-if="tag == 3">
			<view v-for="jkzx in list" :key="jkzx._id"
				style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx; background-color: #fff;"
				@click="show(1,jkzx)">
				<view style="display: inline-flex;">
					<!-- 资讯图片 -->
					<view v-if="jkzx.pic">
						<img class="img" :src='jkzx.pic' />
					</view>
					<view>
						<!-- 资讯标题 -->
						<view style="font-size:30rpx;margin-left:40rpx;font-weight: 800;margin-top:10rpx;">
							{{jkzx.title}}
						</view>
						<!-- 发布日期和新标签 -->
						<view
							style="margin-left:40rpx;margin-top:10rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
							<view>发布日期：</view>
							<view>{{formatDate(jkzx.createTime)}}</view>
							<view style="margin-left:30rpx;margin-top:-10rpx;" v-if="jkzx.isnew">
								<img style="height:40rpx;width:40rpx;" src='/static/images/new.png' />
							</view>
						</view>
						<!-- 浏览量和阅读量 -->
						<view style="margin-left:30rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
							<view><img style="height:40rpx;width:40rpx;" src='/static/images/review.png' /></view>
							<view style="margin-left:10rpx;">{{jkzx.hits?jkzx.hits:0}}</view>
							<view><img style="height:40rpx;width:40rpx;margin-left:50rpx;"
									src='/static/images/orders.png' /></view>
							<view style="margin-left:10rpx;">{{jkzx.yd?jkzx.yd:0}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 名医问诊列表 -->
		<view v-else>
			<view v-for="mywz in list" :key="mywz._id"
				style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx; background-color: #fff;"
				@click="show(2,mywz)">
				<view style="display: inline-flex;">
					<!-- 名医图片 -->
					<view v-if="mywz.pic">
						<img class="img" :src='mywz.pic' />
					</view>
					<view>
						<!-- 名医标题 -->
						<view style="font-size:30rpx;margin-top:10rpx;margin-left:40rpx;font-weight: 800;">
							{{mywz.title}}
						</view>
						<!-- 发布日期和新标签 -->
						<view
							style="margin-left:40rpx;margin-top:10rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
							<view>发布日期：</view>
							<view>{{formatDate(mywz.createTime)}}</view>
							<view style="margin-left:30rpx;margin-top:-10rpx;" v-if="mywz.isnew">
								<img style="height:40rpx;width:40rpx;" src='/static/images/new.png' />
							</view>
						</view>
						<!-- 浏览量和阅读量 -->
						<view style="margin-left:30rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
							<view><img style="height:40rpx;width:40rpx;" src='/static/images/review.png' /></view>
							<view style="margin-left:10rpx;">{{mywz.hits?mywz.hits:0}}</view>
							<view><img style="height:40rpx;width:40rpx;margin-left:50rpx;"
									src='/static/images/orders.png' /></view>
							<view style="margin-left:10rpx;">{{mywz.yd?mywz.yd:0}}</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 子菜单 -->
		<view v-if="showMenu" class="submenu" :style="submenuStyle">
			<view @click.stop="selectTag(1)">沪上直通车</view>
			<view @click.stop="selectTag(2)">本地直通车</view>
			<view @click.stop="selectTag(3)">健康资讯</view>
			<view @click.stop="selectTag(4)">名医问诊</view>
		</view>
	</view>
</template>

<script>
	import CustomPicker from '../../../components/CustomPicker/CustomPicker.vue';
	import {
		callCloudFunction
	} from '/utils/utils.js';

	export default {
		components: {
			CustomPicker
		},
		data() {
			return {
				// 横幅图片地址
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片地址
				bgSrc: uni.getStorageSync('bg'),
				// 是否为管理员
				isgm: uni.getStorageSync('uni_isgm'),
				// 当前页面路径
				currentPage: '../../../pages/tabbar/jiuyi/index',
				index: 0,
				selectedType: '',
				list: [],
				value: '',
				xm: '',
				tel: '',
				gx: '',
				sq: '',
				yy: '',
				tag: 1,
				action: '',
				// 类别列表
				typeList: uni.getStorageSync("setting").typeList ? uni.getStorageSync("setting").typeList.split(';') : [
					'1.乳腺癌、卵巢癌、子宫内膜癌等',
					'2.严重心血管疾病',
					'3.帕金森，癫痫，脑膜瘤等',
					'4.白血病，骨髓瘤，严重糖尿病等',
					'5.胃癌，肠癌，胰腺癌，肾癌，前列腺癌，膀胱癌等',
					'6.颌面头颈肿瘤，口腔癌等',
					'7.肺癌',
					'8.肝癌，胆囊癌等',
					'9.骨癌',
					'10.前列腺癌，膀胱癌，严重烧伤等',
					'11.其它疑难杂症'
				],
				// 医院列表
				yyList: ['请选择', '苏州市第九人民医院', '苏州市永鼎医院'],
				showMenu: true,
				pageSize: 8,
				pageNumber: 1,
				tabBarSecondItemLeft: 150,
				lxIndex: 0,
				lx: '',
				setting: uni.getStorageSync("setting"),
				menuTimeout: null, // 存储延时器
				isWeChatMiniProgram: false
			};
		},
		onLoad(options) {
			// 如果有tag参数，设置当前tag
			if (options.tag) {
				this.tag = parseInt(options.tag);
			}
			if (this.tag < 2) {
				this.tag = 1;
			}
		},
		onShow() {
			// 页面显示时的逻辑
		},
		onTabItemTap(item) {
			this.showSubMenu();
		},
		mounted() {
			this.checkEnvironment();
		},
		computed: {
			// 计算子菜单样式
			submenuStyle() {
				return this.isWeChatMiniProgram ? {
					bottom: '10rpx',
					left: '150rpx'
				} : {
					bottom: '110rpx',
					left: '150rpx'
				};
			}
		},
		onPullDownRefresh() {
			console.log('下拉刷新');
			// 模拟数据请求
			setTimeout(() => {
				uni.stopPullDownRefresh(); // 停止下拉刷新
			}, 1500);
		},
		onReachBottom() {
			console.log('上拉触底');
			// 加载更多数据
			this.loadData(this.tag)
		},
		methods: {
			// 检查运行环境
			checkEnvironment() {
				if (process.env.VUE_APP_PLATFORM === 'mp-weixin') {
					this.isWeChatMiniProgram = true;
				} else {
					this.isWeChatMiniProgram = false;
				}
			},
			// 显示详情页
			async show(index, item) {
				let action = '';
				if (index == 1) {
					action = 'xcx/addHitJkzx';
				} else {
					action = 'xcx/addHitMywz';
				}

				await callCloudFunction({
					name: 'admin',
					data: {
						action: action,
						params: {
							'_id': item._id
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				})
				uni.navigateTo({
					url: "/pages/tabbar/jiuyi/view?index=" + index + "&params=" + encodeURIComponent(JSON
						.stringify(item))
				})
			},
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 计算TabBar第二个位置的左偏移量
			calculateTabBarSecondItemLeft() {
				this.tabBarSecondItemLeft = this.isgm ? 750 / 5 : 750 / 4;
			},
			// 清除表单
			clear() {
				this.xm = '';
				this.tel = '';
				this.sq = '';
				this.yy = '';
				this.selectedType = '';
				this.gx = '';
				if (this.$refs.customPicker.clearSelection()) {
					this.$refs.customPicker.clearSelection(); // 调用子组件的方法清除选择
				}
			},
			// 处理类别选择变化
			handlePickerChange(value) {
				this.selectedType = value;
			},
			// 处理医院选择变化
			handlePickerChange2(value) {
				this.yy = value;
			},
			// 提交表单
			async submit() {
				if (this.tag == 1) {
					this.lx = '沪上直通车';
					this.lxIndex = 1;
				} else {
					this.lx = '本地直通车';
					this.lxIndex = 2;
				}

				await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveJiuYi',
						params: {
							userId: uni.getStorageSync("uid"),
							dept: uni.getStorageSync("dept"),
							username: uni.getStorageSync("user").username,
							xm: this.xm,
							mtel: this.tel,
							sq: this.sq,
							lx: this.lx,
							gx: this.gx,
							yy: this.yy,
							type: this.selectedType,
							lxIndex: this.lxIndex,
							sqrxm: uni.getStorageSync("uni_truename"),
							tel: uni.getStorageSync("uni_tel")
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				uni.showModal({
					title: '确认',
					content: '已发送，点击确定返回上个页面！',
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							// uni.navigateBack({
							// 	delta: 1 // 返回上一层页面
							// });
							this.clear();
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				})；
			},
			hideSubMenu() {
				this.showMenu = false;
				if (this.menuTimeout) {
					clearTimeout(this.menuTimeout);
				}
			},
			selectTag(tag) {
				this.tag = tag;
				this.hideSubMenu();
				if (tag < 3) {
					this.clear();
				} else {
					this.pageNumber = 1;
					this.list=[];
					this.loadData(tag);
				}
			},
			async loadData(v) {
				switch (v) {
					case 1:
					case 2:
						return;
					case 3:
						this.action = 'xcx/jlist';
						break;
					case 4:
						this.action = 'xcx/mlist';
						break;
					default:
						console.warn('未知的 tag 值:', this.tag);
						return;
				}

				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: this.action,
						params: {
							pageSize: this.pageSize,
							pageNumber: this.pageNumber,
							sc: ''
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				if (res.result && res.result.page) {
					const now = Date.now();
					const threeDaysInMs = 72 * 60 * 60 * 1000; // 72小时的毫秒数

					res.result.page.list = res.result.page.list.map(item => {
						item.isnew = (now - new Date(item.createTime).getTime()) <= threeDaysInMs;
						return item;
					});


					if (this.pageNumber === 1) {
						this.list = res.result.page.list;
					} else {
						// 如果是上拉加载更多，追加数据到现有列表
						this.list = this.list.concat(res.result.page.list);
					}
					// 增加页码准备下次加载
					if (res.result.page.list.length > 0) {
						this.pageNumber++;
					}
				} else {
					console.warn('无效的响应数据:', res);
				}
			}
		}
	}
</script>

<style>
	.cu-form-group .title {
		min-width: calc(4em + 15px);
	}

	.cu-form-group {
		border-top: none;
	}

	.img {
		border-radius: 20rpx;
		flex-shrink: 0;
		width: 160rpx;
		height: 160rpx;
	}
</style>

<style scoped>

</style>