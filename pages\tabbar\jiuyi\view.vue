<!-- 
	就医资讯查看页面 
-->
<template>
	<view>
		<!-- 水印画布 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<view class='box'>
			<!-- 标题部分 -->
			<view style="width:700rpx;display: block;float:center;">
				<view class='subtitle' style="width:100%;height:100%;">
					{{item.title}} <!-- 显示项目标题 -->
				</view>
			</view>
			<!-- 按钮部分 -->
			<view style="display:block;right:50rpx;float:right;font-size:10rpx;margin-top:10rpx;">
				<button size="mini" style="height:60rpx;line-height:60rpx;text-align:center;" @click="xd"
					:disabled="isDisabled">参加活动</button> <!-- 参加活动按钮 -->
				<button size="mini" style="margin-left:20rpx;height:60rpx;line-height:60rpx;text-align:center;"
					@click="back">返回</button> <!-- 返回按钮 -->
			</view>

			<!-- 时间信息部分 -->
			<view class='subtime'>
				<view style="display: flex;align:left;">发布日期：{{ formatDate(item.createTime) }} </view> <!-- 显示发布日期 -->
				<view style="margin-left:20rpx;display:flex;align:left;">截止日期：{{ item.jzrq }} </view> <!-- 显示截止日期 -->
			</view>
			<!-- 详细信息部分 -->
			<view class='subdetail'>
				<mp-html :content="item.intro" /> <!-- 显示项目介绍 -->
			</view>
		</view>
	</view>
</template>

<script>
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				// 横幅图片来源
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片来源
				bgSrc: uni.getStorageSync('bg'),
				// 当前项目数据
				item: {},
				// 当前索引
				index: 0
			}
		},
		computed: {
			// 使用计算属性动态生成背景图片的样式
			backgroundStyle() {
				return `background-image: url(${this.item.pic}); width: 150rpx; height: 150rpx;`
			},
			// 判断按钮是否禁用
			isDisabled() {
				if (this.item.jzrq) {
					let targetDateString = this.item.jzrq;
					let targetDate = new Date();
					if (targetDateString.length == 8)
					// 将字符串转换为 Date 对象
					 targetDate = new Date(targetDateString.slice(0, 4), targetDateString.slice(4, 6) - 1, targetDateString.slice(6, 8));	
					else
					 targetDate = new Date(targetDateString)		 
					// 直接加一天
					targetDate.setDate(targetDate.getDate() + 1);
					
					return new Date() > targetDate // 当前日期是否超过截止日期
				} else {
					return false;
				}
			}
		},

		// 页面加载时执行
		onLoad(options) {
			if (options.params) {
				let params;
				try {
					// 解析传入的参数
					params = JSON.parse(decodeURIComponent(options.params));
				} catch (e) {
					if (e instanceof URIError) {
						params = JSON.parse(options.params);
					}
				}
				this.item = params; // 设置当前项目数据

				this.index = options.index ? options.index : 0; // 设置当前索引
				if (this.item.jzrq) {
					// 格式化截止日期
					this.item.jzrq = this.item.jzrq.replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')
				}
			}
		},
		methods: {
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 返回上一层页面
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一层页面
				});
			},
			// 参加活动的处理
			async xd() {
				let content = '是否确认参加活动？'
				let sres = await uni.showModal({
					title: '提示',
					content
				});
				if (!sres.confirm) {
					return // 用户取消
				}
				uni.showLoading({
					mask: true // 显示加载中
				})

				// 调用云函数进行报名
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveJM',
						params: {
							userId: uni.getStorageSync("uid"),
							tag: this.index,
							_id: this.item._id
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});

				let msg = '已报名参与，点击确定返回上个页面！';
				let title = '报名成功';
				if (res.result.state == 'fail') {
					msg = res.result.msg; // 报名失败信息
					title = '报名失败';
				}
				uni.hideLoading(); // 隐藏加载中

				// 显示报名结果
				uni.showModal({
					title: title,
					content: msg,
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack({
								delta: 1 // 返回上一层页面
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});

			}
		}

	}
</script>

<style>
	page {
		background: #EEE; /* 页面背景颜色 */
	}

	.title,
	.box {
		height: 100%;
		font-size: 50rpx; /* 字体大小 */
		margin: 10rpx; /* 外边距 */
		border-radius: 15rpx; /* 圆角 */
		background-color: #fff; /* 背景颜色 */
		padding: 20rpx; /* 内边距 */
	}

	.subtitle {
		font-size: 40rpx; /* 字体大小 */
		font-weight: 900; /* 字体加粗 */
		text-align: center; /* 居中对齐 */
		line-height: 50rpx; /* 行高 */
		height: 60rpx; /* 高度 */
		display: block; /* 块级元素 */
		width: 100vw; /* 宽度 */
	}

	.subtime {
		display: inline-flex; /* 行内弹性布局 */
		text-align: right; /* 右对齐 */
		color: red; /* 字体颜色 */
		font-size: 20rpx; /* 字体大小 */
		margin-top: 10rpx; /* 上边距 */
		margin-left: 100rpx; /* 左边距 */
	}

	.subdetail {
		font-size: 30rpx; /* 字体大小 */
		margin-top: 20rpx; /* 上边距 */
		margin-left: 20rpx; /* 左边距 */
		margin-right: 20rpx; /* 右边距 */
	}
</style>