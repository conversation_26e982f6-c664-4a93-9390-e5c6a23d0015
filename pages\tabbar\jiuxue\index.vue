<!-- 
	就学Tab页  
-->
<template>
	<view>
		<!-- 水印画布 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 标题显示区域 -->
		<view v-if="title"
			style="margin:5rpx;padding:20rpx; display: block;vertical-align: middle;text-align: center;word-wrap: break-word;">
			{{title}}
		</view>
		<view>
			<!--
				就学需求和功模就学表单
			-->
			<view style="padding:10rpx;" v-if="tag ==1 || tag ==2">
				<form>
					<!-- 姓名输入框 -->
					<view class="cu-form-group margin-top-xs">
						<view class="title">姓名</view>
						<uni-easyinput placeholder="请输入姓名" v-model="xm"></uni-easyinput>
					</view>
					<!-- 手机号码输入框 -->
					<view class="cu-form-group ">
						<view class="title">手机号码</view>
						<uni-easyinput placeholder="请输入手机号码" v-model="tel"></uni-easyinput>
					</view>
					<!-- 功模就学特有的阶段输入框 -->
					<view class="cu-form-group" v-if="tag == 1">
						<view class="title">阶段</view>
						<uni-easyinput placeholder="请输入学习阶段" v-model="jd"></uni-easyinput>
					</view>
					<!-- 就学需求特有的分类选择器 -->
					<view class="cu-form-group" v-if="tag == 2">
						<view class="title">分类</view>
						<picker @change="bindPickerChange" :value="index" :range="typeList">
							<view class="uni-input">{{typeList[index]}}</view>
						</picker>
					</view>
					<!-- 年龄输入框 -->
					<view class="cu-form-group ">
						<view class="title">年龄</view>
						<uni-easyinput placeholder="请输入年龄" v-model="nl"></uni-easyinput>
					</view>
					<!-- 关系输入框 -->
					<view class="cu-form-group ">
						<view class="title">关系</view>
						<uni-easyinput placeholder="请输入关系" v-model="gx"></uni-easyinput>
					</view>
					<!-- 性别输入框 -->
					<view class="cu-form-group ">
						<view class="title">性别</view>
						<uni-easyinput placeholder="请输入性别" v-model="xb"></uni-easyinput>
					</view>
					<!-- 功模就学特有的学习情况输入框 -->
					<view class="cu-form-group " v-if='tag==1'>
						<view class="title">学习情况</view>
						<uni-easyinput placeholder="请输入小中高考学习情况" v-model="cj"></uni-easyinput>
					</view>
					<!-- 述求输入框 -->
					<view class="cu-form-group padding-bottom-xs">
						<view class="title">述求</view>
						<uni-easyinput type="textarea" autoHeight v-model="sq" placeholder="请输入述求"></uni-easyinput>
					</view>

				</form>
				<!-- 提交和清除按钮 -->
				<view style="display: flex;">
					<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:8%;"
						@click="submit">提交</button>
					<button class="cu-btn block bg-blue margin-tb-xs lg" style="width:40%;margin-left:5%;"
						@click="clear">清除</button>
				</view>
			</view>
			<!--
				就学资讯列表
			-->
			<view v-else>
				<view v-for="jxxx in list" :key="jxxx._id"
					style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx; background-color: #fff;"
					@click="show(jxxx)">
					<view style="display: inline-flex;">
						<!-- 资讯图片 -->
						<view v-if="jxxx.pic">
							<img class="img" :src='jxxx.pic' style="width:180rpx;height:180rpx;" />
						</view>
						<view>
							<!-- 资讯标题 -->
							<view style="font-size:30rpx;margin-top:10rpx;margin-left:40rpx;font-weight: 800;">
								{{jxxx.title}}
							</view>

							<!-- 发布日期和新标签 -->
							<view
								style="margin-left:40rpx;margin-top:10rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
								<view>发布日期：</view>
								<view>{{formatDate(jxxx.createTime)}}</view>
								<view style="margin-left:30rpx;margin-top:-10rpx;" v-if="jxxx.isnew">
									<img style="height:40rpx;width:40rpx;" src='/static/images/new.png' />
								</view>
							</view>
							<!-- 浏览量和预定量 -->
							<view
								style="margin-left:30rpx;display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
								<view><img style="height:40rpx;width:40rpx;" src='/static/images/review.png' /></view>
								<view style="margin-left:10rpx;">{{jxxx.hits?jxxx.hits:0}}</view>
								<view><img style="height:40rpx;width:40rpx;margin-left:50rpx;"
										src='/static/images/orders.png' /></view>
								<view style="margin-left:10rpx;">{{jxxx.yd?jxxx.yd:0}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>


		</view>

		<!-- 子菜单 -->
		<view v-if="showMenu" class="submenu" :style="submenuStyle" style="left:40vw">
			<view @click.stop="selectTag(1)">功模就学</view>
			<view @click.stop="selectTag(2)">就学需求</view>
			<view @click.stop="selectTag(3)">就学资讯</view>

		</view>

	</view>
</template>

<script>
	const setting = uni.getStorageSync("setting");
	import {
		callCloudFunction
	} from '/utils/utils.js';

	export default {

		data() {
			return {
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				bgSrc: uni.getStorageSync('bg'),
				title: setting ? setting.jiuxue : '',
				isgm: uni.getStorageSync('uni_isgm'),
				currentPage: '../../../pages/tabbar/jiuxue/index',
				list: [],
				index: 0,
				pageSize: 8,
				pageNumber: 1,
				value: '',
				xm: '',
				jd: '',
				nl: '',
				xb: '',
				cj: '',
				sq: '',
				tag: 1,
				tel: '',
				type: '',
				gx: '',
				showMenu: false,
				typeList: ['请选择', '中高考志愿填报', '幼小初升学辅导', '警娃寒暑假托管'],
				isWeChatMiniProgram: false

			};
		},
		computed: {
			// 计算子菜单样式
			submenuStyle() {
				return this.isWeChatMiniProgram ? {
					bottom: '10rpx'
				} : {
					bottom: '110rpx'
				};
			}
		},
		mounted() {
			this.checkEnvironment();
		},
		onShow() {

			//this.showSubMenu();


		},
		onTabItemTap(item) {
			this.showSubMenu();
		},
		onPullDownRefresh() {
			console.log('下拉刷新');

			// 延时停止下拉刷新数据请求
			setTimeout(() => {
				uni.stopPullDownRefresh(); // 停止下拉刷新
			}, 1500);
		},
		onReachBottom() {
			// 调用 Watermark 组件的方法重新绘制水印

			console.log('上拉触底');
			// 这里增加你的加载更多数据的函数
			this.loadData(this.tag)
			//this.$refs.watermarkComponent.applyWatermark();
		},
		methods: {
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 检查运行环境
			checkEnvironment() {
				if (process.env.VUE_APP_PLATFORM === 'mp-weixin') {
					this.isWeChatMiniProgram = true;
				} else {
					this.isWeChatMiniProgram = false;
				}
			},
			// 选择标签
			selectTag(tag) {
				this.tag = tag;
				if (tag == 1) {
					this.clear();
					this.title = '功模就学（请填写被申请人资料）';
				} else if (tag == 2) {
					this.clear();
					this.title = '就学需求（请填写被申请人资料）';
				} else {
					this.loadData(tag);
				}
				this.hideSubMenu();
				this.clear();
			},
			// 绑定选择器变化事件
			bindPickerChange(e) {
				this.index = e.detail.value;
				this.type = this.typeList[e.detail.value];
			},
			// 显示子菜单
			showSubMenu() {
				this.showMenu = true;
				if (this.menuTimeout) {
					clearTimeout(this.menuTimeout);
				}
				this.menuTimeout = setTimeout(() => {
					this.showMenu = false;
				}, 3000); // 子菜单3秒后自动关闭
			},
			// 隐藏子菜单
			hideSubMenu() {
				this.showMenu = false;
				if (this.menuTimeout) {
					clearTimeout(this.menuTimeout);
				}
			},
			// 清除表单数据
			clear() {
				this.xm = '';
				this.jd = '';
				this.nl = '';
				this.xb = '';
				this.cj = '';
				this.sq = '';
				this.type = '';
				this.gx = '';
				this.tel = '';
				this.index = 0;
			},
			// 提交表单
			async submit() {
				let data = {
					"userId": uni.getStorageSync("uid"),
					"dept": uni.getStorageSync("dept"),
					xm: this.xm,
					nl: this.nl,
					xb: this.xb,
					sq: this.sq,
					gx: this.gx,
					mtel: this.tel,
					sqrxm: uni.getStorageSync("uni_truename"),
					tel: uni.getStorageSync("uni_tel")
				};
				if (this.tag == 1) {
					data.jd = this.jd;
					data.cj = this.cj;
					data.lx = '功模就学';
					data.lxIndex = 1;

				} else {
					data.type = this.type;
					data.lx = '就学需求';
					data.lxIndex = 2;
				}
				await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveJiuXue',
						params: data,
						"uniIdToken": uni.getStorageSync("token")
					}
				});

				uni.showModal({
					title: '确认',
					content: '已发送，点击确定返回上个页面！',
					showCancel: false,
					success: (res) => {
						if (res.confirm) {
							uni.navigateBack({
								delta: 1 // 返回上一层页面
							});
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 显示资讯详情
			async show(item) {
				let action = 'xcx/addHitJiuXue';

				await callCloudFunction({
					name: 'admin',
					data: {
						action: action,
						params: {
							'_id': item._id
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				})
				uni.navigateTo({
					url: "/pages/tabbar/jiuxue/view?params=" + encodeURIComponent(JSON
						.stringify(item))
				})
			},
			// 加载数据
			async loadData(v) {

				switch (v) {
					case 1:
					case 2:
						return;
					case 3:
						this.action = 'xcx/jxxxlist';
						break;
					default:
						console.warn('未知的 tag 值:', this.tag);
						return;
				}

				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: this.action,
						params: {
							pageSize: this.pageSize,
							pageNumber: this.pageNumber,
							sc: ''
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				console.log(res);
				if (res.result && res.result.page) {
					const now = Date.now();
					const threeDaysInMs = 72 * 60 * 60 * 1000; // 72小时的毫秒数
		
					
					res.result.page.list = res.result.page.list.map(item => {
						item.isnew = (now - new Date(item.createTime).getTime()) <= threeDaysInMs;
						return item;
					});
					
					
					if (this.pageNumber === 1) {
						this.list = res.result.page.list;
					} else {
						// 如果是上拉加载更多，追加数据到现有列表
						this.list = this.list.concat(res.result.page.list);
					}
					// 增加页码准备下次加载
					if (res.result.page.list.length > 0) {
						this.pageNumber++;
					}
					
					
				} else {
					console.warn('无效的响应数据:', res);
				}

			}
		}
	}
</script>

<style>
	.cu-form-group .title {
		min-width: calc(4em + 15px);
	}

	.cu-form-group {
		border-top: none;
	}
	.img {
		border-radius: 20rpx;
		flex-shrink: 0;
		width: 160rpx;
		height: 160rpx;
	}
</style>