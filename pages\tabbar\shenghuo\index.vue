<!-- 
	首页页面 
-->
<template>
	<view>
		<!-- 水印画布 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />
		<!-- 功能按钮区域 -->
		<view style="display: flex;">
			<!-- 即时团购按钮 -->
			<view style="width:345rpx;height:180rpx;text-align: center;
			background-color: burlywood;
			margin:20rpx 0rpx 20rpx 20rpx;
			border-radius: 20rpx;font-size: 14pt;" @tap="goGroup">
				<img style="height:81rpx;width:89rpx;margin-top:20rpx;" src="/static/img/tabbar/tg.png" />
				<view style="color:#000;font-weight: 600;">即时团购</view>
			</view>
			<!-- 警营110按钮 -->
			<view style="width:345rpx;height:180rpx;text-align: center;
			background-color: burlywood;
			margin:20rpx;
			border-radius: 20rpx;font-size: 14pt;" @tap="jy110">
				<img style="height:81rpx;width:89rpx;margin-top:20rpx;" src="/static/img/tabbar/110.png" />
				<view view style="color:#000;font-weight: 600;">警营110</view>
			</view>
		</view>
		<!-- 分类导航栏 -->
		<view class="cu-list grid col-4 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom">
			<!-- 遍历分类列表 -->
			<view class="cu-item" v-for="(item,index) in lxList" :key="index" @tap="showContent(index)">
				<!-- 分类图标 -->
				<view :class="['cuIcon-'+item.icon,'text-'+item.color,'text-shadow']" style="font-size: 46rpx;">
					<!-- 徽章（如果有） -->
					<view class="cu-tag badge" v-if="item.badge!=0 && item.badge != undefined">
						<block v-if="item.badge!=1">{{item.badge>99?"99+":item.badge}}</block>
					</view>
				</view>
				<!-- 分类名称（当前选中的显示红色） -->
				<text v-if="index==currentIndex"
					style="color:red;border-bottom: 5rpx red solid;margin:0 30rpx 0 30rpx;">{{item.name}}</text>
				<text v-else style="margin:0 30rpx 0 30rpx;">{{item.name}}</text>
			</view>
		</view>
		<!-- 内容列表区域 -->
		<view class="shop-list" v-if="dataLoaded">
			<!-- 警局或警力队的订餐界面 -->
			<view v-if="(dept=='JJG' || dept == 'JLD') && currentIndex == lxList.length-1">
				<view class="caicontent">
					<!-- 左侧日期选择栏 -->
					<scroll-view class="left-side" scroll-y>
						<view class="item center" :class="{active: item._id === current._id}" v-for="item in list"
							:key="item._id" @tap="changeCate(item)">
							<text>{{ item.title }}</text>
						</view>
					</scroll-view>
					<!-- 右侧菜品列表 -->
					<scroll-view class="right-side" scroll-y>
						<checkbox-group @change="checkboxChange">
							<!-- 遍历菜品列表 -->
							<view v-for="(item, index) in current.list" :key="index">
								<view
									style="width:500rpx;margin-top:20rpx;padding-bottom:20rpx;margin-left: 40rpx;align-items: center;display: flex;border-bottom:1px solid #eee;">
									<!-- 菜品图片 -->
									<view class="left" style="100rpx;display: flex;">
										<image :src="item.caipic"
											style="width: 120rpx;height: 120rpx;border-radius:10rpx;" mode="aspectFill">
										</image>
									</view>
									<!-- 菜品信息 -->
									<view class="right" style="350rpx;margin-left:20rpx;">
										<!-- 菜名和复选框 -->
										<view class="itemtext"
											style="width: 350rpx;height: 30rpx;text-align: left;margin-top:10rpx;">
											<text>菜名：{{ item.cainame }}</text>
											<view style="float:right;">
												<checkbox :disabled="item.sysl==0" :data-id="item._id"
													:data-value="index" :value="item._id" :checked="item.checked"
													@tap="checkBox($event, item)" />
											</view>
										</view>
										<!-- 限购提示 -->
										<view class="itemtext"
											style="width: 350rpx;height: 30rpx;text-align: left;margin-top:10rpx;">
											本菜品仅限选购一份
										</view>
										<!-- 价格和剩余数量 -->
										<view class="itemtext"
											style="width: 350rpx;height: 30rpx;text-align: left;margin-top:10rpx;">
											<text>单价： {{ item.pdprice }} </text>
											<text style="margin-left:50rpx;">剩余： {{ item.sysl }} 份</text>
										</view>
									</view>
								</view>
							</view>
						</checkbox-group>

					</scroll-view>
					<!-- 底部固定的订餐按钮 -->
					<view class="fixed-button">
						<!-- 显示总价 -->
						<text
							style="color:#fff;line-height:80rpx;height:80rpx;align:middle;margin:20rpx;">用餐价格:&nbsp;&nbsp;{{price}}</text>
						<!-- 报餐按钮 -->
						<button class="data-v-222ce380" size="mini" style="float:right;margin-right:20rpx;" @tap="caiyd"
							:disabled="isDisable">报餐</button>
					</view>
				</view>
			</view>
			<!-- 其他部门的列表界面 -->
			<view v-else class="list-content">
				<!-- 无数据提示 -->
				<view v-if="itemList.length==0"
					style="font-size: 30rpx;margin-top:50rpx;width:100vw;text-align: center;color:#ccc;">
					暂无数据
				</view>
				<view v-else>
					<!-- 搜索栏 -->
					<uni-search-bar @confirm="search" :focus="false" v-model="searchValue">
					</uni-search-bar>
					<!-- 非订餐分类的列表项 -->
					<view class="shop-item" v-for="item in itemList" :key="item._id" @click="handleDetail(item)"
						v-if="currentIndex < lxList.length -1 ">
						<view>
							<!-- 商品图片 -->
							<image class="img" :src="item.pic"></image>
						</view>
						<view class="shop-content">
							<!-- 商品标题 -->
							<view class="shop-title">
								{{item.title}}
							</view>
							<!-- 发布日期和限量信息 -->
							<view style="display: inline-flex;width:100%;color:#aaa;font-weight: 300;">
								<view>发布日期：{{formatDate(item.createTime)}}</view>
								<view style="margin-left:30rpx;" v-if="item.limit">限量：{{item.limit}}</view>
							</view>

							<!-- 商品标签 -->
							<view class="shop-tag" v-if="item.label">
								<view v-for="label in item.label.split(',')" :key="label" class="tag-item">
									{{label}}
								</view>
							</view>
							<!-- 商品底部信息（价格、浏览量、订单量） -->
							<view class="shop-footer">
								<view class="shop-price">
									<view class="unit" style="line-height: 40rpx;">￥</view>
									{{item.price}}
									<view class="origin-price" v-if="item.originPrice">
										{{item.originPrice}}
									</view>
									<view style="margin-left:50rpx;">
										<img style="height:30rpx;width:30rpx;" src='/static/images/review.png' />
									</view>
									<view style="margin-left:10rpx;">{{item.hits?item.hits:0}}</view>
									<view style="margin-left:50rpx;">
										<img style="height:30rpx;width:30rpx;" src='/static/images/orders.png' />
									</view>
									<view style="margin-left:10rpx;">{{item.yd?item.yd:0}}</view>
								</view>

							</view>
						</view>
					</view>
					<!-- 订餐分类的列表项 -->
					<view class="shop-item" v-for="item in itemList" :key="item._id" @click="handleDetail(item)"
						v-else-if="currentIndex == lxList.length -1">
						<image class="img" :src="item.pic"></image>
						<view class="shop-content">
							<!-- 订餐日期和名称 -->
							<view class="shop-title">
								{{item.rq}} {{item.name}}
							</view>
							<!-- 预约结束时间 -->
							<view class="shop-desc">
								{{item.jzrq}} 结束预约
							</view>
							<!-- 价格和剩余数量 -->
							<view class="shop-footer">
								<view class="shop-price">
									<view class="unit">￥</view>
									{{item.jg}} 剩余{{item.sl?item.sl:0}}份
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- <tab-bar :pagePath="currentPage" :isgm="isgm"></tab-bar> -->

	</view>
	<!-- Watermark ref="watermarkComponent"? -->
</template>
<script>
	// 定义星期数组
	const weekArr = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"]
	// 导入登录函数
	import {
		login
	} from '../../../utils/auth';
	// 导入云函数调用工具
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				// 横幅图片地址
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片地址
				bgSrc: uni.getStorageSync('bg'),
				// 是否禁用报餐按钮
				isDisable: false,
				// 是否为管理员
				isgm: uni.getStorageSync('uni_isgm'),
				// 日期列表
				list: [],
				// 当前页面路径
				currentPage: '../../../pages/tabbar/shenghuo/index',
				// 当前选中的日期和菜品列表
				current: {
					_id: 0,
					list: []
				},
				// 用户角色
				role: "user",
				// 排序类型
				sortType: '',
				// 类型值
				typeValue: '1',
				// 搜索值
				searchValue: '',
				// 当前选中的分类索引
				currentIndex: 0,
				// 每页显示数量
				pageSize: 5,
				// 当前页码
				pageNumber: 1,
				// 用户信息
				user: uni.getStorageSync('user'),
				// 用户卡号
				cardNumber: uni.getStorageSync('user').cardNumber || '',
				// 用户真实姓名
				truename: uni.getStorageSync('user').truename || '',
				// 用户部门
				cs: uni.getStorageSync('user').dept || '',
				// 用户ID
				uid: uni.getStorageSync('uid') || '',
				// 部门
				dept: null,
				// 订餐总价
				price: 0.00,
				// 食堂订餐分类信息
				ctfw: {
					icon: 'presentfill',
					color: 'red',
					badge: 0,
					name: '食堂订餐',
				},
				// 分类列表
				lxList: [],
				// 商品列表
				itemList: [],
				// 用户token
				token: uni.getStorageSync('token'),
				// 数据是否加载完成
				dataloaded: false
			};

		},
		computed: {
			// 计算属性：数据是否加载完成
			dataLoaded() {
				return this.dept !== null;
			}
		},
		onLoad() {
			//this.loadData();
			// 获取部门信息
			this.dept = uni.getStorageSync('dept');

		},
		onShow() {
			console.log("onShow");
			// 获取部门信息
			this.dept = uni.getStorageSync('dept');

			// 加载数据
			this.loadData();
		},
		onPullDownRefresh() {
			console.log('下拉刷新');

			// 模拟数据请求
			setTimeout(() => {
				uni.stopPullDownRefresh(); // 停止下拉刷新
			}, 1500);
		},
		onReachBottom() {
			// 调用 Watermark 组件的方法重新绘制水印

			console.log('上拉触底');
			// 这里增加你的加载更多数据的函数
			this.showContent(this.currentIndex)
			//this.$refs.watermarkComponent.applyWatermark();
		},

		methods: {
			// 跳转到团购页面
			goGroup() {
				uni.navigateTo({
					url: '/pages/tabbar/group/index'
				})
			},
			// 跳转到警营110页面
			jy110() {
				uni.navigateTo({
					url: '/pages/tabbar/jy110/index'
				})
			},
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 报餐函数
			async caiyd() {
				// 禁用报餐按钮
				this.isDisable = true;
				let that = this;
				// 获取选中的菜品列表
				let xdlist = this.list[this.current._id].list.filter(item => item.checked == true);
				let err = 0;
				let errcai = [];

				//可以考虑在这里加入判断有没有超过15时
				console.log(this.current);
					let date = new Date();
					var hour = this.dateFormatHour(date);
					var i = 1;
					if (hour > 15)
						i++;
					date.setHours(0, 0, 0, 0);

					date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * i)
					console.log(date);
					let plandate = new Date(this.current.rq);
					console.log(plandate);
					// 判断是否超过15点
					if (plandate < date)
					{
						uni.showModal({
							showCancel: false,
							content: '已超过15点，不能进行当前的订餐！',
							success: (res) => {
								this.list[this.current._id].list.forEach((item) => {
									item.checked = false;
								});
								this.price = 0.00;
							}
						})
						return;
					}





				if (xdlist.length > 0) {
					// 检查每个菜品的库存
					for (var i = 0, lenx = xdlist.length; i < lenx; ++i) {
						let res = await callCloudFunction({
							name: 'ycxcx',
							data: {
								action: 'caipdinfo',
								params: {
									_id: xdlist[i]._id
								}
							}
						})
						let pdtotal = parseFloat(res.pdtotal);
						let ydtotal = res.ydtotal == "undefined" ? 0 : parseFloat(res.ydtotal);
						if (pdtotal <= ydtotal) {
							err = 1;
							errcai.push(res.cainame);
						}
					}

					if (err > 0) {
						// 显示库存不足的提示
						uni.showModal({
							showCancel: false,
							content: errcai.join(',') + '已经预订完了，报餐失败！请重新选择',
							success: (res) => {
								that.isDisable = false;
							}
						})
					} else {
						// 确认订餐
						let sres = await uni.showModal({
							content: '确认进行订餐吗？',
						});

						if (sres.confirm) {
							// 扣款
							let yres = await callCloudFunction({
								name: 'ycxcx',
								data: {
									action: 'wea',
									params: {
										cardNumber: that.cardNumber,
										walletID: 1,
										amount: that.price
									}
								}
							});

							console.log(yres);
							let str = yres.result.data.replace(/<.*?>/ig, "").replace('\r\n', '');
							if (str[0] == "S") {
								// 扣款成功，更新库存
								for (var i = 0, lenx = xdlist.length; i < lenx; ++i) {
									await callCloudFunction({
										name: 'ycxcx',
										data: {
											action: 'ycydkc',
											params: xdlist[i]
										}
									});

								}
								// 创建订单
								await callCloudFunction({
									name: 'ycxcx',
									data: {
										action: 'ycyddd',
										params: {
											ydr: that.truename,
											createTime: new Date(),
											yddate: that.current.rq,
											ydr_id: that.uid,
											ydprice: that.price,
											cs: that.cs,
											isUsed: 0,
											ydlist: xdlist
										}
									}
								});
								// 显示订餐成功提示
								uni.showModal({
									showCancel: false,
									content: '报餐预订成功，谢谢使用！',
									success: (res) => {
										that.getPage();
										that.price = 0.00;
									}
								});

							} else {
								// 扣款失败
								let msg = str.split(",")[1];
								uni.showModal({
									showCancel: false,
									content: msg,
									success: (res) => {
										this.getPage();
										this.price = 0.00;
									}
								})
							}

						} else {
							that.isDisable = false;
						}
					}
				} else {
					// 未选择菜品的提示
					uni.showModal({
						showCancel: false,
						content: '请选择菜品后再进行报餐！',
						success: (res) => {
							that.isDisable = false;
						}
					})
				}
			},
			// 复选框变化处理函数
			checkboxChange: function(e) {
				//this.price = parseFloat(this.price).toFixed(2);
				this.price = 0.00
				var items = this.list[this.current._id].list,
					values = e.detail.value;
				for (var i = 0, lenI = items.length; i < lenI; ++i) {
					const item = items[i]
					if (values.indexOf(item._id) >= 0) {
						this.$set(item, 'checked', true)
						this.price += parseFloat(item.pdprice);
					} else {
						this.$set(item, 'checked', false)
					}
				}
				this.price = this.price.toFixed(2)
				e.detail.value = "";
			},
			// 单个复选框点击处理函数
			checkBox(e, item) {
				let that = this;
				// let box = (item.checked = !item.checked);
			},
			// 搜索函数
			async search() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/spslist',
						params: {
							'sc': this.searchValue
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				});
				this.itemList = res.result.data;

			},
			// 改变类型处理函数
			handleChangeType(typeValue) {
				this.typeValue = typeValue
			},
			// 处理详情页跳转
			async handleDetail(item) {
				await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/addHitLife',
						params: {
							'_id': item._id
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				})


				uni.navigateTo({
					url: "/pages/tabbar/shenghuo/view?index=" + this.currentIndex + "&params=" +
						encodeURIComponent(JSON.stringify(item))
				})
			},
			// 处理排序
			handleSort(sortType) {
				this.sortType = sortType
			},
			bindZan() {},
			// 显示内容
			showContent(v) {
				this.dept = uni.getStorageSync('dept');
				if (v == -1) {
					v = 0;
				}
				if (v != this.currentIndex) {
					this.currentIndex = v;
					this.pageNumber = 1;
					uni.setStorageSync("shqIndex", v);
				}


				if (v == this.lxList.length - 1) {
					this.showDc();
				} else {

					this.showFl(this.lxList[v].name);
				}

				//this.itemList = this.itemLists[v];
			},
			// 显示分类内容
			async showFl(v) {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/splist',
						params: {
							pageSize: this.pageSize,
							pageNumber: this.pageNumber,
							'lx': v,
							dept: this.dept
						},
						"uniIdToken": uni.getStorageSync('token')
					}
				});
				const now = Date.now();
				const threeDaysInMs = 72 * 60 * 60 * 1000; // 72小时的毫秒数

				res.result.page.list = res.result.page.list.map(item => {
					// 检查dept字段，并据此更新label字段
					let prefix = item.dept === 'JJG' ? '区' : '所';
					// 如果label字段不存在或为空字符串，直接赋值为prefix
					// 如果label不为空，前面添加prefix和逗号
					item.label = item.label ? `${prefix}, ${item.label}` : prefix;
					if ((now - new Date(item.createTime).getTime()) <= threeDaysInMs) {
						item.label = 'NEW,' + item.label;
					}

					return item;
				});

				if (this.pageNumber === 1) {
					this.itemList = res.result.page.list;

				} else {
					// 如果是上拉加载更多，追加数据到现有列表
					this.itemList = this.itemList.concat(res.result.page.list);
				}
				// 增加页码准备下次加载
				if (res.result.page.list.length > 0) {
					this.pageNumber++;
				}
				// 调用 Watermark 组件的方法重新绘制水印
				//this.$refs.watermarkComponent.drawWatermark('xxxx 130000000000');

			},
			// 显示订餐内容
			async showDc() {
				this.pageNumber === 1
				if (this.dept == 'JJG' || this.dept == 'JLD') {
					this.getPage();
				} else {
					let res = await callCloudFunction({
						name: 'admin',
						data: {
							action: 'xcx/splist',
							params: {
								pageSize: this.pageSize,
								pageNumber: this.pageNumber,
								'lx': '食堂订餐',
								dept: this.dept
							},
							"uniIdToken": uni.getStorageSync('token')
						}
					});
					//this.itemList = res.result.page.list;
					// 如果是初次加载或下拉刷新，设置 itemList 为返回的列表
					if (this.pageNumber === 1) {
						this.itemList = res.result.page.list;
					} else {
						// 如果是上拉加载更多，追加数据到现有列表
						this.itemList = this.itemList.concat(res.result.page.list);
					}
					// 增加页码准备下次加载
					if (res.result.page.list.length > 0) {
						this.pageNumber++;
					}
					for (var k = 0; k < this.itemList.length; k++) {


						this.itemList[k].sl = (this.itemList[k].limit || 0) - (this.itemList[k].yd || 0);

					}

				}

			},

			// 加载数据
			async loadData() {
				//this.lxList=['111','222']
				//this.currentIndex = 0;
				this.currentIndex = uni.getStorageSync("shqIndex") || 0;

				let _that = this;
				this.list = [];
				let date = new Date();
				var hour = this.dateFormatHour(date);
				var i = 1;
				if (hour > 15)
					i++;
				date.setHours(0, 0, 0, 0);
				date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * i)

				for (var j = 0; j < 7; j++) {
					var result = this.dateFormat(date);
					let mn = {
						rq: result.rq,
						title: result.wn,
						_id: j,
						list: [],
						checked: false
					};
					this.list.push(mn);
					date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * 1)
				}
				uni.getSystemInfo({
					success: function(res) {
						_that.windowHeight = res.windowHeight - 300 + 'px';
					}
				});

				this.loadSy();

				//this.loadLx();
			},
			// 加载类型
			async loadLx() {
				let res = await callCloudFunction({
					name: 'user-center',
					data: {
						action: 'user/login',
						params: {
							"username": "admin",
							"password": "F5BB0C8DE146C67B44BABBF4E6584CC0"
						}
					}
				});
				// console.log(res);
				this.user = res.result;
				uni.setStorageSync("uid", res.result.userId);
				uni.setStorageSync("dept", res.result.dept);
				uni.setStorageSync("token", res.result.uniIdToken);
				uni.setStorageSync("username", res.result.userName);
				uni.setStorageSync("user", res.result);

				this.token = res.result.uniIdToken;

				await this.loadSy();

			},
			// 加载首页数据
			async loadSy() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/sylist',
						params: {},
						"uniIdToken": uni.getStorageSync('token')
					}
				});
				if (res.result.state == 'needLogin') {
					login();
				} else {
					this.lxList = res.result.names;
					uni.setStorageSync("setting", res.result.lastConfigRecord);
					if (res.result.shes) {
						uni.setStorageSync("shes", res.result.shes);

					}

					this.lxList.push(this.ctfw);
					this.showContent(this.currentIndex)
				}


				// res 为数据库查询结果				
			},
			// 改变角色的方法(目前为空)
			changeRole() {

			},
			// 获取页面数据的异步方法
			async getPage() {
				// 获取当前日期
				let currday = this.dateFormat(new Date()).rq
				// 设置禁用状态为false
				this.isDisable = false;
				// 调用云函数获取菜品数据
				let res = await callCloudFunction({
					name: 'ycxcx',
					data: {
						action: 'caipd',
						params: {
							currday: currday
						}
					}
				});
				// 清空列表中每一项的子列表
				for (var m = 0; m < this.list.length; m++) {
					this.list[m].list = [];
				}

				// 获取返回的菜品列表
				let elist = res.result.data;
				// 处理每个菜品项
				for (var k = 0; k < elist.length; k++) {
					let pd = elist[k].pddate;
					// 设置选中状态为false
					elist[k].checked = false;
					// 如果预定总数未定义或为null，设置为0
					if (elist[k].ydtotal == "undefined" || elist[k].ydtotal == null)
						elist[k].ydtotal = 0;
					// 计算剩余数量
					elist[k].sysl = elist[k].pdtotal - elist[k].ydtotal;
					// 查找对应日期的列表项
					var q = this.list.filter(item => item.rq == pd);
					// 如果找到，将菜品添加到对应日期的子列表中
					if (q.length > 0)
						q[0].list.push(elist[k]);
				}
				// 设置当前选中项为列表第一项
				this.current = this.list[0];
			},
			// 格式化小时的方法
			dateFormatHour(time) {
				var dt = new Date(time);
				var hours = dt.getHours() < 10 ? "0" + dt.getHours() : dt.getHours();
				return hours;
			},
			// 格式化日期的方法
			dateFormat(time) {
				var dt = new Date(time);
				var year = dt.getFullYear();
				var month = dt.getMonth() + 1 < 10 ? "0" + (dt.getMonth() + 1) : dt.getMonth() + 1;
				var day = dt.getDate() < 10 ? "0" + dt.getDate() : dt.getDate();
				var week = dt.getDay();
				var weekname = weekArr[week] + `(${month}-${day})`;
				// 返回格式化后的日期对象
				return {
					rq: `${year}-${month}-${day}`,
					week: week,
					wn: weekname
				};
			},
			// 切换分类的方法
			changeCate(item) {
				console.log(item);
				console.log(this.list);
				// 如果不是禁用状态
				if (!this.isDisable) {
					// 取消当前列表所有项的选中状态
					this.list[this.current._id].list.forEach((item) => {
						item.checked = false;
					});
					// 重置价格
					this.price = 0.00;

					// 获取当前日期和时间
					let date = new Date();
					var hour = this.dateFormatHour(date);
					var i = 1;
					// 如果当前时间超过15点，i加1
					if (hour > 15)
						i++;
					// 设置日期为当天0点
					date.setHours(0, 0, 0, 0);
					// 增加i天
					date.setTime(date.getTime() + 1000 * 60 * 60 * 24 * i)
					console.log(date);
					// 获取计划日期
					let plandate = new Date(item.rq);
					console.log(plandate);
					// 如果计划日期早于当前日期，清空列表
					if (plandate < date)
						item.list = [];
					// 设置当前选中项
					this.current = item;

				} else {
					// 如果是禁用状态，显示提示框
					uni.showModal({
						showCancel: false,
						content: '正在为您进行订餐，请稍候片刻！',
						success: (res) => {}
					})
				}
			},
			// 导航到列表页的方法
			navToList(item) {
				const arr = [];
				// 遍历当前分类的子分类
				this.current.child.forEach(cate => {
					arr.push({
						_id: cate._id,
						name: cate.name
					})
				})
				// 导航到产品列表页
				this.navTo(`/pages/product/list?cateId=${item._id}&firstCateId=${item.parent_id}`)
			}

		}
	}
</script>



<style lang="scss" scoped>
	page {
		background: #f2f2f2;
	}

	#watermark-canvas {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 1000;
		width: 100%;
		height: 100vh;
		pointer-events: none;
		opacity: 0.5;
	}

	.shop-list {
		display: flex;
		font-size: 10pt;
		background-color: #fff;
		margin-top: 10px;

		.list-left {
			position: fixed;
			width: 160rpx;
			text-align: center;
			background: rgb(248, 248, 248);
			height: 100%;
			overflow-y: auto;
			padding-bottom: 36rpx;

			&-item {
				padding: 24rpx 0;

				&.active {
					background: #fff;

					&::before {
						content: '';
						position: absolute;
						left: 4rpx;
						border-radius: 2px;
						width: 8rpx;
						height: 40rpx;
						background: linear-gradient(to right, #feef3c, #f3cd34);
					}
				}
			}
		}

		.list-content {
			margin-left: 10rpx;

			.list-sort {
				width: 730rpx; //580rpx;
				display: flex;
				justify-content: flex-end;

				.sort-item {
					display: flex;
					align-items: center;
					padding: 12rpx 18rpx;
					color: rgba(0, 0, 0, .7);

					&.active {
						color: #f3cd34;
					}

					.price-sort-icon {
						margin-left: 4rpx;
						width: 20rpx;
						height: 24rpx;
					}
				}
			}

			.shop-item {
				display: flex;
				padding: 10rpx;
				padding-right: 0;
				width: 98vw;

				.img {
					border-radius: 15rpx;
					margin-left: 10rpx;
					flex-shrink: 0;
					width: 160rpx;
					height: 160rpx;
				}

				.shop-content {
					flex: 1;
					padding: 10rpx;
					display: flex;
					margin-left: 20rpx;
					flex-direction: column;

					.shop-title {
						font-size: 26rpx;
						word-break: break-all;

						.limited {
							padding: 2rpx 6rpx;
							border-radius: 6rpx;
							line-height: 26rpx;
							color: rgba(0, 0, 0, 0.8);
							background: linear-gradient(to right, #feef3c, #f3cd34);
							font-size: 20rpx;
							display: inline-block;
						}
					}

					.shop-desc {
						margin-top: 4rpx;
						font-size: 20rpx;
						color: rgba(0, 0, 0, .4);
					}

					.shop-tag {
						display: flex;
						flex-wrap: wrap;
						color: #f3cd34;

						.tag-item {
							margin: 4rpx 8rpx 0 0;
							font-size: 20rpx;
							border-radius: 6rpx;
							padding: 0 4rpx;
							display: inline-block;
							border: 1px solid #feef3c;
						}
					}

					.shop-footer {
						display: flex;
						justify-content: space-between;

						.shop-price {
							margin-top: 4rpx;
							display: flex;
							color: rgb(231, 79, 58);
							align-items: center;

							.unit {
								align-self: flex-end;
								font-size: 20rpx;
							}

							.origin-price {
								margin-top: 2rpx;
								margin-left: 4rpx;
								font-size: 20rpx;
								text-decoration: line-through;
								color: rgb(0, 0, 0, 0.4);
							}
						}

						.shop-button {
							background: linear-gradient(to right, #feef3c, #f3cd34);
							border-radius: 6rpx;
							height: 36rpx;
							display: flex;
							align-items: center;
							padding: 0 12rpx;
							border-radius: 36rpx;
						}
					}
				}
			}
		}
	}


	.right {
		font-size: 24rpx;
	}

	.app {
		height: 100%;
	}

	.caicontent {
		flex: 1;
		display: flex;
		height: 70vh;
		//padding-top: 12rpx;
		padding-left: 0rpx;
		padding-right: 0rpx;
		padding-bottom: 0rpx;
		overflow: hidden;
	}

	.left-side {
		flex-shrink: 0;
		width: 200rpx;
		height: 100%;
		overflow-y: hidden;
		background-color: #f2f2f2;

		.item {
			height: 90rpx;
			font-size: 24rpx;
			color: #555;

			text {
				line-height: 90rpx;
				margin-left: 20rpx;
				margin-bottom: 20rpx;
			}
		}

		.active {
			font-size: 26rpx;
			color: $base-color;
			font-weight: 700;
			background-color: #fff;
			position: relative;

			&::before {
				content: '';
				position: absolute;
				left: 0;
				top: 30rpx;
				width: 6rpx;
				height: 30rpx;
				background-color: $base-color;
				border-radius: 0 4rpx 4rpx 0;
			}
		}
	}

	.right-side {
		flex: 1;
		height: 100%;
		background-color: #fff;

		.cate-image {
			width: calc(100% - 40rpx);
			height: 200rpx;
			margin: 0 20rpx;
			border-radius: 8rpx;
		}

		.wrap {
			display: flex;
			flex-wrap: wrap;
			padding: 0 20rpx 20rpx;
		}

		.item {
			flex-shrink: 0;
			justify-content: flex-start;
			align-items: center;
			width: 30%;
			padding: 30rpx 0 0;

			&:nth-child(3n-1) {
				width: 40%;
			}
		}

		.icon {
			width: 108rpx;
			height: 108rpx;
			margin-bottom: 16rpx;
		}

		.btn {
			margin-left: 20rpx;
		}

		.tit {
			width: 140rpx;
			font-size: 24rpx;
			color: #333;
			text-align: center;
			line-height: 1.4;
		}
	}

	.fixed-button {
		position: fixed;
		right: 30rpx;
		bottom: 90rpx;
		width: 500rpx;
		background-color: red;
		height: 80rpx;
		line-height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-radius: 10rpx;
		padding: 0 20rpx;
		z-index: 1000;
	}
</style>
