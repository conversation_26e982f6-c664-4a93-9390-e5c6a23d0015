<!-- 
	我的更多消息页面 
-->
<template>
	<view>
		<!-- 背景图片和水印 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 顶部个人信息 -->
		<view class="bg-white">
			<view class="flex padding">
				<!-- 用户头像 -->
				<view class="padding-lr-xs">
					<view class="cu-avatar lg round" :style="{ backgroundImage: 'url(' + avatar + ')' }">
					</view>
				</view>
				<!-- 用户名和部门信息 -->
				<view class="padding-xs text-xl text-black">
					<view>你好，{{username}}</view>
					<view class="cu-tag round line-green sm">{{dept}}</view>
				</view>
				<!-- 二维码图片 -->
				<view style="margin-left:180rpx;float:left;">
					<image :src='ewmimg' style="width:120rpx;height:120rpx;" @click='showCode()'></image>
				</view>
			</view>
		</view>

		<!-- 常用功能 -->
		<view class="cu-bar margin-lr-xs margin-top-sm grid col-4 no-border bg-white radius-lg-top">
			<view class="action">
				<text class="text-xl text-black">消息列表</text>
			</view>
			<view></view>
				<view></view>
				<!-- 返回按钮 -->
				<button size="mini" @click="back" style="float:right;right:20rpx;height:60rpx;line-height:60rpx;text-align:center;">返回</button>
			
		</view>
		<!-- 消息列表容器 -->
		<view class="cu-list grid col-5 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom">
			<view style="width:700rpx;margin:20rpx;background-color: #fff;border-radius: 25rpx;min-height: 65vh;">
				<!-- 消息列表项 -->
				<view v-if="msgList.length>0" class="cu-item" v-for="(item,index) in msgList" :key="index"
					@click="showMsg(item)" style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:20rpx;
					background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;
					width:100%;display: inline-block;">
					<view>
						<!-- 消息日期和查看按钮 -->
						<view class='text-grey'
							style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;">
							{{ formatDate(item.createTime) }}
							<view style="float:right;margin-right: 30rpx;">点击查看</view>
						</view>
						<!-- 消息标题 -->
						<view class='text-grey'
							style="margin-top:5px;padding-left:4%;padding-right:2%;text-align: left; overflow: hidden;">
							{{ item.title }}
						</view>
						<!-- 消息类型和简介 -->
						<view class='text-red' style="display: inline-flex;width:100%;">
							<text style="margin-left:4%;"> {{item.lx}}</text>
							<text style="margin-left:4%;">{{item.intro}}</text>
						</view>
						<!-- 回复信息（如果有） -->
						<view class='text-red' style="display: inline-flex;width:100%;" v-if="item.hf">
							<text style="margin-left:4%;color:red;">已回复：{{item.hf}}</text>
						</view>
					</view>
				</view>
				<!-- 无数据时显示 -->
				<view v-else style="min-height:280rpx;margin-left:150rpx;margin-top:40rpx;font-size:30rpx;color:#AAA;">
					暂无记录
				</view>
			</view>

		</view>

		<!-- 二维码弹窗 -->
		<uni-popup ref="popup1" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;
					justify-content: center;align-items: center;padding: 5upx;">
					<img :src="ewmcode" style="width:450rpx;" />
				</view>
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 15upx;">
						<view @tap="ewm_close" style="margin-left: 200rpx;padding: 8upx 15upx 8upx 15upx;">
							确 定
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 评分弹窗 -->
		<uni-popup ref="popup2" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;padding-bottom:50rpx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;margin-bottom: 50rpx;;
					justify-content: center;align-items: center;padding: 5upx;">
					<text style="font-size:30rpx;margin-bottom:30rpx;">请打分</text>
					<l-starRate :value="star" @input="getvalue" :disabled="false"></l-starRate>
				</view>
				<button @tap="saveStar" size="mini" type="primary">确定 </button>

			</view>
		</uni-popup>
		<!-- 消息详情弹窗 -->
		<uni-popup ref="popup3" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;margin-bottom: 20rpx;;
					justify-content: left;align-items: left;padding: 5upx;">
					<view style="margin-top:10rpx;" v-if="currentItem.createTime">
						<text style="font-size:30rpx;">日期：</text>
						<text style="font-size:30rpx;">{{formatDate(currentItem.createTime)}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.title">
						<text style="font-size:30rpx;">标题：</text>
						<text style="font-size:30rpx;">{{currentItem.title}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.lx">
						<text style="font-size:30rpx;">分类：</text>
						<text style="font-size:30rpx;">{{currentItem.lx}}</text>
					</view>

					<view style="margin-top:10rpx;" v-if="currentItem.intro">
						<text style="font-size:30rpx;">需求：</text>
						<text style="font-size:30rpx;">{{currentItem.intro}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.doc.type">
						<text style="font-size:30rpx;">类别：</text>
						<text style="font-size:30rpx;">{{currentItem.doc.type}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.doc.yy">
						<text style="font-size:30rpx;">类别：</text>
						<text style="font-size:30rpx;">{{currentItem.doc.yy}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.doc.gx">
						<text style="font-size:30rpx;">关系：</text>
						<text style="font-size:30rpx;">{{currentItem.doc.gx}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.hf">
						<text style="font-size:30rpx;">处理：</text>
						<text style="font-size:30rpx;color:red;">{{currentItem.hf}}</text>
					</view>

				</view>
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 40rpx;">
						<button type='primary' size="mini" @tap="ewm_close">确 定</button>
					</view>
				</view>
			</view>
		</uni-popup>
	</view>
</template>



<script>
	import SettingsDialog from "@/components/SettingsDialog/SettingsDialog.vue";
	import {
		callCloudFunction
	} from '/utils/utils.js';
	import {
		createQrCodeImg
	} from "../../../utils/wxqrcode.js";
	export default {
		components: {
			SettingsDialog
		},
		data() {
			return {
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				bgSrc: uni.getStorageSync('bg'),
				currentPage: '../../../pages/tabbar/my/index',
				username: uni.getStorageSync("username"),
				dept: uni.getStorageSync("dept"),
				uid: uni.getStorageSync("uid"),
				ewmimg: '/static/images/ewm.png',
				Message: '',
				ewmcode: '',
				index: 0,
				star: 0,
				msgList: [],
				showSettingsDialog: false,
				action: 'xcx/myMsg',
				params: {
					pageNumber: 1,
					pageSize: 10,
					userId: uni.getStorageSync("uid")
				},
				list: [],
				currentItem: {
					doc:{}
				}

			}
		},
		onLoad(option) {
			// 页面加载时创建二维码并加载数据
			this.createQr(this.uid);
			this.loadData();
		},
		onPullDownRefresh() {
			console.log('下拉刷新');
		
			// 模拟数据请求
			setTimeout(() => {
				uni.stopPullDownRefresh(); // 停止下拉刷新
			}, 1500);
		},
		onReachBottom() {
			// 调用 Watermark 组件的方法重新绘制水印
		
			console.log('上拉触底');
			// 这里增加你的加载更多数据的函数
			this.loadData()
			//this.$refs.watermarkComponent.applyWatermark();
		},
		methods: {
			// 获取评分值
			getvalue(e) {
				this.star = e;
			},
			// 显示二维码
			showCode() {
				this.ewmcode = this.ewmimg;
				this.show = 1;
				this.$refs.popup1.open()
			},
			// 关闭弹窗
			ewm_close() {
				this.$refs.popup1.close();
				this.$refs.popup2.close()
				this.$refs.popup3.close()

			},
			// 加载消息数据
			async loadData() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: this.action,
						params: this.params,
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				if (res.result.code === 0) {
				
													
					if (this.params.pageNumber === 1) {
						this.msgList = res.result.page.list;
					} else {
						// 如果是上拉加载更多，追加数据到现有列表
						this.msgList = this.msgList.concat(res.result.page.list);
					}
					// 增加页码准备下次加载
					if (res.result.page.list.length > 0) {
						this.params.pageNumber++;
					}			
					
					
					
					
				} else {
					console.error('加载数据失败:', res.result.msg);
				}
			},
			// 保存评分
			async saveStar() {
				if (this.currentItem.star) {
					this.$refs.popup2.close()
					this.showModal("这个预约已评分，请勿重复提交");
					return;
				}
				let type = "";
				switch (this.index) {
					case 0:
						type = 'shq';
						break;
					case 1:
						if (this.dept == 'JJG' || this.dept == 'JLD') {
							type = 'dc';
						} else {
							type = 'jgdc';
						}
						break;
					case 2:
						type = 'group';
						break;
					case 3:
						type = 'jkzx';
						break;
					case 4:
						type = 'mywz';
						break;
					default:
						break;
				}
				let params = {
					type: type,
					_id: this.currentItem._id,
					star: this.star
				}


				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveStar',
						params: params,
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				if (res.result.state === 'ok') {

				} else {
					this.showModal('评分失败:'+res.result.msg);
				}



				this.star = 0;
				this.$refs.popup2.close()
			},
			// 返回上一页
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一层页面
				});
			},
			// 显示订单二维码
			showOrderEwm(v) {
				this.currentItem = v;
				this.show = 1;
				this.star = 0;
				this.$refs.popup2.open()
			},
			// 显示模态框
			showModal(content) {
				uni.showModal({
					title: '提示',
					content: content,
					showCancel: false
				})
			},
			// 创建二维码
			createQr(code) {
				this.ewmimg = createQrCodeImg(code, {
					size: parseInt(120) //二维码大小  
				});
			},
			// 显示消息详情
			showMsg(item) {
				this.currentItem = JSON.parse(JSON.stringify(item));
				//this.show = 1;
				this.$refs.popup3.open()
			},
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			}

		}
	}
</script>

<style>
	page {
		background: #EEE;
	}
</style>