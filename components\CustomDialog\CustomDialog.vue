<template>
  <view class="dialog" v-if="visible">
    <view class="dialog-content">
      <view class="dialog-title">{{ title }}</view>
      <input class="dialog-input" v-model="inputValue" :placeholder="placeholderText" />
      <view class="dialog-actions">
        <button size='mini' @click="confirm">确定</button>
        <button size='mini' @click="cancel">取消</button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '',
      placeholderText: '',
      inputValue: '',
      resolve: null,
      reject: null
    };
  },
  methods: {
    show(title, placeholderText) {
      this.title = title;
      this.placeholderText = placeholderText;
      this.visible = true;
      return new Promise((resolve, reject) => {
        this.resolve = resolve;
        this.reject = reject;
      });
    },
    confirm() {
      this.resolve(this.inputValue);
      this.visible = false;
    },
    cancel() {
      this.reject('取消');
      this.visible = false;
    }
  }
};
</script>

<style scoped>
.dialog {
  display: flex;
  justify-content: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.dialog-content {
  background: #fff;
  padding: 40rpx;
  border-radius: 16rpx;
  width: 80%;
  max-width: 560rpx;
  text-align: center;
}

.dialog-title {
  font-size: 32rpx;
  margin-bottom: 40rpx;
}

.dialog-input {
  width: 100%;
  padding: 15rpx;
  font-size: 25rpx;
  margin-bottom: 40rpx;
  height:70rpx;
  border: 2rpx solid #ddd;
  border-radius: 12rpx;
}

.dialog-actions {
  display: flex;
  justify-content: space-around;
}

.dialog-button {
  padding: 5rpx 30rpx;
  font-size: 12rpx;
  border: none;
  background-color: #007aff;
  color: #fff;
  border-radius: 8rpx;
  cursor: pointer;
}

.dialog-button:hover {
  background-color: #005bb5;
}
</style>
