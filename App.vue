<script>
	export default {
		globalData: { // 定义全局数据对象
			role: "user", // 设置用户角色为"user"
			watermarkDataURL:"", // 水印数据URL，初始为空字符串
			setting:{ // 设置对象
				banner:'', // banner设置，初始为空字符串
				jishu:'' // 技术设置，初始为空字符串
			},
			isgm:1 // 是否为游戏管理员，1表示是
		},
		onLaunch: function() { // 应用启动时的生命周期函数
			console.log('App Launch'); // 在控制台打印"App Launch"

			// 以下是被注释掉的更新管理器代码
			// const updateManager = uni.getUpdateManager();

			// console.log(updateManager);
			// updateManager.onCheckForUpdate(function(res) {
			// 	console.log(res.hasUpdate);
			// });

			// updateManager.onUpdateReady(function(res) {
			// 	uni.showModal({
			// 		title: '更新提示',
			// 		content: '新版本已经准备好，是否重启应用？',
			// 		success(res) {
			// 			if (res.confirm) {
			// 				// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
			// 				updateManager.applyUpdate();
			// 			}
			// 		}
			// 	});
			// });

			// updateManager.onUpdateFailed(function(res) {
			// 	// 新的版本下载失败
			// 	uni.showModal({
			// 		title: '已经有新版本了哟~',
			// 		content: '新版本已经上线啦~，请您删除当前小程序，重新搜索打开哟~'
			// 	});
			// });


		},
		onShow: function() { // 应用显示时的生命周期函数
			// uni.hideTabBar({}); // 隐藏底部导航栏的代码被注释掉了
			
		},
		onHide: function() { // 应用隐藏时的生命周期函数
			console.log('App Hide'); // 在控制台打印"App Hide"
		},
		methods: { // 定义方法对象，目前为空



		}
		/*
	
		
		*/
	   }
</script>

<style>
	/*每个页面公共css */
	@import "colorui/main.css"; 
	@import "colorui/icon.css"; 
	@import "app.css"; 
	/* 你的项目css */
</style>