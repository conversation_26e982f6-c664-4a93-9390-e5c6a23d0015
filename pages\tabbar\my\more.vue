<!-- 
	我的更多记录页面 
-->
<template>
	<view>
		<!-- 背景图片和水印 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />
		<!-- 顶部个人信息 -->
		<view class="bg-white">
			<view class="flex padding">
				<!-- 用户头像 -->
				<view class="padding-lr-xs">
					<view class="cu-avatar lg round" :style="{ backgroundImage: 'url(' + avatar + ')' }">
					</view>
				</view>
				<!-- 用户名和部门信息 -->
				<view class="padding-xs text-xl text-black">
					<view>你好，{{username}}</view>
					<view class="cu-tag round line-green sm">{{dept}}</view>
				</view>
				<!-- 二维码图片 -->
				<view style="margin-left:180rpx;float:left;">
					<image :src='ewmimg' style="width:120rpx;height:120rpx;" @click='showCode()'></image>
				</view>
			</view>
		</view>
		<!-- 常用功能 -->
		<view class="cu-bar margin-lr-xs margin-top-sm grid col-4 no-border bg-white radius-lg-top">
			<view class="action">
				<text class="text-xl text-black">预约记录</text>
			</view>
			<view></view>
			<view></view>
			<!-- 返回按钮 -->
			<button size="mini" @click="back"
				style="float:right;right:20rpx;height:60rpx;line-height:60rpx;text-align:center;">返回</button>
		</view>
		<!-- 功能图标列表 -->
		<view class="cu-list grid col-4 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom">
			<view class="cu-item" v-for="(item,index) in iconList" :key="index" :bindtap="item.bindtap"
				@click=showOrder(index)>
				<view :class="['cuIcon-'+item.icon,'text-'+item.color,'text-shadow']" style="font-size: 56rpx;">
					<view class="cu-tag badge" v-if="item.badge!=0">
						<block v-if="item.badge!=1">{{item.badge>99?"99+":item.badge}}</block>
					</view>
				</view>
				<text>{{item.name}}</text>
			</view>
			<!-- 订单列表容器 -->
			<view style="width:700rpx;margin:20rpx;background-color: #fff;border-radius: 25rpx;min-height: 65vh;">
				<!-- 无数据时显示 -->
				<view v-if="this.list.length==0" style="color:#aaa;margin-top:20rpx;">
					暂无数据
				</view>
				<!-- 有数据时显示 -->
				<view v-else>
					<!-- 生活服务订单列表 -->
					<view class='culist' v-if="index==0">
						<view v-for="life in list" :key="life._id"
							style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;display: block;padding:20rpx 10rpx 20rpx 0;display: inline-flex;">
							<view style="margin-left:10rpx;">
								<view style="width:500rpx;float:left;">
									<view class='text-grey'
										style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
										{{ life.title }}
									</view>
									<view class='text-red' style="display: inline-flex;width:100vw;">
										<text>日期: {{ formatDate(life.createTime) }}</text>
										<text style="margin-left:30rpx;">单价: {{life.price}}元</text>
										<text style="margin-left:30rpx;">数量: {{life.sl}}</text>
									</view>
								</view>
							</view>
							<view style="wdith:200rpx;float:right;">
								<button @click="showOrderEwm(life)" size="mini">评价</button>
							</view>
						</view>
					</view>
					<!-- 食堂订餐订单列表 -->
					<view style="height:300rpx;" v-else-if="index==1">
						<!-- 非机关单位的食堂订餐列表 -->
						<view v-for="life in list" v-if="dept !='JJG'&&dept!='JLD'" :key="life._id" style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx;
					background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;display:inline-flex;">
							<view style="width:500rpx;float:left;">
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									{{ life.title }}
								</view>
								<view class='text-red' style="display: inline-flex;width:100vw;">
									<text>日期: {{ formatDate(life.createTime) }}</text>
									<text style="margin-left:30rpx;">单价: {{life.price}}元</text>
									<text style="margin-left:30rpx;">数量: {{life.sl}}</text>
								</view>

							</view>
							<view>
								<button v-if="!life.star" @click="showOrderEwm(life)" size="mini">评价</button>
								<button v-else size="mini" disabled>已评</button>
							</view>
						</view>
						<!-- 机关单位的食堂订餐列表 -->
						<view v-else @tap="showcode(item._id,item.yddate)" v-for="item in list" :key="item._id" style="border: 1px solid #eee;
														margin-left:30rpx;margin-right:30rpx;padding-bottom: 20rpx;margin-bottom: 15rpx;border-radius: 15rpx;">
							<view
								style="padding-bottom:20rpx;margin-left: 40rpx;align-items: center;display: flex;border-bottom:1px solid #eee;display: inline-flex;margin-left:30rpx;margin-top: 20rpx;width:600rpx;">
								<view>{{item.yddate}}</view>
								<view style="margin-left: 15rpx;">共计:{{item.ydprice}}元</view>
								<view v-if="item.isUsed==1" style="margin-left: 15rpx;">已使用</view>
								<view v-else-if="item.yddate < currday " style="margin-left: 15rpx;">已过期</view>
								<view v-else style="margin-left: 15rpx;">
									未使用
								</view>
								<view style="width:120rpx;">
									<img src="/static/icon/ewm.png" style="width:40rpx;height:40rpx;float:right;" />
								</view>
							</view>
							<view style="display: inline-flex;margin-left:30rpx;margin-top: 10rpx;">
								<view v-for="iitem in item.ydlist" :key="iitem._id" style="display: flex;">
									<view style="flex:1;float:left;margin-left: 15rpx;">
										<view>
											<image :src="iitem.caipic" style="width:120rpx;height:120rpx;"></image>
										</view>
										<view>
											{{iitem.cainame}}
										</view>
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 团购订单列表 -->
					<view style="height:100%;" v-else-if="index==2">
						<view v-for="group in list" :key="group._id"
							style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;display: block;padding:20rpx 10rpx 20rpx 0;display: inline-flex;">
							<view style="width:480rpx;float:left;height:100%;margin-left:20rpx;">
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									{{ group.title }} {{group.isPay?"已付款":"未付款"}}
								</view>
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									订单金额：{{ group.sl * group.price }}
								</view>
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;"
									v-if="group.hf">
									回复：{{ group.hf }}
								</view>
							</view>
							<view style="wdith:200rpx;float:right;">
								<button v-if="!group.isPay" @click="pay(group)" size="mini">付款</button>
								<button v-else-if="!group.star" @click="showOrderEwm(group)" size="mini">评价</button>
								<button v-else size="mini" disabled>已评</button>
							</view>
						</view>
					</view>
					<!-- 健康资讯列表 -->
					<view style="height:300rpx;" v-else-if="index==3">
						<view v-for="jkzx in list" :key="jkzx._id" style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx;
					background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;display:inline-flex;">
							<view style="width:500rpx;float:left;">
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									{{ jkzx.title }} {{jkzx.jd}}
								</view>
								<view class='text-red'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									<text>日期: {{ formatDate(jkzx.createTime) }}</text>
								</view>

							</view>
							<view>
								<button @click="showOrderEwm(jkzx)" size="mini">评价</button>
							</view>
						</view>
					</view>
					<!-- 名医问诊列表 -->
					<view style="height:300rpx;" v-else-if="index==4">
						<view v-for="mywz in list" :key="mywz._id" style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx;
					background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;display:inline-flex;">
							<view style="width:500rpx;float:left;">
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									{{ mywz.title }} {{mywz.xl}}
								</view>
								<view class='text-red'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									<text v-if="mywz.createTime">日期: {{ formatDate(mywz.createTime) }}</text>
								</view>
							</view>
							<view>
								<button @click="showOrderEwm(mywz)" size="mini">评价</button>
							</view>
						</view>
					</view>
					<!-- 就学和就业资讯列表 -->
					<view style="height:300rpx;" v-else-if="index==5 || index==6">
						<view v-for="jxxx in list" :key="jxxx._id" style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:10rpx;
					background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;display:inline-flex;">
							<view style="width:500rpx;float:left;">
								<view class='text-grey'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									{{ jxxx.title }} {{jxxx.xl}}
								</view>
								<view class='text-red'
									style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;;">
									<text v-if="jxxx.createTime">日期: {{ formatDate(jxxx.createTime) }}</text>
								</view>
							</view>
							<view>
								<button @click="showOrderEwm(jxxx)" size="mini">评价</button>
							</view>
						</view>
					</view>
				</view>
			</view>

		</view>

		<!-- 二维码弹窗 -->
		<uni-popup ref="popup1" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;border-radius: 20rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;
					justify-content: center;align-items: center;padding: 5upx;">
					<img :src="ewmcode" style="width:450rpx;" />
				</view>
				<view style="width: 100%;display: flex;align-items: center;font-size: 16px;color: #333;margin-top:20rpx;margin-bottom:20rpx;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 15upx;">
						<view @tap="ewm_close" style="margin-left: 200rpx;padding: 8upx 15upx 8upx 15upx;">
							确 定
						</view>
					</view>
				</view>
			</view>
		</uni-popup>
		<!-- 评分弹窗 -->
		<uni-popup ref="popup2" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;padding-bottom:50rpx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;margin-bottom: 50rpx;;
					justify-content: center;align-items: center;padding: 5upx;">
					<text style="font-size:30rpx;margin-bottom:30rpx;">请打分</text>
					<l-starRate :value="star" @input="getvalue" :disabled="false"></l-starRate>
				</view>
				<button @tap="saveStar" size="mini" type="primary">确定 </button>

			</view>
		</uni-popup>
		<!-- 支付组件 -->
		<uni-pay ref="uniPay" height="70vh" return-url="/pages/tabbar/my/myorder" logo="/static/logo.png"
			@success="onSuccess" @create="onCreate"></uni-pay>
	</view>
</template>