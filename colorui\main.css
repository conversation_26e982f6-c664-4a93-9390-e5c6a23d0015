/*
  ColorUi for MP-weixin  v2.9.1
  仅供学习交流，如作它用所承受的法律责任一概与作者无关
  使用ColorUI GA开发扩展与插件时，请注明基于ColorUI GA开发
*/

/* ==================
  基础参数-初始化(必须)
 ==================== */

page {
  /* Color 可以自定义相关配色 */
  /* var属性兼容性 --> https://www.caniuse.com/#feat=css-variables */
  /* 标准色 */
  --red: #e54d42;
  --orange: #f37b1d;
  --yellow: #fbbd08;
  --olive: #8dc63f;
  --green: #3eb93b;
  --cyan: #37c0fe;
  --blue: #0081ff;
  --purple: #8044de;
  --mauve: #b745cb;
  --pink: #e03997;
  --brown: #a5673f;
  --grey: #8799a3;
  --black: #0a0a0a;
  --darkGray: #666666;
  --gray: #969696;
  --ghostWhite: #f1f1f1;
  --white: #ffffff;
  /* 浅色 */
  --redLight: #fadbd9;
  --orangeLight: #fde6d2;
  --yellowLight: #fef2ce;
  --oliveLight: #e8f4d9;
  --greenLight: #d7f0db;
  --cyanLight: #d2f1f0;
  --blueLight: #cce6ff;
  --purpleLight: #e1d7f0;
  --mauveLight: #ebd4ef;
  --pinkLight: #f9d7ea;
  --brownLight: #ede1d9;
  --greyLight: #e7ebed;
  /* 渐变色 */
  --gradualRed: linear-gradient(45deg, #f43f3b, #ec008c);
  --gradualOrange: linear-gradient(45deg, #ff9700, #ed1c24);
  --gradualGreen: linear-gradient(45deg, #39b54a, #8dc63f);
  --gradualPurple: linear-gradient(45deg, #9000ff, #5e00ff);
  --gradualPink: linear-gradient(45deg, #ec008c, #6739b6);
  --gradualBlue: linear-gradient(45deg, #00b8f9, #0166eb);
  --gradualPinkNew: linear-gradient(45deg, #fea894, #ff1047);
  --gradualCyan: linear-gradient(45deg, #06edfe, #48b2fe);
  --gradualRedLight: linear-gradient(45deg, white, #f9d3d0);
  --gradualOrangeLight: linear-gradient(45deg, white, #fcdec7);
  --gradualGreenLight: linear-gradient(45deg, white, #cfeece);
  --gradualPurpleLight: linear-gradient(45deg, white, #dfd0f7);
  --gradualPinkLight: linear-gradient(45deg, white, #edd1f2);
  --gradualBlueLight: linear-gradient(45deg, white, #bfe0ff);
  --gradualPinkNewLight: linear-gradient(45deg, white, #f7cee5);
  --gradualCyanLight: linear-gradient(45deg, white, #cdefff);
  /* 阴影透明色 */
  --ShadowSize: 6rpx 6rpx 8rpx;
  --redShadow: rgba(204, 69, 59, 0.2);
  --orangeShadow: rgba(217, 109, 26, 0.2);
  --yellowShadow: rgba(224, 170, 7, 0.2);
  --oliveShadow: rgba(124, 173, 55, 0.2);
  --greenShadow: rgba(48, 156, 63, 0.2);
  --cyanShadow: rgba(28, 187, 180, 0.2);
  --blueShadow: rgba(0, 102, 204, 0.2);
  --purpleShadow: rgba(88, 48, 156, 0.2);
  --mauveShadow: rgba(133, 33, 150, 0.2);
  --pinkShadow: rgba(199, 50, 134, 0.2);
  --brownShadow: rgba(140, 88, 53, 0.2);
  --greyShadow: rgba(114, 130, 138, 0.2);
  --grayShadow: rgba(114, 130, 138, 0.2);
  --blackShadow: rgba(26, 26, 26, 0.2);

  background-color: var(--ghostWhite);
  font-size: 28rpx;
  color: var(--black);
  font-family: Helvetica Neue, Helvetica, sans-serif;

  /* 十级基础背景色 */
  /* General */
  --oc-white: #ffffff;
  --oc-black: #000000;
  /* Gray */
  --oc-gray-0: #f8f9fa;
  --oc-gray-1: #f1f3f5;
  --oc-gray-2: #e9ecef;
  --oc-gray-3: #dee2e6;
  --oc-gray-4: #ced4da;
  --oc-gray-5: #adb5bd;
  --oc-gray-6: #868e96;
  --oc-gray-7: #495057;
  --oc-gray-8: #343a40;
  --oc-gray-9: #212529;
  /* Red */
  --oc-red-0: #fff5f5;
  --oc-red-1: #ffe3e3;
  --oc-red-2: #ffc9c9;
  --oc-red-3: #ffa8a8;
  --oc-red-4: #ff8787;
  --oc-red-5: #ff6b6b;
  --oc-red-6: #fa5252;
  --oc-red-7: #f03e3e;
  --oc-red-8: #e03131;
  --oc-red-9: #c92a2a;
  /* Pink */
  --oc-pink-0: #fff0f6;
  --oc-pink-1: #ffdeeb;
  --oc-pink-2: #fcc2d7;
  --oc-pink-3: #faa2c1;
  --oc-pink-4: #f783ac;
  --oc-pink-5: #f06595;
  --oc-pink-6: #e64980;
  --oc-pink-7: #d6336c;
  --oc-pink-8: #c2255c;
  --oc-pink-9: #a61e4d;
  /* Grape */
  --oc-grape-0: #f8f0fc;
  --oc-grape-1: #f3d9fa;
  --oc-grape-2: #eebefa;
  --oc-grape-3: #e599f7;
  --oc-grape-4: #da77f2;
  --oc-grape-5: #cc5de8;
  --oc-grape-6: #be4bdb;
  --oc-grape-7: #ae3ec9;
  --oc-grape-8: #9c36b5;
  --oc-grape-9: #862e9c;
  /* Violet */
  --oc-violet-0: #f3f0ff;
  --oc-violet-1: #e5dbff;
  --oc-violet-2: #d0bfff;
  --oc-violet-3: #b197fc;
  --oc-violet-4: #9775fa;
  --oc-violet-5: #845ef7;
  --oc-violet-6: #7950f2;
  --oc-violet-7: #7048e8;
  --oc-violet-8: #6741d9;
  --oc-violet-9: #5f3dc4;
  /* Indigo */
  --oc-indigo-0: #edf2ff;
  --oc-indigo-1: #dbe4ff;
  --oc-indigo-2: #bac8ff;
  --oc-indigo-3: #91a7ff;
  --oc-indigo-4: #748ffc;
  --oc-indigo-5: #5c7cfa;
  --oc-indigo-6: #4c6ef5;
  --oc-indigo-7: #4263eb;
  --oc-indigo-8: #3b5bdb;
  --oc-indigo-9: #364fc7;
  /* Blue */
  --oc-blue-0: #e7f5ff;
  --oc-blue-1: #d0ebff;
  --oc-blue-2: #a5d8ff;
  --oc-blue-3: #74c0fc;
  --oc-blue-4: #4dabf7;
  --oc-blue-5: #339af0;
  --oc-blue-6: #228be6;
  --oc-blue-7: #1c7ed6;
  --oc-blue-8: #1971c2;
  --oc-blue-9: #1864ab;
  /* Cyan */
  --oc-cyan-0: #e3fafc;
  --oc-cyan-1: #c5f6fa;
  --oc-cyan-2: #99e9f2;
  --oc-cyan-3: #66d9e8;
  --oc-cyan-4: #3bc9db;
  --oc-cyan-5: #22b8cf;
  --oc-cyan-6: #15aabf;
  --oc-cyan-7: #1098ad;
  --oc-cyan-8: #0c8599;
  --oc-cyan-9: #0b7285;
  /* Teal */
  --oc-teal-0: #e6fcf5;
  --oc-teal-1: #c3fae8;
  --oc-teal-2: #96f2d7;
  --oc-teal-3: #63e6be;
  --oc-teal-4: #38d9a9;
  --oc-teal-5: #20c997;
  --oc-teal-6: #12b886;
  --oc-teal-7: #0ca678;
  --oc-teal-8: #099268;
  --oc-teal-9: #087f5b;
  /* Green */
  --oc-green-0: #ebfbee;
  --oc-green-1: #d3f9d8;
  --oc-green-2: #b2f2bb;
  --oc-green-3: #8ce99a;
  --oc-green-4: #69db7c;
  --oc-green-5: #51cf66;
  --oc-green-6: #40c057;
  --oc-green-7: #37b24d;
  --oc-green-8: #2f9e44;
  --oc-green-9: #2b8a3e;
  /* Lime */
  --oc-lime-0: #f4fce3;
  --oc-lime-1: #e9fac8;
  --oc-lime-2: #d8f5a2;
  --oc-lime-3: #c0eb75;
  --oc-lime-4: #a9e34b;
  --oc-lime-5: #94d82d;
  --oc-lime-6: #82c91e;
  --oc-lime-7: #74b816;
  --oc-lime-8: #66a80f;
  --oc-lime-9: #5c940d;
  /* Yellow */
  --oc-yellow-0: #fff9db;
  --oc-yellow-1: #fff3bf;
  --oc-yellow-2: #ffec99;
  --oc-yellow-3: #ffe066;
  --oc-yellow-4: #ffd43b;
  --oc-yellow-5: #fcc419;
  --oc-yellow-6: #fab005;
  --oc-yellow-7: #f59f00;
  --oc-yellow-8: #f08c00;
  --oc-yellow-9: #e67700;
  /* Orange */
  --oc-orange-0: #fff4e6;
  --oc-orange-1: #ffe8cc;
  --oc-orange-2: #ffd8a8;
  --oc-orange-3: #ffc078;
  --oc-orange-4: #ffa94d;
  --oc-orange-5: #ff922b;
  --oc-orange-6: #fd7e14;
  --oc-orange-7: #f76707;
  --oc-orange-8: #e8590c;
  --oc-orange-9: #d9480f;
}

/* ==================
    基础样式-按钮
 ==================== */

.cu-btn {
  position: relative;
  border: 0rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 30rpx;
  font-size: 28rpx;
  height: 64rpx;
  line-height: 1;
  text-align: center;
  text-decoration: none;
  overflow: visible;
  margin-left: initial;
  transform: translate(0rpx, 0rpx);
  margin-right: initial;
}

.cu-btn::after {
  display: none;
}

.cu-btn:not([class*="bg-"]) {
  background-color: #f0f0f0;
}

.cu-btn[class*="line"] {
  background-color: transparent;
}

.cu-btn[class*="line"]::after {
  content: " ";
  display: block;
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 12rpx;
  z-index: 1;
  pointer-events: none;
}

.cu-btn.round[class*="line"]::after {
  border-radius: 1000rpx;
}

.cu-btn[class*="lines"]::after {
  border: 6rpx solid currentColor;
}

.cu-btn[class*="bg-"]::after {
  display: none;
}

.cu-btn.sm {
  padding: 0 20rpx;
  font-size: 20rpx;
  height: 48rpx;
}

.cu-btn.lg {
  padding: 0 40rpx;
  font-size: 30rpx;
  height: 72rpx;
}

.cu-btn.xxl {
  padding: 0 48rpx;
  font-size: 36rpx;
  height: 96rpx;
}

.cu-btn.icon.sm {
  width: 48rpx;
  height: 48rpx;
}

.cu-btn.icon {
  width: 64rpx;
  height: 64rpx;
  border-radius: 500rpx;
  padding: 0;
}

button.icon.lg {
  width: 80rpx;
  height: 80rpx;
}

.cu-btn.shadow-blur::before {
  top: 4rpx;
  left: 4rpx;
  filter: blur(6rpx);
  opacity: 0.6;
}

.cu-btn.button-hover {
  transform: translate(1rpx, 1rpx);
}

.block {
  display: block;
}

.cu-btn.block {
  display: flex;
}

.cu-btn[disabled] {
  opacity: 0.6;
  color: var(--white);
}

/* ==================
    基础样式-标签
 ==================== */

.cu-tag {
  font-size: 24rpx;
  vertical-align: middle;
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0rpx 16rpx;
  height: 48rpx;
  font-family: Helvetica Neue, Helvetica, sans-serif;
  white-space: nowrap;
}

.cu-tag:not([class*="bg"]):not([class*="line"]) {
  background-color: var(--ghostWhite);
}

.cu-tag[class*="line-"]::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1rpx solid currentColor;
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
}

.cu-tag.radius[class*="line"]::after {
  border-radius: 12rpx;
}

.cu-tag.round[class*="line"]::after {
  border-radius: 1000rpx;
}

.cu-tag[class*="line-"]::after {
  border-radius: 0;
}

.cu-tag+.cu-tag {
  margin-left: 10rpx;
}

.cu-tag.sm {
  font-size: 20rpx;
  padding: 0rpx 12rpx;
  height: 32rpx;
}

.cu-capsule {
  display: inline-flex;
  vertical-align: middle;
}

.cu-capsule+.cu-capsule {
  margin-left: 10rpx;
}

.cu-capsule .cu-tag {
  margin: 0;
}

.cu-capsule .cu-tag[class*="line-"]:last-child::after {
  border-left: 0rpx solid transparent;
}

.cu-capsule .cu-tag[class*="line-"]:first-child::after {
  border-right: 0rpx solid transparent;
}

.cu-capsule.radius .cu-tag:first-child {
  border-top-left-radius: 6rpx;
  border-bottom-left-radius: 6rpx;
}

.cu-capsule.radius .cu-tag:last-child::after,
.cu-capsule.radius .cu-tag[class*="line-"] {
  border-top-right-radius: 12rpx;
  border-bottom-right-radius: 12rpx;
}

.cu-capsule.round .cu-tag:first-child {
  border-top-left-radius: 200rpx;
  border-bottom-left-radius: 200rpx;
  text-indent: 4rpx;
}

.cu-capsule.round .cu-tag:last-child::after,
.cu-capsule.round .cu-tag:last-child {
  border-top-right-radius: 200rpx;
  border-bottom-right-radius: 200rpx;
  text-indent: -4rpx;
}

.cu-tag.badge {
  border-radius: 200rpx;
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  font-size: 20rpx;
  padding: 0rpx 10rpx;
  height: 28rpx;
  color: var(--white);
}

.cu-tag.badge:not([class*="bg-"]) {
  background-color: #dd514c;
}

.cu-tag:empty:not([class*="cuIcon-"]) {
  padding: 0rpx;
  width: 16rpx;
  height: 16rpx;
  top: -4rpx;
  right: -4rpx;
}

.cu-tag[class*="cuIcon-"] {
  width: 32rpx;
  height: 32rpx;
  top: -4rpx;
  right: -4rpx;
}

/* ==================
    基础样式-头像
 ==================== */

.cu-avatar {
  font-variant: small-caps;
  display: inline-flex;
  text-align: center;
  justify-content: center;
  align-items: center;
  white-space: nowrap;
  position: relative;
  width: 64rpx;
  height: 64rpx;
  background-size: cover;
  background-position: center;
  vertical-align: middle;
  font-size: 1.5em;
}

.cu-avatar.sm {
  width: 48rpx;
  height: 48rpx;
  font-size: 1em;
}

.cu-avatar.lg {
  width: 96rpx;
  height: 96rpx;
  font-size: 2em;
}

.cu-avatar.xl {
  width: 136rpx;
  height: 136rpx;
  font-size: 2.5em;
}

.cu-avatar .avatar-text {
  font-size: 0.4em;
}

.cu-avatar-group {
  direction: rtl;
  unicode-bidi: bidi-override;
  padding: 0 10rpx 0 40rpx;
  display: inline-block;
}

.cu-avatar-group .cu-avatar {
  margin-left: -30rpx;
  border: 4rpx solid var(--ghostWhite);
  vertical-align: middle;
}

.cu-avatar-group .cu-avatar.sm {
  margin-left: -20rpx;
  border: 1rpx solid var(--ghostWhite);
}

/* ==================
    基础样式-进度条
 ==================== */

.cu-progress {
  overflow: hidden;
  height: 28rpx;
  background-color: #ebeef5;
  display: inline-flex;
  align-items: center;
  width: 100%;
}

.cu-progress+view,
.cu-progress+text {
  line-height: 1;
}

.cu-progress.xs {
  height: 10rpx;
}

.cu-progress.sm {
  height: 20rpx;
}

.cu-progress view {
  width: 0;
  height: 100%;
  align-items: center;
  display: flex;
  justify-items: flex-end;
  justify-content: space-around;
  font-size: 20rpx;
  color: var(--white);
  transition: width 0.6s ease;
}

.cu-progress text {
  align-items: center;
  display: flex;
  font-size: 20rpx;
  color: var(--black);
  text-indent: 10rpx;
}

.cu-progress.text-progress {
  padding-right: 60rpx;
}

.cu-progress.striped view {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 72rpx 72rpx;
}

.cu-progress.active view {
  animation: progress-stripes 2s linear infinite;
}

@keyframes progress-stripes {
  from {
    background-position: 72rpx 0;
  }

  to {
    background-position: 0 0;
  }
}

/* 环形进度条样式 */
.cu-progress__circle {
  position: relative;
  background-image: linear-gradient(#7affaf, #7a88ff);
  border-radius: 50%;
}

.cu-progress__circle--left {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 0;
  border-radius: 50%;
  transition: transform;
  transition-duration: 0.25s;
}

.cu-progress__circle--right {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 0;
  border-radius: 50%;
  transition: transform;
  transition-duration: 0.25s;
}

.cu-progress__mask {
  position: absolute;
  top: 10px;
  right: 0;
  bottom: 0;
  left: 10px;
  z-index: 0;
  border-radius: 50%;
  background-color: #ffffff;
}

.cu-progress__circle__percent {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 28rpx;
}

/* ==================
    基础样式-阴影
 ==================== */

.shadow[class*="-red"] {
  box-shadow: var(--ShadowSize) var(--redShadow);
}

.shadow[class*="-orange"] {
  box-shadow: var(--ShadowSize) var(--orangeShadow);
}

.shadow[class*="-yellow"] {
  box-shadow: var(--ShadowSize) var(--yellowShadow);
}

.shadow[class*="-olive"] {
  box-shadow: var(--ShadowSize) var(--oliveShadow);
}

.shadow[class*="-green"] {
  box-shadow: var(--ShadowSize) var(--greenShadow);
}

.shadow[class*="-cyan"] {
  box-shadow: var(--ShadowSize) var(--cyanShadow);
}

.shadow[class*="-blue"] {
  box-shadow: var(--ShadowSize) var(--blueShadow);
}

.shadow[class*="-purple"] {
  box-shadow: var(--ShadowSize) var(--purpleShadow);
}

.shadow[class*="-mauve"] {
  box-shadow: var(--ShadowSize) var(--mauveShadow);
}

.shadow[class*="-pink"] {
  box-shadow: var(--ShadowSize) var(--pinkShadow);
}

.shadow[class*="-brown"] {
  box-shadow: var(--ShadowSize) var(--brownShadow);
}

.shadow[class*="-grey"] {
  box-shadow: var(--ShadowSize) var(--greyShadow);
}

.shadow[class*="-gray"] {
  box-shadow: var(--ShadowSize) var(--grayShadow);
}

.shadow[class*="-black"] {
  box-shadow: var(--ShadowSize) var(--blackShadow);
}

.shadow[class*="-white"] {
  box-shadow: var(--ShadowSize) var(--blackShadow);
}

.shadow[class*='white'] {
  --ShadowSize: 0 1rpx 6rpx;
}

.text-shadow[class*="-red"] {
  text-shadow: var(--ShadowSize) var(--redShadow);
}

.text-shadow[class*="-orange"] {
  text-shadow: var(--ShadowSize) var(--orangeShadow);
}

.text-shadow[class*="-yellow"] {
  text-shadow: var(--ShadowSize) var(--yellowShadow);
}

.text-shadow[class*="-olive"] {
  text-shadow: var(--ShadowSize) var(--oliveShadow);
}

.text-shadow[class*="-green"] {
  text-shadow: var(--ShadowSize) var(--greenShadow);
}

.text-shadow[class*="-cyan"] {
  text-shadow: var(--ShadowSize) var(--cyanShadow);
}

.text-shadow[class*="-blue"] {
  text-shadow: var(--ShadowSize) var(--blueShadow);
}

.text-shadow[class*="-purple"] {
  text-shadow: var(--ShadowSize) var(--purpleShadow);
}

.text-shadow[class*="-mauve"] {
  text-shadow: var(--ShadowSize) var(--mauveShadow);
}

.text-shadow[class*="-pink"] {
  text-shadow: var(--ShadowSize) var(--pinkShadow);
}

.text-shadow[class*="-brown"] {
  text-shadow: var(--ShadowSize) var(--brownShadow);
}

.text-shadow[class*="-grey"] {
  text-shadow: var(--ShadowSize) var(--greyShadow);
}

.text-shadow[class*="-gray"] {
  text-shadow: var(--ShadowSize) var(--grayShadow);
}

.text-shadow[class*="-black"] {
  text-shadow: var(--ShadowSize) var(--blackShadow);
}

.shadow-lg {
  --ShadowSize: 0rpx 40rpx 100rpx 0rpx;
}

.shadow-warp {
  position: relative;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.shadow-warp:before,
.shadow-warp:after {
  position: absolute;
  content: "";
  top: 20rpx;
  bottom: 30rpx;
  left: 20rpx;
  width: 50%;
  box-shadow: 0 30rpx 20rpx rgba(0, 0, 0, 0.2);
  transform: rotate(-3deg);
  z-index: -1;
}

.shadow-warp:after {
  right: 20rpx;
  left: auto;
  transform: rotate(3deg);
}

.shadow-blur {
  position: relative;
}

.shadow-blur::before {
  content: "";
  display: block;
  background: inherit;
  filter: blur(10rpx);
  position: absolute;
  width: 100%;
  height: 100%;
  top: 10rpx;
  left: 10rpx;
  z-index: -1;
  opacity: 0.4;
  transform-origin: 0 0;
  border-radius: inherit;
  transform: scale(1, 1);
}

/* ==================
    基础样式-加载
 ==================== */

.cu-load {
  display: block;
  line-height: 3em;
  text-align: center;
}

.cu-load::before {
  font-family: "cuIcon";
  display: inline-block;
  margin-right: 6rpx;
}

.cu-load.loading::before {
  content: "\e67a";
  animation: cuIcon-spin 2s infinite linear;
}

.cu-load.loading::after {
  content: "加载中...";
}

.cu-load.over::before {
  content: "\e64a";
}

.cu-load.over::after {
  content: "没有更多了";
}

.cu-load.erro::before {
  content: "\e658";
}

.cu-load.erro::after {
  content: "加载失败";
}

.cu-load.load-icon::before {
  font-size: 32rpx;
}

.cu-load.load-icon::after {
  display: none;
}

.cu-load.load-icon.over {
  display: none;
}

.cu-load.load-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 140rpx;
  left: 0;
  margin: auto;
  width: 260rpx;
  height: 260rpx;
  background-color: var(--white);
  border-radius: 10rpx;
  box-shadow: 0 0 0rpx 2000rpx rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  font-size: 28rpx;
  z-index: 9999;
  line-height: 2.4em;
}

.cu-load.load-modal [class*="cuIcon-"] {
  font-size: 60rpx;
}

.cu-load.load-modal image {
  width: 70rpx;
  height: 70rpx;
}

.cu-load.load-modal::after {
  content: "";
  position: absolute;
  background-color: var(--white);
  border-radius: 50%;
  width: 200rpx;
  height: 200rpx;
  font-size: 10px;
  border-top: 6rpx solid rgba(0, 0, 0, 0.05);
  border-right: 6rpx solid rgba(0, 0, 0, 0.05);
  border-bottom: 6rpx solid rgba(0, 0, 0, 0.05);
  border-left: 6rpx solid var(--orange);
  animation: cuIcon-spin 1s infinite linear;
  z-index: -1;
}

.load-progress {
  pointer-events: none;
  top: 0;
  position: fixed;
  width: 100%;
  left: 0;
  z-index: 2000;
}

.load-progress.hide {
  display: none;
}

.load-progress .load-progress-bar {
  position: relative;
  width: 100%;
  height: 4rpx;
  overflow: hidden;
  transition: all 200ms ease 0s;
}

.load-progress .load-progress-spinner {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  z-index: 2000;
  display: block;
}

.load-progress .load-progress-spinner::after {
  content: "";
  display: block;
  width: 24rpx;
  height: 24rpx;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border: solid 4rpx transparent;
  border-top-color: inherit;
  border-left-color: inherit;
  border-radius: 50%;
  -webkit-animation: load-progress-spinner 0.4s linear infinite;
  animation: load-progress-spinner 0.4s linear infinite;
}

@-webkit-keyframes load-progress-spinner {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes load-progress-spinner {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

/* Loading扩展样式 */
.cu-loading-box {
  height: 100px;
}

/* Loading扩展一 */
.cu-loading1 {
  position: relative;
}

.cu-loading1>view {
  position: absolute;
  width: 4px;
  height: 20px;
  border-radius: 10px;
}

.cu-loading1>view:nth-child(1) {
  top: 20px;
  left: 0px;
  animation: cu-loading1 infinite 1s;
}

.cu-loading1>view:nth-child(2) {
  top: 14.1442px;
  left: 14.1442px;
  transform: rotate(-45deg);
  animation: cu-loading1 infinite 1s 0.125s;
}

.cu-loading1>view:nth-child(3) {
  top: 0px;
  left: 20px;
  transform: rotate(90deg);
  animation: cu-loading1 infinite 1s 0.25s;
}

.cu-loading1>view:nth-child(4) {
  top: -14.1442px;
  left: 14.1442px;
  transform: rotate(45deg);
  animation: cu-loading1 infinite 1s 0.375s;
}

.cu-loading1>view:nth-child(5) {
  top: -20px;
  left: 0px;
  transform: rotate(0deg);
  animation: cu-loading1 infinite 1s 0.5s;
}

.cu-loading1>view:nth-child(6) {
  top: -14.1442px;
  left: -14.1442px;
  transform: rotate(-45deg);
  animation: cu-loading1 infinite 1s 0.625s;
}

.cu-loading1>view:nth-child(7) {
  top: 0px;
  left: -20px;
  transform: rotate(90deg);
  animation: cu-loading1 infinite 1s 0.75s;
}

.cu-loading1>view:nth-child(8) {
  top: 14.1442px;
  left: -14.1442px;
  transform: rotate(45deg);
  animation: cu-loading1 infinite 1s 0.875s;
}

@keyframes cu-loading1 {
  50% {
    opacity: 0.1;
  }

  100% {
    opacity: 1;
  }
}

/* Loading扩展二 */
.cu-loading2,
.circle-add {
  width: 100px;
  height: 100px;
  display: inline-block;
  position: relative;
}

.cu-loading2 text {
  display: block;
  width: 40px;
  height: 50px;
  border-radius: 50%;
  position: absolute;
  top: calc(50% - 25px);
  left: calc(50% - 40px);
  transform-origin: center right;
  animation: circle1 1.5s linear infinite;
}

.cu-loading2 text::before {
  content: '';
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  right: 10px;
  background-color: #0081ff;
}

.cu-loading2 text:nth-child(1) {
  transform: rotate(0deg);
  animation-delay: 0.2s;
}

.cu-loading2 text:nth-child(2) {
  transform: rotate(45deg);
  animation-delay: 0.4s;
}

.cu-loading2 text:nth-child(3) {
  transform: rotate(90deg);
  animation-delay: 0.6s;
}

.cu-loading2 text:nth-child(4) {
  transform: rotate(135deg);
  animation-delay: 0.8s;
}

.cu-loading2 text:nth-child(5) {
  transform: rotate(180deg);
  animation-delay: 1s;
}

.cu-loading2 text:nth-child(6) {
  transform: rotate(225deg);
  animation-delay: 1.2s;
}

.cu-loading2 text:nth-child(7) {
  transform: rotate(270deg);
  animation-delay: 1.4s;
}

.cu-loading2 text:nth-child(8) {
  transform: rotate(315deg);
  animation-delay: 1.6s;
}

@keyframes circle1 {
  0% {
    opacity: 0.15;
  }

  100% {
    opacity: 0.9;
  }
}

/* Loading扩展三 */
.cu-loading3 {
  position: relative;
  animation: circle-line 1.5s linear infinite;
}

.cu-loading3 text {
  display: block;
  width: 50%;
  height: 5px;
  opacity: .7;
  position: absolute;
  top: calc(50% - 2.5px);
  left: 0px;
  transform-origin: center right;
}

.cu-loading3 text::before {
  content: '';
  display: block;
  width: 15px;
  height: 5px;
  position: absolute;
  top: 0;
  right: 10px;
  background-color: #0081ff;
}

.cu-loading3 text:nth-child(1) {
  transform: rotate(0deg);
  animation-delay: 0.2s;
}

.cu-loading3 text:nth-child(2) {
  transform: rotate(45deg);
  animation-delay: 0.4s;
}

.cu-loading3 text:nth-child(3) {
  transform: rotate(90deg);
  animation-delay: 0.6s;
}

.cu-loading3 text:nth-child(4) {
  transform: rotate(135deg);
  animation-delay: 0.8s;
}

.cu-loading3 text:nth-child(5) {
  transform: rotate(180deg);
  animation-delay: 1s;
}

.cu-loading3 text:nth-child(6) {
  transform: rotate(225deg);
  animation-delay: 1.2s;
}

.cu-loading3 text:nth-child(7) {
  transform: rotate(270deg);
  animation-delay: 1.4s;
}

.cu-loading3 text:nth-child(8) {
  transform: rotate(315deg);
  animation-delay: 1.6s;
}

@keyframes circle-line {
  0% {
    transform: rotate(-360deg);
  }

  100% {
    transform: rotate(10deg);
  }
}

/* Loading扩展四 */
.cu-loading4>view {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  float: left;
  animation: stretchdelay 1.2s infinite ease-in-out;
}

.cu-loading4>.rect2 {
  animation-delay: -1.1s;
}

.cu-loading4>.rect3 {
  animation-delay: -1.0s;
}

.cu-loading4>.rect4 {
  animation-delay: -0.9s;
}

.cu-loading4>.rect5 {
  animation-delay: -0.8s;
}

@keyframes stretchdelay {

  0%,
  40%,
  100% {
    transform: scale(0.4);
    -webkit-transform: scale(0.4);
  }

  20% {
    transform: scale(1.0);
    -webkit-transform: scale(1.0);
  }
}

/* ==================
    基础样式-边框
 ==================== */

/* -- 实线 -- */
.solid,
.solid-top,
.solid-right,
.solid-bottom,
.solid-left,
.solids,
.solids-top,
.solids-right,
.solids-bottom,
.solids-left,
.solid-blue,
.solid-top-blue,
.solid-right-blue,
.solid-bottom-blue,
.solid-left-blue,
.solids-blue,
.solids-top-blue,
.solids-right-blue,
.solids-bottom-blue,
.solids-left-blue,
.dashed,
.dashed-top,
.dashed-right,
.dashed-bottom,
.dashed-left {
  position: relative;
}

.solid::after,
.solid-top::after,
.solid-right::after,
.solid-bottom::after,
.solid-left::after,
.solids::after,
.solids-top::after,
.solids-right::after,
.solids-bottom::after,
.solids-left::after,
.solid-blue::after,
.solid-top-blue::after,
.solid-right-blue::after,
.solid-bottom-blue::after,
.solid-left-blue::after,
.solids-blue::after,
.solids-top-blue::after,
.solids-right-blue::after,
.solids-bottom-blue::after,
.solids-left-blue::after,
.dashed::after,
.dashed-top::after,
.dashed-right::after,
.dashed-bottom::after,
.dashed-left::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
}

.solid::after {
  border: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-top::after {
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-right::after {
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-bottom::after {
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solid-left::after {
  border-left: 1rpx solid rgba(0, 0, 0, 0.1);
}

.solids::after {
  border: 8rpx solid #eee;
}

.solids-top::after {
  border-top: 8rpx solid #eee;
}

.solids-right::after {
  border-right: 8rpx solid #eee;
}

.solids-bottom::after {
  border-bottom: 8rpx solid #eee;
}

.solids-left::after {
  border-left: 8rpx solid #eee;
}

.solid-blue::after {
  border: 1rpx solid #0081ff;
}

.solid-top-blue::after {
  border-top: 1rpx solid #0081ff;
}

.solid-right-blue::after {
  border-right: 1rpx solid #0081ff;
}

.solid-bottom-blue::after {
  border-bottom: 1rpx solid #0081ff;
}

.solid-left-blue::after {
  border-left: 1rpx solid #0081ff;
}

.solids-blue::after {
  border: 8rpx solid #0081ff;
}

.solids-top-blue::after {
  border-top: 8rpx solid #0081ff;
}

.solids-right-blue::after {
  border-right: 8rpx solid #0081ff;
}

.solids-bottom-blue::after {
  border-bottom: 8rpx solid #0081ff;
}

.solids-left-blue::after {
  border-left: 8rpx solid #0081ff;
}

/* -- 虚线 -- */

.dashed::after {
  border: 6rpx dashed #ddd;
}

.dashed-top::after {
  border-top: 6rpx dashed #ddd;
}

.dashed-right::after {
  border-right: 6rpx dashed #ddd;
}

.dashed-bottom::after {
  border-bottom: 6rpx dashed #ddd;
}

.dashed-left::after {
  border-left: 6rpx dashed #ddd;
}

/* ==================
    基础样式-开关&复选框
 ==================== */

switch,
checkbox,
radio {
  position: relative;
}

switch::after,
switch::before {
  font-family: "cuIcon";
  content: "\e645";
  position: absolute;
  color: var(--white) !important;
  top: 0%;
  left: 0rpx;
  font-size: 26rpx;
  line-height: 26px;
  width: 50%;
  text-align: center;
  pointer-events: none;
  transform: scale(0, 0);
  transition: all 0.3s ease-in-out 0s;
  z-index: 9;
  bottom: 0;
  height: 26px;
  margin: auto;
}

switch::before {
  content: "\e646";
  right: 0;
  transform: scale(1, 1);
  left: auto;
}

switch[checked]::after,
switch.checked::after {
  transform: scale(1, 1);
}

switch[checked]::before,
switch.checked::before {
  transform: scale(0, 0);
}

switch[checked]::before {
  transform: scale(0, 0);
}

radio::before,
checkbox::before {
  font-family: "cuIcon";
  content: "\e645";
  position: absolute;
  color: var(--white) !important;
  top: 50%;
  margin-top: -9px;
  right: 5px;
  font-size: 32rpx;
  line-height: 16px;
  pointer-events: none;
  transform: scale(1, 1);
  transition: all 0.3s ease-in-out 0s;
  z-index: 9;
}

radio .wx-radio-input,
checkbox .wx-checkbox-input {
  margin: 0;
  width: 24px;
  height: 24px;
}

checkbox.round .wx-checkbox-input {
  border-radius: 100rpx;
}

switch .wx-switch-input {
  border: none;
  padding: 0 24px;
  width: 48px;
  height: 26px;
  margin: 0;
  border-radius: 100rpx;
}

switch .wx-switch-input:not([class*="bg-"]) {
  background: var(--grey) !important;
}

switch .wx-switch-input::after {
  margin: auto;
  width: 26px;
  height: 26px;
  border-radius: 100rpx;
  left: 0rpx;
  top: 0rpx;
  bottom: 0rpx;
  position: absolute;
  transform: scale(0.9, 0.9);
  transition: all 0.1s ease-in-out 0s;
}

switch .wx-switch-input.wx-switch-input-checked::after {
  margin: auto;
  left: 22px;
  box-shadow: none;
  transform: scale(0.9, 0.9);
}

radio-group {
  display: inline-block;
}

switch.radius .wx-switch-input::after,
switch.radius .wx-switch-input,
switch.radius .wx-switch-input::before {
  border-radius: 10rpx;
}

switch .wx-switch-input::before,
radio.radio::before,
checkbox .wx-checkbox-input::before,
radio .wx-radio-input::before,
radio.radio::before {
  display: none;
}

radio.radio[checked]::after {
  content: "";
  background-color: transparent;
  display: block;
  position: absolute;
  width: 8px;
  height: 8px;
  z-index: 999;
  top: 0rpx;
  left: 0rpx;
  right: 0;
  bottom: 0;
  margin: auto;
  border-radius: 200rpx;
  border: 8px solid var(--white) !important;
}

.switch-sex::after {
  content: "\e71c";
}

.switch-sex::before {
  content: "\e71a";
}

.switch-music::after {
  content: "\e66a";
}

.switch-music::before {
  content: "\e6db";
}

.switch-sex .wx-switch-input {
  background: var(--red) !important;
  border-color: var(--red) !important;
}

.switch-sex[checked] .wx-switch-input {
  background: var(--blue) !important;
  border-color: var(--blue) !important;
}

switch.red[checked] .wx-switch-input,
checkbox.red[checked] .wx-checkbox-input,
radio.red[checked] .wx-radio-input {
  border-color: var(--red) !important;
}

switch.orange[checked] .wx-switch-input,
checkbox.orange[checked] .wx-checkbox-input,
radio.orange[checked] .wx-radio-input {
  border-color: var(--orange) !important;
}

switch.yellow[checked] .wx-switch-input,
checkbox.yellow[checked] .wx-checkbox-input,
radio.yellow[checked] .wx-radio-input {
  border-color: var(--yellow) !important;
}

switch.olive[checked] .wx-switch-input,
checkbox.olive[checked] .wx-checkbox-input,
radio.olive[checked] .wx-radio-input {
  border-color: var(--olive) !important;
}

switch.green[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input {
  border-color: var(--green) !important;
}

switch.cyan[checked] .wx-switch-input,
checkbox.cyan[checked] .wx-checkbox-input,
radio.cyan[checked] .wx-radio-input {
  border-color: var(--cyan) !important;
}

switch.blue[checked] .wx-switch-input,
checkbox.blue[checked] .wx-checkbox-input,
radio.blue[checked] .wx-radio-input {
  border-color: var(--blue) !important;
}

switch.purple[checked] .wx-switch-input,
checkbox.purple[checked] .wx-checkbox-input,
radio.purple[checked] .wx-radio-input {
  border-color: var(--purple) !important;
}

switch.mauve[checked] .wx-switch-input,
checkbox.mauve[checked] .wx-checkbox-input,
radio.mauve[checked] .wx-radio-input {
  border-color: var(--mauve) !important;
}

switch.pink[checked] .wx-switch-input,
checkbox.pink[checked] .wx-checkbox-input,
radio.pink[checked] .wx-radio-input {
  border-color: var(--pink) !important;
}

switch.brown[checked] .wx-switch-input,
checkbox.brown[checked] .wx-checkbox-input,
radio.brown[checked] .wx-radio-input {
  border-color: var(--brown) !important;
}

switch.grey[checked] .wx-switch-input,
checkbox.grey[checked] .wx-checkbox-input,
radio.grey[checked] .wx-radio-input {
  border-color: var(--grey) !important;
}

switch.gray[checked] .wx-switch-input,
checkbox.gray[checked] .wx-checkbox-input,
radio.gray[checked] .wx-radio-input {
  border-color: var(--grey) !important;
}

switch.black[checked] .wx-switch-input,
checkbox.black[checked] .wx-checkbox-input,
radio.black[checked] .wx-radio-input {
  border-color: var(--black) !important;
}

switch.white[checked] .wx-switch-input,
checkbox.white[checked] .wx-checkbox-input,
radio.white[checked] .wx-radio-input {
  border-color: var(--white) !important;
}

switch.red[checked] .wx-switch-input.wx-switch-input-checked,
checkbox.red[checked] .wx-checkbox-input,
radio.red[checked] .wx-radio-input {
  background-color: var(--red) !important;
  color: var(--white) !important;
}

switch.orange[checked] .wx-switch-input,
checkbox.orange[checked] .wx-checkbox-input,
radio.orange[checked] .wx-radio-input {
  background-color: var(--orange) !important;
  color: var(--white) !important;
}

switch.yellow[checked] .wx-switch-input,
checkbox.yellow[checked] .wx-checkbox-input,
radio.yellow[checked] .wx-radio-input {
  background-color: var(--yellow) !important;
  color: var(--black) !important;
}

switch.olive[checked] .wx-switch-input,
checkbox.olive[checked] .wx-checkbox-input,
radio.olive[checked] .wx-radio-input {
  background-color: var(--olive) !important;
  color: var(--white) !important;
}

switch.green[checked] .wx-switch-input,
switch[checked] .wx-switch-input,
checkbox.green[checked] .wx-checkbox-input,
checkbox[checked] .wx-checkbox-input,
radio.green[checked] .wx-radio-input,
radio[checked] .wx-radio-input {
  background-color: var(--green) !important;
  color: var(--white) !important;
}

switch.cyan[checked] .wx-switch-input,
checkbox.cyan[checked] .wx-checkbox-input,
radio.cyan[checked] .wx-radio-input {
  background-color: var(--cyan) !important;
  color: var(--white) !important;
}

switch.blue[checked] .wx-switch-input,
checkbox.blue[checked] .wx-checkbox-input,
radio.blue[checked] .wx-radio-input {
  background-color: var(--blue) !important;
  color: var(--white) !important;
}

switch.purple[checked] .wx-switch-input,
checkbox.purple[checked] .wx-checkbox-input,
radio.purple[checked] .wx-radio-input {
  background-color: var(--purple) !important;
  color: var(--white) !important;
}

switch.mauve[checked] .wx-switch-input,
checkbox.mauve[checked] .wx-checkbox-input,
radio.mauve[checked] .wx-radio-input {
  background-color: var(--mauve) !important;
  color: var(--white) !important;
}

switch.pink[checked] .wx-switch-input,
checkbox.pink[checked] .wx-checkbox-input,
radio.pink[checked] .wx-radio-input {
  background-color: var(--pink) !important;
  color: var(--white) !important;
}

switch.brown[checked] .wx-switch-input,
checkbox.brown[checked] .wx-checkbox-input,
radio.brown[checked] .wx-radio-input {
  background-color: var(--brown) !important;
  color: var(--white) !important;
}

switch.grey[checked] .wx-switch-input,
checkbox.grey[checked] .wx-checkbox-input,
radio.grey[checked] .wx-radio-input {
  background-color: var(--grey) !important;
  color: var(--white) !important;
}

switch.gray[checked] .wx-switch-input,
checkbox.gray[checked] .wx-checkbox-input,
radio.gray[checked] .wx-radio-input {
  background-color: #f0f0f0 !important;
  color: var(--black) !important;
}

switch.black[checked] .wx-switch-input,
checkbox.black[checked] .wx-checkbox-input,
radio.black[checked] .wx-radio-input {
  background-color: var(--black) !important;
  color: var(--white) !important;
}

switch.white[checked] .wx-switch-input,
checkbox.white[checked] .wx-checkbox-input,
radio.white[checked] .wx-radio-input {
  background-color: var(--white) !important;
  color: var(--black) !important;
}

checkbox.red .wx-checkbox-input {
  border-color: var(--red) !important;
}

checkbox.orange .wx-checkbox-input {
  border-color: var(--orange) !important;
}

checkbox.blue .wx-checkbox-input {
  border-color: var(--blue) !important;
}

checkbox.green .wx-checkbox-input {
  border-color: var(--green) !important;
}

checkbox.yellow .wx-checkbox-input {
  border-color: var(--yellow) !important;
}

checkbox.olive .wx-checkbox-input {
  border-color: var(--olive) !important;
}

checkbox.cyan .wx-checkbox-input {
  border-color: var(--cyan) !important;
}

checkbox.purple .wx-checkbox-input {
  border-color: var(--purple) !important;
}

checkbox.mauve .wx-checkbox-input {
  border-color: var(--mauve) !important;
}

checkbox.pink .wx-checkbox-input {
  border-color: var(--pink) !important;
}

/* 开关扩展样式-蛋黄派开关 */
egg-input {
  display: none;
}

egg-input+egg-label {
  display: flex;
  align-items: center;
  width: 180rpx;
  height: 90rpx;
  border-radius: 50rpx;
  background: #0081ff;
  padding: 0 5rpx;
}

egg-input:checked+egg-label {
  animation: shake 1s forwards 1;
}

egg-label>span {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: var(--white);
}

egg-label>span,
egg-label>span::before {
  transition: all 400ms ease;
  transition-delay: 100ms;
}

egg-label>span::before {
  display: block;
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background: radial-gradient(circle at top left, rgb(255, 188, 0) 10%, rgb(255, 130, 57));
  content: '';
  animation: eggy 15s linear infinite;
}

egg-label>span::after {
  width: 15rpx;
  height: 16rpx;
  border-top: 3rpx solid rgb(255, 255, 255);
  content: '';
  display: block;
  border-radius: 50%;
  position: absolute;
  transform: translate(-35%, -25%) rotate(-45deg);
}

.egg-flip {
  background: rgb(183, 183, 183);
  animation: egg-flip 1s forwards 1;
}

.egg-flip::before {
  opacity: 0.3;
}

.egg-flip::after {
  opacity: 0;
}

.unegg-flip {
  animation: unegg-flip 1s forwards 1;
}

@keyframes egg-flip {
  from {
    transform: rotateY(0) translateX(0);
  }

  to {
    transform: rotateY(180deg) translateX(-100rpx);
  }
}

@keyframes unegg-flip {
  from {
    transform: translateX(90rpx);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes shake {

  0%,
  20% {
    transform: translateX(-8px);
  }

  10%,
  30%,
  100% {
    transform: translateX(0);
  }
}

@keyframes eggy {
  0% {
    border-radius: 51% 49% 34% 66% / 39% 66% 34% 61%;
  }

  10% {
    border-radius: 39% 61% 38% 62% / 53% 54% 46% 47%;
  }

  20% {
    border-radius: 56% 44% 52% 48% / 57% 35% 65% 43%;
  }

  30% {
    border-radius: 41% 59% 45% 55% / 33% 56% 44% 67%;
  }

  40% {
    border-radius: 46% 54% 60% 40% / 55% 74% 26% 45%;
  }

  50% {
    border-radius: 44% 56% 22% 78% / 31% 74% 26% 69%;
  }

  60% {
    border-radius: 31% 69% 32% 68% / 31% 71% 29% 69%;
  }

  70% {
    border-radius: 44% 56% 60% 40% / 63% 36% 64% 37%;
  }

  80% {
    border-radius: 46% 54% 38% 62% / 44% 48% 52% 56%;
  }

  90% {
    border-radius: 66% 34% 57% 43% / 49% 51% 49% 51%;
  }

  100% {
    border-radius: 51% 49% 34% 66% / 39% 66% 34% 61%;
  }
}

/* ==================
    组件样式-操作条
 ==================== */

.cu-bar {
  display: flex;
  position: relative;
  align-items: center;
  min-height: 100rpx;
  justify-content: space-between;
}

.cu-bar .action {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: center;
  max-width: 100%;
}

.cu-bar .action.border-title {
  position: relative;
  top: -10rpx;
}

.cu-bar .action.border-title text[class*="bg-"]:last-child {
  position: absolute;
  bottom: -0.5rem;
  min-width: 2rem;
  height: 6rpx;
  left: 0;
}

.cu-bar .action.sub-title {
  position: relative;
  top: -0.2rem;
}

.cu-bar .action.sub-title text {
  position: relative;
  z-index: 1;
}

.cu-bar .action.sub-title text[class*="bg-"]:last-child {
  position: absolute;
  display: inline-block;
  bottom: -0.2rem;
  border-radius: 6rpx;
  width: 100%;
  height: 0.6rem;
  left: 0.6rem;
  opacity: 0.3;
  z-index: 0;
}

.cu-bar .action.sub-title text[class*="text-"]:last-child {
  position: absolute;
  display: inline-block;
  bottom: -0.7rem;
  left: 0.5rem;
  opacity: 0.2;
  z-index: 0;
  text-align: right;
  font-weight: 900;
  font-size: 36rpx;
}

.cu-bar.justify-center .action.border-title text:last-child,
.cu-bar.justify-center .action.sub-title text:last-child {
  left: 0;
  right: 0;
  margin: auto;
  text-align: center;
}

.cu-bar .action:first-child {
  margin-left: 30rpx;
  font-size: 30rpx;
}

.cu-bar .action text.text-cut {
  text-align: left;
  width: 100%;
}

.cu-bar .cu-avatar:first-child {
  margin-left: 20rpx;
}

.cu-bar .action:first-child>text[class*="cuIcon-"] {
  margin-left: -0.3em;
  margin-right: 0.3em;
}

.cu-bar .action:last-child {
  margin-right: 30rpx;
}

.cu-bar .action>text[class*="cuIcon-"],
.cu-bar .action>view[class*="cuIcon-"] {
  font-size: 36rpx;
}

.cu-bar .action>text[class*="cuIcon-"]+text[class*="cuIcon-"] {
  margin-left: 0.5em;
}

.cu-bar .content {
  position: absolute;
  text-align: center;
  width: calc(100% - 340rpx);
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  height: 60rpx;
  font-size: 32rpx;
  line-height: 60rpx;
  cursor: none;
  pointer-events: none;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.cu-bar.ios .content {
  bottom: 7px;
  height: 30px;
  font-size: 32rpx;
  line-height: 30px;
}

.cu-bar.btn-group {
  justify-content: space-around;
}

.cu-bar.btn-group button {
  padding: 20rpx 32rpx;
}

.cu-bar.btn-group button {
  flex: 1;
  margin: 0 20rpx;
  max-width: 50%;
}

.cu-bar .search-form {
  background-color: #f5f5f5;
  line-height: 64rpx;
  height: 64rpx;
  font-size: 24rpx;
  color: var(--black);
  flex: 1;
  display: flex;
  align-items: center;
  margin: 0 30rpx;
}

.cu-bar .search-form+.action {
  margin-right: 30rpx;
}

.cu-bar .search-form input {
  flex: 1;
  padding-right: 30rpx;
  height: 64rpx;
  line-height: 64rpx;
  font-size: 26rpx;
  background-color: transparent;
}

.cu-bar .search-form [class*="cuIcon-"] {
  margin: 0 0.5em 0 0.8em;
}

.cu-bar .search-form [class*="cuIcon-"]::before {
  top: 0rpx;
}

.cu-bar.fixed,
.nav.fixed {
  position: fixed;
  width: 100%;
  top: 0;
  z-index: 1024;
  box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.foot {
  position: fixed;
  width: 100%;
  bottom: 0;
  z-index: 1024;
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar {
  padding: 0;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
  padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.cu-tabbar-height {
  min-height: 100rpx;
  height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}

.cu-bar.tabbar.shadow {
  box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.cu-bar.tabbar .action {
  font-size: 22rpx;
  position: relative;
  flex: 1;
  text-align: center;
  padding: 0;
  display: block;
  height: auto;
  line-height: 1;
  margin: 0;
  overflow: initial;
}

.cu-bar.tabbar.shop .action {
  width: 140rpx;
  flex: initial;
}

.cu-bar.tabbar .action.add-action {
  position: relative;
  z-index: 2;
  padding-top: 50rpx;
  background-color: inherit;
}

.cu-bar.tabbar .action.add-action [class*="cuIcon-"] {
  position: absolute;
  width: 70rpx;
  z-index: 2;
  height: 70rpx;
  border-radius: 50%;
  line-height: 70rpx;
  font-size: 50rpx;
  top: -35rpx;
  left: 0;
  right: 0;
  margin: auto;
  padding: 0;
}

.cu-bar.tabbar .action.add-action::after {
  content: "";
  position: absolute;
  width: 100rpx;
  height: 100rpx;
  top: -50rpx;
  left: 0;
  right: 0;
  margin: auto;
  box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
  border-radius: 50rpx;
  background-color: inherit;
  z-index: 0;
}

.cu-bar.tabbar .action.add-action::before {
  content: "";
  position: absolute;
  width: 100rpx;
  height: 30rpx;
  bottom: 30rpx;
  left: 0;
  right: 0;
  margin: auto;
  background-color: inherit;
  z-index: 1;
}

.cu-bar.tabbar .btn-group {
  flex: 1;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 0 10rpx;
}

.cu-bar.tabbar button.action::after {
  border: 0;
}

.cu-bar.tabbar .action [class*="cuIcon-"] {
  width: 100rpx;
  position: relative;
  display: block;
  height: auto;
  margin: 0 auto 10rpx;
  text-align: center;
  font-size: 40rpx;
}

.cu-bar.tabbar .action .cuIcon-cu-image {
  margin: 0 auto;
}

.cu-bar.tabbar .action .cuIcon-cu-image image {
  width: 50rpx;
  height: 50rpx;
  display: inline-block;
}

.cu-bar.tabbar .submit {
  align-items: center;
  display: flex;
  justify-content: center;
  text-align: center;
  position: relative;
  flex: 2;
  align-self: stretch;
}

.cu-bar.tabbar .submit:last-child {
  flex: 2.6;
}

.cu-bar.tabbar .submit+.submit {
  flex: 2;
}

.cu-bar.tabbar.border .action::before {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  transform: scale(0.5);
  transform-origin: 0 0;
  border-right: 1rpx solid rgba(0, 0, 0, 0.1);
  z-index: 3;
}

.cu-bar.tabbar.border .action:last-child:before {
  display: none;
}

.cu-bar.input {
  padding-right: 20rpx;
  background-color: var(--white);
}

.cu-bar.input input {
  overflow: initial;
  line-height: 64rpx;
  height: 64rpx;
  min-height: 64rpx;
  flex: 1;
  font-size: 30rpx;
  margin: 0 20rpx;
}

.cu-bar.input .action {
  margin-left: 20rpx;
}

.cu-bar.input .action [class*="cuIcon-"] {
  font-size: 48rpx;
}

.cu-bar.input input+.action {
  margin-right: 20rpx;
  margin-left: 0rpx;
}

.cu-bar.input .action:first-child [class*="cuIcon-"] {
  margin-left: 0rpx;
}

.cu-custom {
  display: block;
  position: relative;
}

.cu-custom .cu-bar .content {
  width: calc(100% - 440rpx);
}


.cu-custom .cu-bar .content image {
  height: 60rpx;
  width: 240rpx;
}

.cu-custom .cu-bar {
  min-height: 0px;
  padding-right: 220rpx;
  box-shadow: 0rpx 0rpx 0rpx;
  z-index: 9999;
}

.cu-custom .cu-bar .border-custom {
  position: relative;
  background: rgba(0, 0, 0, 0.15);
  border-radius: 1000rpx;
  height: 30px;
}

.cu-custom .cu-bar .border-custom::after {
  content: " ";
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border-radius: inherit;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  border: 1rpx solid var(--white);
  opacity: 0.5;
}

.cu-custom .cu-bar .border-custom::before {
  content: " ";
  width: 1rpx;
  height: 110%;
  position: absolute;
  top: 22.5%;
  left: 0;
  right: 0;
  margin: auto;
  transform: scale(0.5);
  transform-origin: 0 0;
  pointer-events: none;
  box-sizing: border-box;
  opacity: 0.6;
  background-color: var(--white);
}

.cu-custom .cu-bar .border-custom text {
  display: block;
  flex: 1;
  margin: auto !important;
  text-align: center;
  font-size: 34rpx;
}

/* ==================
    组件样式-导航栏
 ==================== */

.nav {
  white-space: nowrap;
}

::-webkit-scrollbar {
  display: none;
}

.nav .cu-item {
  height: 90rpx;
  display: inline-block;
  line-height: 90rpx;
  margin: 0 10rpx;
  padding: 0 20rpx;
}

.nav .cu-item.cur {
  border-bottom: 6rpx solid;
}

/* ==================
    组件样式-列表
 ==================== */
.grayscale {
  filter: grayscale(1);
}

.cu-list+.cu-list {
  margin-top: 30rpx
}

.cu-list>.cu-item {
  transition: all .6s ease-in-out 0s;
  transform: translateX(0rpx)
}

.cu-list>.cu-item.move-cur {
  transform: translateX(-260rpx)
}

.cu-list>.cu-item .move {
  position: absolute;
  right: 0;
  display: flex;
  width: 260rpx;
  height: 100%;
  transform: translateX(100%)
}

.cu-list>.cu-item .move view {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center
}

.cu-list.menu-avatar {
  overflow: hidden;
}

.cu-list.menu-avatar>.cu-item {
  position: relative;
  display: flex;
  padding-right: 10rpx;
  height: 140rpx;
  /* background-color: var(--white); */
  justify-content: flex-end;
  align-items: center
}

.cu-list.menu-avatar>.cu-item>.cu-avatar {
  position: absolute;
  left: 30rpx
}

.cu-list.menu-avatar>.cu-item .flex .text-cut {
  max-width: 510rpx
}

.cu-list.menu-avatar>.cu-item .content {
  position: absolute;
  left: 146rpx;
  width: calc(100% - 96rpx - 60rpx - 120rpx - 20rpx);
  line-height: 1.6em;
}

.cu-list.menu-avatar>.cu-item .content.flex-sub {
  width: calc(100% - 96rpx - 60rpx - 20rpx);
}

.cu-list.menu-avatar>.cu-item .content>view:first-child {
  font-size: 30rpx;
  display: flex;
  align-items: center
}

.cu-list.menu-avatar>.cu-item .content .cu-tag.sm {
  display: inline-block;
  margin-left: 10rpx;
  height: 28rpx;
  font-size: 16rpx;
  line-height: 32rpx
}

.cu-list.menu-avatar>.cu-item .action {
  width: 100rpx;
  text-align: center
}

.cu-list.menu-avatar>.cu-item .action view+view {
  margin-top: 10rpx
}

.cu-list.menu-avatar.comment>.cu-item .content {
  position: relative;
  left: 0;
  width: auto;
  flex: 1;
}

.cu-list.menu-avatar.comment>.cu-item {
  padding: 30rpx 30rpx 30rpx 120rpx;
  height: auto
}

.cu-list.menu-avatar.comment .cu-avatar {
  align-self: flex-start
}

.cu-list.menu>.cu-item {
  position: relative;
  display: flex;
  padding: 0 30rpx;
  min-height: 100rpx;
  background-color: var(--white);
  justify-content: space-between;
  align-items: center
}

.cu-list.menu>.cu-item:last-child:after {
  border: none
}

.cu-list.menu>.cu-item:after {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 200%;
  height: 200%;
  border-bottom: 1rpx solid #ddd;
  border-radius: inherit;
  content: " ";
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none
}

.cu-list.menu>.cu-item.grayscale {
  background-color: #f5f5f5
}

.cu-list.menu>.cu-item.cur {
  background-color: #fcf7e9
}

.cu-list.menu>.cu-item.arrow {
  padding-right: 90rpx
}

.cu-list.menu>.cu-item.arrow:before {
  position: absolute;
  top: 0;
  right: 30rpx;
  bottom: 0;
  display: block;
  margin: auto;
  width: 30rpx;
  height: 30rpx;
  color: var(--grey);
  content: "\e6a3";
  text-align: center;
  font-size: 34rpx;
  font-family: "cuIcon";
  line-height: 30rpx
}

.cu-list.menu>.cu-item button.content {
  padding: 0;
  background-color: transparent;
  justify-content: flex-start
}

.cu-list.menu>.cu-item button.content:after {
  display: none
}

.cu-list.menu>.cu-item .cu-avatar-group .cu-avatar {
  border-color: var(--white)
}

.cu-list.menu>.cu-item .content>view:first-child {
  display: flex;
  align-items: center
}

.cu-list.menu>.cu-item .content>text[class*=cuIcon] {
  display: inline-block;
  margin-right: 10rpx;
  width: 1.6em;
  text-align: center
}

.cu-list.menu>.cu-item .content>image {
  display: inline-block;
  margin-right: 10rpx;
  width: 1.6em;
  height: 1.6em;
  vertical-align: middle
}

.cu-list.menu>.cu-item .content {
  font-size: 30rpx;
  line-height: 1.6em;
  flex: 1
}

.cu-list.menu>.cu-item .content .cu-tag.sm {
  display: inline-block;
  margin-left: 10rpx;
  height: 28rpx;
  font-size: 16rpx;
  line-height: 32rpx
}

.cu-list.menu>.cu-item .action .cu-tag:empty {
  right: 10rpx
}

.cu-list.menu {
  display: block;
  overflow: hidden
}

.cu-list.menu.sm-border>.cu-item:after {
  left: 30rpx;
  width: calc(200% - 120rpx)
}

.cu-list.grid>.cu-item {
  position: relative;
  display: flex;
  padding: 20rpx 0 30rpx;
  transition-duration: 0s;
  flex-direction: column
}

.cu-list.grid>.cu-item:after {
  position: absolute;
  top: 0;
  left: 0;
  box-sizing: border-box;
  width: 200%;
  height: 200%;
  border-right: 1px solid rgba(0, 0, 0, .1);
  border-bottom: 1px solid rgba(0, 0, 0, .1);
  border-radius: inherit;
  content: " ";
  transform: scale(.5);
  transform-origin: 0 0;
  pointer-events: none
}

.cu-list.grid>.cu-item text {
  display: block;
  margin-top: 10rpx;
  color: #888;
  font-size: 26rpx;
  line-height: 40rpx
}

.cu-list.grid>.cu-item [class*=cuIcon] {
  position: relative;
  display: block;
  margin-top: 20rpx;
  width: 100%;
  font-size: 48rpx
}

.cu-list.grid>.cu-item .cu-tag {
  right: auto;
  left: 50%;
  margin-left: 20rpx
}

.cu-list.grid {
  background-color: var(--white);
  text-align: center
}

.cu-list.grid.no-border>.cu-item {
  padding-top: 10rpx;
  padding-bottom: 20rpx
}

.cu-list.grid.no-border>.cu-item:after {
  border: none
}

/* .cu-list.grid.no-border {
  padding: 20rpx 10rpx
} */

.cu-list.grid.col-3>.cu-item:nth-child(3n):after,
.cu-list.grid.col-4>.cu-item:nth-child(4n):after,
.cu-list.grid.col-5>.cu-item:nth-child(5n):after {
  border-right-width: 0
}

.cu-list.card-menu {
  overflow: hidden;
  margin-right: 30rpx;
  margin-left: 30rpx;
  border-radius: 20rpx
}

/* ==================
    组件样式-卡片
 ==================== */

.cu-card {
  display: block;
  overflow: hidden;
}

.cu-card>.cu-item {
  display: block;
  background-color: var(--white);
  overflow: hidden;
  border-radius: 20rpx;
  /* margin: 30rpx; */
}

.cu-card>.cu-item.shadow-blur {
  overflow: initial;
}

.cu-card.no-card>.cu-item {
  margin: 0rpx;
  border-radius: 0rpx;
}

.cu-card .grid.grid-square {
  margin-bottom: -20rpx;
}

.cu-card.case .image {
  position: relative;
}

.cu-card.case .image image {
  width: 100%;
}

.cu-card.case .image .cu-tag {
  position: absolute;
  right: 0;
  top: 0;
}

.cu-card.case .image .cu-bar {
  position: absolute;
  bottom: 0;
  width: 100%;
  background-color: transparent;
  padding: 0rpx 30rpx;
}

.cu-card.case.no-card .image {
  margin: 30rpx 30rpx 0;
  overflow: hidden;
  border-radius: 10rpx;
}

.cu-card.dynamic {
  display: block;
}

.cu-card.dynamic>.cu-item {
  display: block;
  background-color: var(--white);
  overflow: hidden;
}

.cu-card.dynamic>.cu-item>.text-content {
  padding: 0 30rpx 0;
  max-height: 6.4em;
  overflow: hidden;
  font-size: 30rpx;
  margin-bottom: 20rpx;
}

.cu-card.dynamic>.cu-item .square-img {
  width: 100%;
  height: 200rpx;
  border-radius: 6rpx;
}

.cu-card.dynamic>.cu-item .only-img {
  width: 100%;
  height: 320rpx;
  border-radius: 6rpx;
}

.cu-card.article {
  display: block;
}

.cu-card.article>.cu-item {
  padding-bottom: 30rpx;
}

.cu-card.article>.cu-item .title {
  font-size: 30rpx;
  font-weight: 900;
  color: var(--black);
  line-height: 100rpx;
  padding: 0 30rpx;
}

.cu-card.article>.cu-item .content {
  display: flex;
  padding: 0 30rpx;
}

.cu-card.article>.cu-item .content>image {
  width: 240rpx;
  height: 6.4em;
  margin-right: 20rpx;
  border-radius: 6rpx;
}

.cu-card.article>.cu-item .content .desc {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.cu-card.article>.cu-item .content .text-content {
  font-size: 28rpx;
  color: #888;
  height: 4.8em;
  overflow: hidden;
}

/* ==================
    组件样式-表单
 ==================== */

.cu-form-group {
  background-color: var(--white);
  padding: 1rpx 30rpx;
  display: flex;
  align-items: center;
  min-height: 100rpx;
  justify-content: space-between;
}

.cu-form-group+.cu-form-group {
  border-top: 1rpx solid #eee;
}

.cu-form-group .title {
  text-align: justify;
  padding-right: 30rpx;
  font-size: 30rpx;
  position: relative;
  height: 60rpx;
  line-height: 60rpx;
}

.cu-form-group input {
  flex: 1;
  font-size: 30rpx;
  color: #555;
  padding-right: 20rpx;
}

.cu-form-group>text[class*="cuIcon-"] {
  font-size: 36rpx;
  padding: 0;
  box-sizing: border-box;
}

.cu-form-group textarea {
  margin: 32rpx 0 30rpx;
  height: 4.6em;
  width: 100%;
  line-height: 1.2em;
  flex: 1;
  font-size: 28rpx;
  padding: 0;
}

.cu-form-group.align-start .title {
  height: 1em;
  margin-top: 32rpx;
  line-height: 1em;
}

.cu-form-group picker {
  flex: 1;
  padding-right: 40rpx;
  overflow: hidden;
  position: relative;
}

.cu-form-group picker .picker {
  line-height: 100rpx;
  font-size: 28rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  width: 100%;
  text-align: right;
}

.cu-form-group picker::after {
  font-family: "cuIcon";
  display: block;
  content: "\e6a3";
  position: absolute;
  font-size: 34rpx;
  color: var(--grey);
  line-height: 100rpx;
  width: 60rpx;
  text-align: center;
  top: 0;
  bottom: 0;
  right: -20rpx;
  margin: auto;
}

.cu-form-group textarea[disabled],
.cu-form-group textarea[disabled] .placeholder {
  color: transparent;
}

/* ==================
    组件样式-时间轴
 ==================== */

.cu-timeline {
  display: block;
  background-color: var(--white);
}

.cu-timeline .cu-time {
  width: 120rpx;
  text-align: center;
  padding: 20rpx 0;
  font-size: 26rpx;
  color: #888;
  display: block;
}

.cu-timeline>.cu-item {
  padding: 30rpx 30rpx 30rpx 120rpx;
  position: relative;
  display: block;
  z-index: 0;
}

.cu-timeline>.cu-item:not([class*="text-"]) {
  color: #ccc;
}

.cu-timeline>.cu-item::after {
  content: "";
  display: block;
  position: absolute;
  width: 1rpx;
  background-color: #ddd;
  left: 60rpx;
  height: 100%;
  top: 0;
  z-index: 8;
}

.cu-timeline>.cu-item::before {
  font-family: "cuIcon";
  display: block;
  position: absolute;
  top: 36rpx;
  z-index: 9;
  background-color: var(--white);
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  border: none;
  line-height: 50rpx;
  left: 36rpx;
}

.cu-timeline>.cu-item:not([class*="cuIcon-"])::before {
  content: "\e763";
}

.cu-timeline>.cu-item[class*="cuIcon-"]::before {
  background-color: var(--white);
  width: 50rpx;
  height: 50rpx;
  text-align: center;
  border: none;
  line-height: 50rpx;
  left: 36rpx;
}

.cu-timeline>.cu-item>.content {
  padding: 30rpx;
  border-radius: 16rpx;
  display: block;
  line-height: 1.6;
}

.cu-timeline>.cu-item>.content:not([class*="bg-"]) {
  background-color: var(--ghostWhite);
  color: var(--black);
}

.cu-timeline>.cu-item>.content+.content {
  margin-top: 20rpx;
}

/* ==================
    组件样式-轮播
 ==================== */
swiper .a-swiper-dot {
  display: inline-block;
  width: 16rpx;
  height: 16rpx;
  background: rgba(0, 0, 0, .3);
  border-radius: 50%;
  vertical-align: middle;
}

swiper[class*="-dot"] .wx-swiper-dots {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
}

swiper.square-dot .wx-swiper-dot {
  background-color: var(--white);
  opacity: 0.4;
  width: 10rpx;
  height: 10rpx;
  border-radius: 20rpx;
  margin: 0 8rpx !important;
}

swiper.square-dot .wx-swiper-dot.wx-swiper-dot-active {
  opacity: 1;
  width: 30rpx;
}

swiper.round-dot .wx-swiper-dot {
  width: 10rpx;
  height: 10rpx;
  position: relative;
  margin: 4rpx 8rpx !important;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active::after {
  content: "";
  position: absolute;
  width: 10rpx;
  height: 10rpx;
  top: 0rpx;
  left: 0rpx;
  right: 0;
  bottom: 0;
  margin: auto;
  background-color: var(--white);
  border-radius: 20rpx;
}

swiper.round-dot .wx-swiper-dot.wx-swiper-dot-active {
  width: 18rpx;
  height: 18rpx;
}

.screen-swiper {
  min-height: 360rpx;
}

.screen-swiper image,
.screen-swiper video,
.swiper-item image,
.swiper-item video {
  width: 100%;
  display: block;
  height: 100%;
  margin: 0;
  pointer-events: none;
}

.card-swiper {
  height: 400rpx !important;
}

.card-swiper swiper-item {
  width: 610rpx !important;
  left: 70rpx;
  box-sizing: border-box;
  padding: 40rpx 0rpx 70rpx;
  overflow: initial;
}

.card-swiper swiper-item .swiper-item {
  width: 100%;
  display: block;
  height: 100%;
  border-radius: 16rpx;
  transform: scale(0.9);
  transition: all 0.2s ease-in 0s;
  overflow: hidden;
}

.card-swiper swiper-item.cur .swiper-item {
  transform: none;
  transition: all 0.2s ease-in 0s;
}

.tower-swiper {
  height: 420rpx;
  position: relative;
  max-width: 750rpx;
  overflow: hidden;
}

.tower-swiper .tower-item {
  position: absolute;
  width: 300rpx;
  height: 380rpx;
  top: 0;
  bottom: 0;
  left: 50%;
  margin: auto;
  transition: all 0.2s ease-in 0s;
  opacity: 1;
}

.tower-swiper .tower-item.none {
  opacity: 0;
}

.tower-swiper .tower-item .swiper-item {
  width: 100%;
  height: 100%;
  border-radius: 6rpx;
  overflow: hidden;
}

.tower-swiper .tower-item {
  transform: scale(calc(0.5 + var(--index) / 10));
  margin-left: calc(var(--left) * 100rpx - 150rpx);
  z-index: var(--index);
}

/* 3D轮播 */
.swiper-3d {
  width: 100%;
  height: 600rpx;
  background-color: #eee;
}

.swiper-3d .item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-3d .item image {
  height: 90%;
}

.swiper-3d .item .active {
  transform: scale(1);
  transition: all .3s ease-in 0s;
  border-radius: 15rpx;
}

.swiper-3d .item .no-active {
  transform: scale(.8);
  transition: all .3s ease-in 0s;
  opacity: .3;
}

/* ==================
    组件样式-模态框
 ==================== */

.cu-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1110;
  opacity: 0;
  outline: 0;
  text-align: center;
  -ms-transform: scale(1.185);
  transform: scale(1.185);
  backface-visibility: hidden;
  perspective: 2000rpx;
  background: rgba(0, 0, 0, 0.6);
  transition: all 0.3s ease-in-out 0s;
  pointer-events: none;
}

.cu-modal::before {
  content: "\200B";
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.cu-modal.show {
  opacity: 1;
  transition-duration: 0.3s;
  -ms-transform: scale(1);
  transform: scale(1);
  overflow-x: hidden;
  overflow-y: auto;
  pointer-events: auto;
}

.cu-dialog {
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin-left: auto;
  margin-right: auto;
  width: 680rpx;
  max-width: 100%;
  background-color: #f8f8f8;
  border-radius: 10rpx;
  overflow: hidden;
}

.cu-modal.bottom-modal::before {
  vertical-align: bottom;
}

.cu-modal.bottom-modal .cu-dialog {
  width: 100%;
  border-radius: 0;
}

.cu-modal.bottom-modal {
  margin-bottom: -1000rpx;
}

.cu-modal.bottom-modal.show {
  margin-bottom: 0;
}

.cu-modal.drawer-modal {
  transform: scale(1);
  display: flex;
}

.cu-modal.drawer-modal .cu-dialog {
  height: 100%;
  min-width: 200rpx;
  border-radius: 0;
  margin: initial;
  transition-duration: 0.3s;
}

.cu-modal.drawer-modal.justify-start .cu-dialog {
  transform: translateX(-100%);
}

.cu-modal.drawer-modal.justify-end .cu-dialog {
  transform: translateX(100%);
}

.cu-modal.drawer-modal.show .cu-dialog {
  transform: translateX(0%);
}

.cu-modal .cu-dialog>.cu-bar:first-child .action {
  min-width: 100rpx;
  margin-right: 0;
  min-height: 100rpx;
}

/* ==================
    组件样式-步骤条
 ==================== */

.cu-steps {
  display: flex;
}

scroll-view.cu-steps {
  display: block;
  white-space: nowrap;
}

scroll-view.cu-steps .cu-item {
  display: inline-block;
}

.cu-steps .cu-item {
  flex: 1;
  text-align: center;
  position: relative;
  min-width: 100rpx;
}

.cu-steps .cu-item:not([class*="text-"]) {
  color: var(--grey);
}

.cu-steps .cu-item [class*="cuIcon-"],
.cu-steps .cu-item .num {
  display: block;
  font-size: 40rpx;
  line-height: 80rpx;
}

.cu-steps .cu-item::before,
.cu-steps .cu-item::after,
.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
  content: "";
  display: block;
  position: absolute;
  height: 0px;
  width: calc(100% - 80rpx);
  border-bottom: 1px solid #ccc;
  left: calc(0px - (100% - 80rpx) / 2);
  top: 40rpx;
  z-index: 0;
}

.cu-steps.steps-arrow .cu-item::before,
.cu-steps.steps-arrow .cu-item::after {
  content: "\e6a3";
  font-family: "cuIcon";
  height: 30rpx;
  border-bottom-width: 0px;
  line-height: 30rpx;
  top: 0;
  bottom: 0;
  margin: auto;
  color: #ccc;
}

.cu-steps.steps-bottom .cu-item::before,
.cu-steps.steps-bottom .cu-item::after {
  bottom: 40rpx;
  top: initial;
}

.cu-steps .cu-item::after {
  border-bottom: 1px solid currentColor;
  width: 0px;
  transition: all 0.3s ease-in-out 0s;
}

.cu-steps .cu-item[class*="text-"]::after {
  width: calc(100% - 80rpx);
  color: currentColor;
}

.cu-steps .cu-item:first-child::before,
.cu-steps .cu-item:first-child::after {
  display: none;
}

.cu-steps .cu-item .num {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  line-height: 40rpx;
  margin: 20rpx auto;
  font-size: 24rpx;
  border: 1px solid currentColor;
  position: relative;
  overflow: hidden;
}

.cu-steps .cu-item[class*="text-"] .num {
  background-color: currentColor;
}

.cu-steps .cu-item .num::before,
.cu-steps .cu-item .num::after {
  content: attr(data-index);
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  transition: all 0.3s ease-in-out 0s;
  transform: translateY(0rpx);
}

.cu-steps .cu-item[class*="text-"] .num::before {
  transform: translateY(-40rpx);
  color: var(--white);
}

.cu-steps .cu-item .num::after {
  transform: translateY(40rpx);
  color: var(--white);
  transition: all 0.3s ease-in-out 0s;
}

.cu-steps .cu-item[class*="text-"] .num::after {
  content: "\e645";
  font-family: "cuIcon";
  color: var(--white);
  transform: translateY(0rpx);
}

.cu-steps .cu-item[class*="text-"] .num.err::after {
  content: "\e646";
}

/* ==================
    组件样式-轻提示
 ==================== */

.toast-fixed-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* ==================
    组件样式-手风琴
 ==================== */

.piano-hidden-content {
  display: none !important;
}

.piano-text {
  overflow: hidden;
  text-align: justify;
  line-height: 40rpx;
  max-height: 160rpx;
}

.piano-text::before {
  content: '';
  height: calc(100% - 20px);
  float: right;
}

.piano-btn {
  position: relative;
  float: right;
  clear: both;
}

.piano-btn::before {
  content: '...';
  position: absolute;
  left: 4rpx;
  font-size: 32rpx;
  color: rgb(71, 71, 71);
  transform: translateX(-100%)
}

.piano-btn::after {
  content: '展开'
}

.piano-ative {
  max-height: none;
}

.piano-ative_label::after {
  content: '收起'
}

/*在展开状态下隐藏省略号*/
.piano-ative_label::before {
  visibility: hidden;
}

.piano-btn-icon {
  position: relative;
  float: right;
  clear: both;
  font-size: 25rpx;
  height: 42rpx;
}

.piano-btn-icon::before {
  content: '...';
  position: absolute;
  left: 8rpx;
  font-size: 32rpx;
  color: rgb(71, 71, 71);
  transform: translateX(-100%)
}

.piano-btn-icon::after {
  visibility: hidden;
}

/* ==================
    组件样式-滚动提示
 ==================== */

.tips-swiperitem {
  height: 80rpx;
}

.tips-box {
  height: 60rpx;
  line-height: 60rpx;
}

.tips-new-swiper {
  flex: 1;
  height: 66rpx;
}

.tips-bot-swiper {
  width: 700rpx;
  height: 120rpx;
  font-size: 28rpx;
  margin: 24rpx auto 0;
  overflow: hidden;
}

.tips-boxs {
  width: 690rpx;
  border-radius: 30rpx;
  box-sizing: border-box;
  padding: 9rpx;
  margin-bottom: 25rpx;
}

.tips-box_i {
  width: 690rpx;
  box-sizing: border-box;
  height: 460rpx;
}

.tips-swiper {
  height: 460rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

/* ==================
    组件样式-日历一
 ==================== */

.calendar-main-center {
  height: 450rpx;
}

.calendar-main-center view {
  width: 96rpx;
  height: 56rpx;
  justify-content: center;
  align-items: center;
}

.calendar-main-center-mini {
  height: 450rpx;
}

.calendar-main-center-mini view {
  width: 89rpx;
  height: 56rpx;
  justify-content: center;
  align-items: center;
}

/* ==================
    组件样式-表格
 ==================== */

.table {
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: row;
}

.table-tr {
  display: flex;
  justify-content: space-around;
  text-align: center;
  flex-direction: row;
  align-items: center;
  background-color: #fff;
  width: 100%;
  padding: 20rpx;
}

.table-th {
  flex: 1;
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
}

/* ==================
    扩展样式-索引列表
 ==================== */

.indexBar {
  position: fixed;
  right: 0px;
  bottom: 0px;
  padding: 20rpx 20rpx 20rpx 60rpx;
}

.indexBar .indexBar-box {
  width: 40rpx;
  height: auto;
}

.indexBar-item {
  flex: 1;
  width: 40rpx;
  height: 40rpx;
  font-size: 24rpx;
  line-height: 40rpx;
}

movable-view.indexBar-item {
  width: 40rpx;
  height: 40rpx;
  z-index: 9;
  position: relative;
}

movable-view.indexBar-item::before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  top: 10rpx;
  height: 20rpx;
  width: 4rpx;
  background-color: #f37b1d;
}

.indexToast {
  position: fixed;
  top: 0;
  right: 80rpx;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  width: 100rpx;
  height: 100rpx;
  border-radius: 10rpx;
  margin: auto;
  color: #fff;
  line-height: 100rpx;
  text-align: center;
  font-size: 48rpx;
}

/* ==================
    扩展样式-全屏抽屉
 ==================== */

.cu-drawer-page {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0vw;
  transition: all 0.4s;
}

.cu-drawer-page.show {
  transform: scale(0.9, 0.9);
  will-change: transform;
  left: 85vw;
  box-shadow: 0 0 60rpx rgba(0, 0, 0, 0.2);
  transform-origin: 0;
}

.cu-drawer-window {
  position: absolute;
  width: 85vw;
  height: 100vh;
  left: 0;
  top: 0;
  transform: scale(0.9, 0.9) translateX(-100%);
  will-change: transform;
  opacity: 0;
  pointer-events: none;
  transition: all 0.4s;
}

.cu-drawer-window.show {
  transform: scale(1, 1) translateX(0%);
  will-change: transform;
  opacity: 1;
  pointer-events: all;
}

.cu-drawer-close {
  position: absolute;
  width: 40vw;
  height: 100vh;
  right: 0;
  top: 0;
  color: transparent;
  padding-bottom: 30rpx;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  background-image: linear-gradient(90deg, rgba(0, 0, 0, 0.01), rgba(0, 0, 0, 0.6));
  letter-spacing: 5px;
  font-size: 50rpx;
  opacity: 0;
  pointer-events: none;
  transition: all 0.4s;
}

.cu-drawer-close.show {
  opacity: 1;
  pointer-events: all;
  width: 15vw;
  color: #fff;
}

.cu-drawer-page .cu-bar.tabbar .action button.icon {
  width: 64rpx;
  height: 64rpx;
  line-height: 64rpx;
  margin: 0;
  display: inline-block;
}

.cu-drawer-page .cu-bar.tabbar .action .cu-avatar {
  margin: 0;
}

.cu-drawer-page .nav {
  flex: 1;
}

.cu-drawer-page .nav .cu-item.cur {
  border-bottom: 0;
  position: relative;
}

.cu-drawer-page .nav .cu-item.cur::after {
  content: "";
  width: 10rpx;
  height: 10rpx;
  background-color: currentColor;
  position: absolute;
  bottom: 10rpx;
  border-radius: 10rpx;
  left: 0;
  right: 0;
  margin: auto;
}

.cu-drawer-page .cu-bar.tabbar .action {
  flex: initial;
}

/* ==================
    扩展样式-垂直导航
 ==================== */

.cu-vertical-nav.nav {
  width: 200rpx;
  white-space: initial;
}

.cu-vertical-nav.nav .cu-item {
  width: 100%;
  text-align: center;
  margin: 0;
  border: none;
  height: 50px;
  position: relative;
}

.cu-vertical-nav.nav .cu-item.cur {
  background-color: #f1f1f1;
}

.cu-vertical-nav.nav .cu-item.cur::after {
  content: "";
  width: 8rpx;
  height: 30rpx;
  border-radius: 10rpx 0 0 10rpx;
  position: absolute;
  background-color: currentColor;
  top: 0;
  right: 0rpx;
  bottom: 0;
  margin: auto;
}

/* ==================
    扩展样式-聊天
 ==================== */

.cu-chat {
  display: flex;
  flex-direction: column;
}

.cu-chat .cu-item {
  display: flex;
  padding: 30rpx 30rpx 70rpx;
  position: relative;
}

.cu-chat .cu-item>.cu-avatar {
  width: 80rpx;
  height: 80rpx;
}

.cu-chat .cu-item>.main {
  max-width: calc(100% - 260rpx);
  margin: 0 40rpx;
  display: flex;
  align-items: center;
}

.cu-chat .cu-item>image {
  height: 320rpx;
}

.cu-chat .cu-item>.main .content {
  padding: 20rpx;
  border-radius: 6rpx;
  display: inline-flex;
  max-width: 100%;
  align-items: center;
  font-size: 30rpx;
  position: relative;
  min-height: 80rpx;
  line-height: 40rpx;
  text-align: left;
}

.cu-chat .cu-item>.main .content:not([class*="bg-"]) {
  background-color: var(--white);
  color: var(--black);
}

.cu-chat .cu-item .date {
  position: absolute;
  font-size: 24rpx;
  color: var(--grey);
  width: calc(100% - 320rpx);
  bottom: 20rpx;
  left: 160rpx;
}

.cu-chat .cu-item .action {
  padding: 0 30rpx;
  display: flex;
  align-items: center;
}

.cu-chat .cu-item>.main .content::after {
  content: "";
  top: 27rpx;
  transform: rotate(45deg);
  position: absolute;
  z-index: 100;
  display: inline-block;
  overflow: hidden;
  width: 24rpx;
  height: 24rpx;
  left: -12rpx;
  right: initial;
  background-color: inherit;
}

.cu-chat .cu-item.self>.main .content::after {
  left: auto;
  right: -12rpx;
}

.cu-chat .cu-item>.main .content::before {
  content: "";
  top: 30rpx;
  transform: rotate(45deg);
  position: absolute;
  z-index: -1;
  display: inline-block;
  overflow: hidden;
  width: 24rpx;
  height: 24rpx;
  left: -12rpx;
  right: initial;
  background-color: inherit;
  filter: blur(5rpx);
  opacity: 0.3;
}

.cu-chat .cu-item>.main .content:not([class*="bg-"])::before {
  background-color: var(--black);
  opacity: 0.1;
}

.cu-chat .cu-item.self>.main .content::before {
  left: auto;
  right: -12rpx;
}

.cu-chat .cu-item.self {
  justify-content: flex-end;
  text-align: right;
}

.cu-chat .cu-info {
  display: inline-block;
  margin: 20rpx auto;
  font-size: 24rpx;
  padding: 8rpx 12rpx;
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 6rpx;
  color: var(--white);
  max-width: 400rpx;
  line-height: 1.4;
}


/* ==================
    扩展样式-瀑布流
 ==================== */

.cu-waterfall {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  min-height: 100vh;
}

/* ==================
    扩展样式-卡片翻转
 ==================== */

.cu-overturn-box {
  position: absolute;
  width: 300px;
  height: 300px;
  transition: all 1s;
  backface-visibility: hidden;
  overflow: hidden;
}

.cu-b2 {
  transform: rotateY(-180deg);
}

/* ==================
    扩展样式-骨架屏
 ==================== */

.skeleton-double-column {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  background: #fff;
}

.skeleton-double-column .cu-card {
  width: 50%;
}

/* ==================
    扩展样式-抽奖
 ==================== */

.cu-draw-con {
  margin: 30rpx auto;
  width: 704rpx;
  border-radius: 30rpx;
  background: linear-gradient(0deg, pink 0%, #e95c74 100%);
  box-shadow: 0px 14px 4px 0 #e95c74;
  box-sizing: border-box;
  padding: 20rpx;
}

.cu-draw-draw_sc {
  width: 637rpx;
  height: 69rpx;
  background: rgba(236, 215, 219, 0.53);
  border-radius: 34rpx;
  padding: 0 30rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.cu-draw-swiper {
  height: 100%;
  font-size: 28rpx;
  color: #fff;
  flex: 1;
  line-height: 69rpx;
}

.cu-draw-box {
  width: 632rpx;
  box-shadow: -6px 8px 81px 0px rgb(255, 192, 203, .73), 0px 0px 24px 0px #FFFFFF inset;
  border-radius: 30rpx;
  box-sizing: border-box;
  padding: 9rpx;
  margin-bottom: 25rpx;
}

.cu-draw-b_box {
  width: 614rpx;
  border-radius: 30rpx;
  box-shadow: 0px 0px 10px 0px rgb(255, 192, 203, .53), 0px 0px 27px 0px #FFFFFF inset;
  box-sizing: border-box;
  padding: 19rpx 15rpx;
}

.cu-draw-prize_box {
  flex-wrap: wrap;
  position: relative;
  z-index: 99;
}

.cu-draw-prize_item {
  width: 175rpx;
  height: 164rpx;
  margin-bottom: 14rpx;
  font-size: 24rpx;
  font-weight: bold;
  box-sizing: border-box;
  padding-top: 10rpx;
}

.cu-draw-prize_start {
  padding-top: 0 !important;
}

.cu-draw-prize_img image {
  width: 100rpx;
  height: 100rpx;
}

.cu-draw-active {
  position: relative;
  border: 2rpx solid transparent;
  box-shadow: none;
}

.cu-draw-active::after {
  content: '';
  position: absolute;
  background-image: linear-gradient(to top, #f77062 0%, #fe5196 100%);
  z-index: -1;
}

/* ==================
    扩展样式-打卡
 ==================== */

.cu-clockin-time {
  width: 300rpx;
  height: 300rpx;
  position: relative;
}

.cu-clockin-time::after {
  content: '';
  top: 0;
  left: 0;
  width: 300rpx;
  height: 300rpx;
  border-radius: 50%;
  position: absolute;
  z-index: 5;
  background: rgba(48, 124, 237, 0.08);
  animation: clock-scale 1s infinite alternate-reverse;
}

.cu-clockin-time::before {
  position: absolute;
  z-index: 11;
  content: '';
  width: 280rpx;
  height: 280rpx;
  background: rgb(48, 124, 237, 0.79);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.cu-clockin-time_over {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 30rpx;
  border-radius: 50%;
  background: rgba(48, 124, 237, 0.08);
  position: relative;
}

.cu-clockin-time_over::after {
  position: absolute;
  z-index: 11;
  content: '';
  width: 280rpx;
  height: 280rpx;
  background: #C6CED9;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes clock-scale {

  0% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

/* 样式1 */
.cu-coupon-box {
  width: 700rpx;
  height: 360rpx;
  overflow: hidden;
  box-sizing: border-box;
  filter: drop-shadow(2px 2px 1px rgba(0, 0, 0, .3));
  /*  mask 裁剪的图形需要在外层嵌套一层父级，不然投影会被 mask 直接裁剪掉 */
  position: relative;
}

.cu-coupon-box::after {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(35deg, var(--orange), var(--pink));
  -webkit-mask: radial-gradient(circle at 20rpx 65%, transparent 20rpx, red 0) -20rpx;
  /* red 0 ：后面颜色的位置只需要小于等于前面颜色的位置就行了 */
  mask: radial-gradient(circle at 20rpx 65%, transparent 20rpx, red 0) -20rpx;
  z-index: -1;
  animation: bgchange 3s infinite ease-in-out alternate-reverse;
}

@keyframes bgchange {
  0% {
    filter: hue-rotate(0deg);
  }

  100% {
    filter: hue-rotate(-50deg);
  }
}

/* ==================
    扩展样式-卡券
 ==================== */

.cu-coupon-content {
  align-items: baseline;
  position: relative;
}

.cu-coupon-content::before {
  content: '';
  width: 600rpx;
  height: 3rpx;
  position: absolute;
  background-image: linear-gradient(90deg, var(--mauve) 50%, transparent 50%);
  background-size: 30rpx 30rpx;
  left: calc(50% - 300rpx);
  bottom: -30rpx;
}

.cu-coupon-content text:nth-child(2) {
  font-size: 80rpx;
  line-height: 90rpx;
  letter-spacing: 2rpx;
  margin: 0px 20rpx;
}

/* 样式 */
.cu-coupon-box2 {
  width: 700rpx;
  height: 220rpx;
  overflow: hidden;
  position: relative;
  background: radial-gradient(circle at right top, transparent 18rpx, var(--pinkLight) 0) top left / 215rpx 51% no-repeat,
    radial-gradient(circle at right bottom, transparent 18rpx, var(--pinkLight) 0) bottom left /215rpx 51% no-repeat,
    radial-gradient(circle at left top, transparent 18rpx, var(--cyanLight) 0) top right /487rpx 51% no-repeat,
    radial-gradient(circle at left bottom, transparent 18rpx, var(--cyanLight) 0) bottom right /487rpx 51% no-repeat;
  filter: drop-shadow(2px 2px 1px rgba(0, 0, 0, .3));
  margin: 20px auto;
  /* 解决filter-导致border-radius生效 */
  transform: translate3d(0, 0, 0);
}

.cu-coupon-box2::after {
  content: '';
  height: 184rpx;
  width: 3rpx;
  background-image: linear-gradient(0deg, #fff 50%, transparent 50%);
  background-size: 20rpx 20rpx;
  position: absolute;
  left: 212rpx;
  top: 0;
  bottom: 0;
  margin: auto;
}

/* 样式3 */
.cu-coupon-card3 {
  margin: 20px auto;
  width: 700rpx;
  height: 200rpx;
  position: relative;
  filter: drop-shadow(2px 2px 1px rgba(0, 0, 0, .2));
}

.cu-coupon-card3::before {
  content: '';
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  height: 100%;
  -webkit-mask: radial-gradient(circle at 30rpx 30rpx, transparent 30rpx, red 0) -30rpx -30rpx;
  mask: radial-gradient(circle at 30rpx 30rpx, transparent 30rpx, red 0) -30rpx -30rpx;
  background-image: linear-gradient(to right, var(--cyanLight) 0%, var(--pinkLight) 100%);
}

/* 样式4 */
.cu-coupon-card4 {
  margin: 20px auto;
  width: 700rpx;
  height: 200rpx;
  position: relative;
  border-radius: 20rpx;
  background: radial-gradient(circle at 30rpx 25rpx, transparent 10rpx, #f77062 0, #fe5196 100%) top left/700rpx 40rpx;
  filter: drop-shadow(2px 2px 1px rgba(0, 0, 0, .2));
}

/* 样式5 */
.cu-coupon-card5 {
  margin: 20px auto;
  width: 700rpx;
  height: 250rpx;
  position: relative;
  border-radius: 20rpx;
  filter: drop-shadow(2px 2px 2px rgba(0, 0, 0, .3));
}

.cu-coupon-card5::after {
  content: '';
  border-radius: 20rpx;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-mask-image: radial-gradient(circle at 9px 46px, transparent 9px, red 9.5px),
    linear-gradient(90deg, transparent 25%, red 0, red 75%, transparent 0);
  mask-image: radial-gradient(circle at 9px 46px, transparent 9px, red 9.5px),
    linear-gradient(90deg, transparent 25%, red 0, red 75%, transparent 0);
  -webkit-mask-size: 100%, 8px 3px;
  mask-size: 100%, 8px 3px;
  -webkit-mask-repeat: repeat, repeat-x;
  mask-repeat: repeat, repeat-x;
  -webkit-mask-position: -9px, 50% 44.5px;
  mask-position: -9px, 50% 44.5px;
  -webkit-mask-composite: source-out;
  mask-composite: subtract;
  background: linear-gradient(45deg, var(--pink), var(--pinkLight));
}

/* 样式6 */
.cu-coupon-card6 {
  margin: 20px auto;
  width: 700rpx;
  height: 220rpx;
  position: relative;
  border-radius: 20rpx;
  filter: drop-shadow(8rpx 8rpx 0 rgba(34, 34, 34, .1));
}

.cu-coupon-card6::after {
  content: '';
  border-radius: 20rpx;
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  -webkit-mask: radial-gradient(circle at 50%, red 5rpx, #0000 0) 50% 50% / 100% 20rpx,
    radial-gradient(circle at 20rpx 20rpx, #0000 20rpx, red 0) -20rpx -20rpx / 50%;
  mask: radial-gradient(circle at 50%, red 5rpx, #0000 0) 50% 50% / 100% 20rpx,
    radial-gradient(circle at 20rpx 20rpx, #0000 20rpx, red 0) -20rpx -20rpx / 50%;
  -webkit-mask-composite: destination-out;
  mask-composite: destination-out;
  background: linear-gradient(45deg, var(--orange), var(--pink));
}

/* 样式7 */

.cu-coupon-card7 {
  width: 683rpx;
  height: 856rpx;
  background: #FFFFFF;
  border-radius: 40rpx;
  box-sizing: border-box;
  padding: 0 24rpx;
}

.cu-coupon-top {
  box-sizing: border-box;
  height: 475rpx;
  padding-top: 40rpx;
  border-bottom: 1px dashed #66E0FE;
  position: relative;
}

.cu-coupon-top::before {
  content: '';
  position: absolute;
  width: 22rpx;
  height: 44rpx;
  background: #57D4F4;
  left: -24rpx;
  bottom: -24rpx;
  border-radius: 0 22rpx 22rpx 0;
}

.cu-coupon-top::after {
  content: '';
  position: absolute;
  width: 22rpx;
  height: 44rpx;
  background: #57D4F4;
  right: -24rpx;
  bottom: -24rpx;
  border-radius: 22rpx 0 0 22rpx;
}

.cu-coupon-title {
  display: flex;
  align-items: center;
  justify-content: center;

}

.cu-coupon-title text {
  font-size: 30rpx;
  font-weight: 600;
  color: #147ABE;
  padding: 0 13rpx 0 20rpx;
}

.cu-coupon-title span {
  width: 21rpx;
  height: 21rpx;
  background: #7FE0F7;
  border-radius: 50%;
  position: relative;
}

.cu-coupon-title span::after {
  content: '';
  position: absolute;
  width: 14rpx;
  height: 14rpx;
  background: rgba(255, 190, 85, 0.58);
  border-radius: 50%;
  bottom: 0;
  right: -7rpx;
}

/* ==================
    扩展样式-商品滑动
 ==================== */

.cu-good-act {
  overflow: hidden;
  position: relative;
  height: 228rpx;
  border-radius: 20rpx;
}

.cu-good-point-con {
  position: absolute;
  top: 19rpx;
  left: 107rpx;
  width: 84%;
  white-space: nowrap;
  overflow-y: auto;
  box-sizing: border-box;
}

.cu-good-point-con .swiper-box {
  height: 192rpx;
  overflow: hidden;
}

.cu-good-point-flex {
  float: left;
  width: 33.33%;
  height: 192rpx;
  text-align: center;
}

.cu-good-point-img {
  display: block;
  width: 145rpx;
  height: 150rpx;
  margin: 0 auto;
  margin-bottom: 9rpx;
}

.cu-good-t-box {
  width: 110rpx;
  height: 100%;
  text-align: center;
}

.cu-good-t-box:after {
  content: '';
  display: inline-block;
  height: 100%;
  vertical-align: middle;
}

.cu-good-title {
  white-space: pre-wrap;
  width: 50rpx;
  text-align: center;
  font-weight: 600;
  color: #fff;
  font-size: 30rpx;
  display: inline-block;
  vertical-align: middle;
}

.cu-good-point-flex-w {
  float: left;
  width: 100%;
  height: 192rpx;
  text-align: center;
}

.cu-good-wd1 {
  width: 28%;
}

.cu-good-wd2 {
  width: 56%;
}

/* ==================
    其他样式-底部
 ==================== */

.colorui-text {
  font-size: 20rpx;
  color: #cecece;
  margin-top: -12rpx;
}

/* ==================
    其他样式-图片
 ==================== */

image {
  max-width: 100%;
  display: inline-block;
  position: relative;
  z-index: 0;
}

image.loading::before {
  content: "";
  background-color: #f5f5f5;
  display: block;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -2;
}

image.loading::after {
  content: "\e7f1";
  font-family: "cuIcon";
  position: absolute;
  top: 0;
  left: 0;
  width: 32rpx;
  height: 32rpx;
  line-height: 32rpx;
  right: 0;
  bottom: 0;
  z-index: -1;
  font-size: 32rpx;
  margin: auto;
  color: #ccc;
  -webkit-animation: cuIcon-spin 2s infinite linear;
  animation: cuIcon-spin 2s infinite linear;
  display: block;
}

.response {
  width: 100%;
}

/* ==================
  基础样式-布局
 ==================== */

view,
scroll-view,
swiper,
button,
input,
textarea,
label,
navigator,
image {
  box-sizing: border-box;
}

/* 内外边距 */

.margin-0 {
  margin: 0;
}

.margin-xs {
  margin: 10rpx;
}

.margin-sm {
  margin: 20rpx;
}

.margin {
  margin: 30rpx;
}

.margin-lg {
  margin: 40rpx;
}

.margin-xl {
  margin: 50rpx;
}

.margin-xxl {
  margin: 60rpx;
}

.margin-top-xs {
  margin-top: 10rpx;
}

.margin-top-sm {
  margin-top: 20rpx;
}

.margin-top {
  margin-top: 30rpx;
}

.margin-top-lg {
  margin-top: 40rpx;
}

.margin-top-xl {
  margin-top: 50rpx;
}

.margin-top-xxl {
  margin-top: 60rpx;
}

.margin-right-xs {
  margin-right: 10rpx;
}

.margin-right-sm {
  margin-right: 20rpx;
}

.margin-right {
  margin-right: 30rpx;
}

.margin-right-lg {
  margin-right: 40rpx;
}

.margin-right-xl {
  margin-right: 50rpx;
}

.margin-right-xxl {
  margin-right: 60rpx;
}

.margin-bottom-xs {
  margin-bottom: 10rpx;
}

.margin-bottom-sm {
  margin-bottom: 20rpx;
}

.margin-bottom {
  margin-bottom: 30rpx;
}

.margin-bottom-lg {
  margin-bottom: 40rpx;
}

.margin-bottom-xl {
  margin-bottom: 50rpx;
}

.margin-bottom-xxl {
  margin-bottom: 60rpx;
}

.margin-left-xs {
  margin-left: 10rpx;
}

.margin-left-sm {
  margin-left: 20rpx;
}

.margin-left {
  margin-left: 30rpx;
}

.margin-left-lg {
  margin-left: 40rpx;
}

.margin-left-xl {
  margin-left: 50rpx;
}

.margin-left-xxl {
  margin-left: 60rpx;
}

.margin-lr-xs {
  margin-left: 10rpx;
  margin-right: 10rpx;
}

.margin-lr-sm {
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.margin-lr {
  margin-left: 30rpx;
  margin-right: 30rpx;
}

.margin-lr-lg {
  margin-left: 40rpx;
  margin-right: 40rpx;
}

.margin-lr-xl {
  margin-left: 50rpx;
  margin-right: 50rpx;
}

.margin-lr-xxl {
  margin-left: 60rpx;
  margin-right: 60rpx;
}

.margin-tb-xs {
  margin-top: 10rpx;
  margin-bottom: 10rpx;
}

.margin-tb-sm {
  margin-top: 20rpx;
  margin-bottom: 20rpx;
}

.margin-tb {
  margin-top: 30rpx;
  margin-bottom: 30rpx;
}

.margin-tb-lg {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
}

.margin-tb-xl {
  margin-top: 50rpx;
  margin-bottom: 50rpx;
}

.margin-tb-xxl {
  margin-top: 60rpx;
  margin-bottom: 60rpx;
}

.padding-0 {
  padding: 0;
}

.padding-xs {
  padding: 10rpx;
}

.padding-sm {
  padding: 20rpx;
}

.padding {
  padding: 30rpx;
}

.padding-lg {
  padding: 40rpx;
}

.padding-xl {
  padding: 50rpx;
}

.padding-xxl {
  padding: 60rpx;
}

.padding-top-xs {
  padding-top: 10rpx;
}

.padding-top-sm {
  padding-top: 20rpx;
}

.padding-top {
  padding-top: 30rpx;
}

.padding-top-lg {
  padding-top: 40rpx;
}

.padding-top-xl {
  padding-top: 50rpx;
}

.padding-top-xxl {
  padding-top: 64rpx;
}

.padding-top-sl {
  padding-top: 84rpx;
}

.padding-right-xs {
  padding-right: 10rpx;
}

.padding-right-sm {
  padding-right: 20rpx;
}

.padding-right {
  padding-right: 30rpx;
}

.padding-right-lg {
  padding-right: 40rpx;
}

.padding-right-xl {
  padding-right: 50rpx;
}

.padding-bottom-xs {
  padding-bottom: 10rpx;
}

.padding-bottom-sm {
  padding-bottom: 20rpx;
}

.padding-bottom {
  padding-bottom: 30rpx;
}

.padding-bottom-lg {
  padding-bottom: 40rpx;
}

.padding-bottom-xl {
  padding-bottom: 50rpx;
}

.padding-bottom-xxl {
  padding-bottom: 60rpx;
}

.padding-left-xs {
  padding-left: 10rpx;
}

.padding-left-sm {
  padding-left: 20rpx;
}

.padding-left {
  padding-left: 30rpx;
}

.padding-left-lg {
  padding-left: 40rpx;
}

.padding-left-xl {
  padding-left: 50rpx;
}

.padding-left-xxl {
  padding-left: 60rpx;
}

.padding-lr-xs {
  padding-left: 10rpx;
  padding-right: 10rpx;
}

.padding-lr-sm {
  padding-left: 20rpx;
  padding-right: 20rpx;
}

.padding-lr {
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.padding-lr-lg {
  padding-left: 40rpx;
  padding-right: 40rpx;
}

.padding-lr-xl {
  padding-left: 50rpx;
  padding-right: 50rpx;
}

.padding-lr-xxl {
  padding-left: 50rpx;
  padding-right: 50rpx;
}

.padding-tb-xs {
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.padding-tb-sm {
  padding-top: 20rpx;
  padding-bottom: 20rpx;
}

.padding-tb {
  padding-top: 30rpx;
  padding-bottom: 30rpx;
}

.padding-tb-lg {
  padding-top: 40rpx;
  padding-bottom: 40rpx;
}

.padding-tb-xl {
  padding-top: 50rpx;
  padding-bottom: 50rpx;
}

.padding-tb-xxl {
  padding-top: 50rpx;
  padding-bottom: 50rpx;
}

/* 圆角样式 */
.round {
  border-radius: 5000rpx;
}

.radius {
  border-radius: 8rpx;
}

.radius-top {
  border-radius: 8rpx 8rpx 0rpx 0rpx;
}

.radius-bottom {
  border-radius: 0rpx 0rpx 8rpx 8rpx;
}

.radius-top-left {
  border-radius: 8rpx 0rpx 8rpx 0rpx;
}

.radius-top-right {
  border-radius: 0rpx 8rpx 0rpx 8rpx;
}

.radius-df {
  border-radius: 16rpx;
}

.radius-df-top {
  border-radius: 16rpx 16rpx 0rpx 0rpx;
}

.radius-df-bottom {
  border-radius: 0rpx 0rpx 16rpx 16rpx;
}

.radius-df-top-left {
  border-radius: 16rpx 0rpx 16rpx 0rpx;
}

.radius-df-top-right {
  border-radius: 0rpx 16rpx 0rpx 16rpx;
}

.radius-lg {
  border-radius: 24rpx;
}

.radius-lg-top {
  border-radius: 24rpx 24rpx 0rpx 0rpx;
}

.radius-lg-bottom {
  border-radius: 0rpx 0rpx 24rpx 24rpx;
}

.radius-lg-top-left {
  border-radius: 24rpx 0rpx 24rpx 0rpx;
}

.radius-lg-top-right {
  border-radius: 0rpx 24rpx 0rpx 24rpx;
}

.radius-xl {
  border-radius: 32rpx;
}

.radius-xl-top {
  border-radius: 32rpx 32rpx 0rpx 0rpx;
}

.radius-xl-bottom {
  border-radius: 0rpx 0rpx 32rpx 32rpx;
}

.radius-xl-top-left {
  border-radius: 32rpx 0rpx 32rpx 0rpx;
}

.radius-xl-top-right {
  border-radius: 0rpx 32rpx 0rpx 32rpx;
}

/* flex布局 */

.flex {
  display: flex;
}

.basis-xs {
  flex-basis: 20%;
}

.basis-sm {
  flex-basis: 40%;
}

.basis-df {
  flex-basis: 50%;
}

.basis-lg {
  flex-basis: 60%;
}

.basis-xl {
  flex-basis: 80%;
}

.basis-xxl {
  flex-basis: 100%;
}

.flex-sub {
  flex: 1;
}

.flex-twice {
  flex: 2;
}

.flex-treble {
  flex: 3;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.flex-wrap {
  flex-wrap: wrap;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.align-center {
  align-items: center;
}

.align-stretch {
  align-items: stretch;
}

.self-start {
  align-self: flex-start;
}

.self-center {
  align-self: flex-center;
}

.self-end {
  align-self: flex-end;
}

.self-stretch {
  align-self: stretch;
}

.align-stretch {
  align-items: stretch;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

/* grid布局 */

.grid {
  display: flex;
  flex-wrap: wrap;
}

.grid.grid-square {
  overflow: hidden;
}

.grid.grid-square .cu-tag {
  position: absolute;
  right: 0;
  top: 0;
  border-bottom-left-radius: 6rpx;
  padding: 6rpx 12rpx;
  height: auto;
  background-color: rgba(0, 0, 0, 0.5);
}

.grid.grid-square>view>text[class*="cuIcon-"] {
  font-size: 52rpx;
  position: absolute;
  color: var(--grey);
  margin: auto;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.grid.grid-square>view {
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  border-radius: 6rpx;
  position: relative;
  overflow: hidden;
}

.grid.grid-square>view.bg-img image {
  width: 100%;
  height: 100%;
  position: absolute;
}

.grid.col-1.grid-square>view {
  padding-bottom: 100%;
  height: 0;
  margin-right: 0;
}

.grid.col-2.grid-square>view {
  padding-bottom: calc((100% - 20rpx)/2);
  height: 0;
  width: calc((100% - 20rpx)/2);
}

.grid.col-3.grid-square>view {
  padding-bottom: calc((100% - 40rpx)/3);
  height: 0;
  width: calc((100% - 40rpx)/3);
}

.grid.col-4.grid-square>view {
  padding-bottom: calc((100% - 60rpx)/4);
  height: 0;
  width: calc((100% - 60rpx)/4);
}

.grid.col-5.grid-square>view {
  padding-bottom: calc((100% - 80rpx)/5);
  height: 0;
  width: calc((100% - 80rpx)/5);
}

.grid.col-2.grid-square>view:nth-child(2n),
.grid.col-3.grid-square>view:nth-child(3n),
.grid.col-4.grid-square>view:nth-child(4n),
.grid.col-5.grid-square>view:nth-child(5n) {
  margin-right: 0;
}

.grid.col-1>view {
  width: 100%;
}

.grid.col-2>view {
  width: 50%;
}

.grid.col-3>view {
  width: 33.33%;
}

.grid.col-4>view {
  width: 25%;
}

.grid.col-5>view {
  width: 20%;
}

/* float布局 */

.cf::after,
.cf::before {
  content: " ";
  display: table;
}

.cf::after {
  clear: both;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

/* ==================
     基础样式-背景
  ==================== */

.line-red::after,
.lines-red::after {
  border-color: var(--red);
}

.line-orange::after,
.lines-orange::after {
  border-color: var(--orange);
}

.line-yellow::after,
.lines-yellow::after {
  border-color: var(--yellow);
}

.line-olive::after,
.lines-olive::after {
  border-color: var(--olive);
}

.line-green::after,
.lines-green::after {
  border-color: var(--green);
}

.line-cyan::after,
.lines-cyan::after {
  border-color: var(--cyan);
}

.line-blue::after,
.lines-blue::after {
  border-color: var(--blue);
}

.line-purple::after,
.lines-purple::after {
  border-color: var(--purple);
}

.line-mauve::after,
.lines-mauve::after {
  border-color: var(--mauve);
}

.line-pink::after,
.lines-pink::after {
  border-color: var(--pink);
}

.line-brown::after,
.lines-brown::after {
  border-color: var(--brown);
}

.line-grey::after,
.lines-grey::after {
  border-color: var(--grey);
}

.line-gray::after,
.lines-gray::after {
  border-color: var(--gray);
}

.line-black::after,
.lines-black::after {
  border-color: var(--black);
}

.line-white::after,
.lines-white::after {
  border-color: var(--white);
}

.bg-red {
  background-color: var(--red);
  color: var(--white);
}

.bg-orange {
  background-color: var(--orange);
  color: var(--white);
}

.bg-yellow {
  background-color: var(--yellow);
  color: var(--white);
}

.bg-olive {
  background-color: var(--olive);
  color: var(--white);
}

.bg-green {
  background-color: var(--green);
  color: var(--white);
}

.bg-cyan {
  background-color: var(--cyan);
  color: var(--white);
}

.bg-blue {
  background-color: var(--blue);
  color: var(--white);
}

.bg-purple {
  background-color: var(--purple);
  color: var(--white);
}

.bg-mauve {
  background-color: var(--mauve);
  color: var(--white);
}

.bg-pink {
  background-color: var(--pink);
  color: var(--white);
}

.bg-brown {
  background-color: var(--brown);
  color: var(--white);
}

.bg-grey {
  background-color: var(--grey);
  color: var(--white);
}

.bg-gray {
  background-color: #f0f0f0;
  color: var(--black);
}

.bg-black {
  background-color: var(--black);
  color: var(--white);
}

.bg-white {
  background-color: var(--white);
  color: var(--darkGray);
}

.bg-shadeTop {
  background-image: linear-gradient(rgba(0, 0, 0, 1), rgba(0, 0, 0, 0.01));
  color: var(--white);
}

.bg-shadeBottom {
  background-image: linear-gradient(rgba(64, 64, 64, 0.01), rgba(64, 64, 64, 1));
  color: var(--white);
}

.bg-red.light {
  color: var(--red);
  background-color: var(--redLight);
}

.bg-orange.light {
  color: var(--orange);
  background-color: var(--orangeLight);
}

.bg-yellow.light {
  color: var(--yellow);
  background-color: var(--yellowLight);
}

.bg-olive.light {
  color: var(--olive);
  background-color: var(--oliveLight);
}

.bg-green.light {
  color: var(--green);
  background-color: var(--greenLight);
}

.bg-cyan.light {
  color: var(--cyan);
  background-color: var(--cyanLight);
}

.bg-blue.light {
  color: var(--blue);
  background-color: var(--blueLight);
}

.bg-purple.light {
  color: var(--purple);
  background-color: var(--purpleLight);
}

.bg-mauve.light {
  color: var(--mauve);
  background-color: var(--mauveLight);
}

.bg-pink.light {
  color: var(--pink);
  background-color: var(--pinkLight);
}

.bg-brown.light {
  color: var(--brown);
  background-color: var(--brownLight);
}

.bg-grey.light {
  color: var(--grey);
  background-color: var(--greyLight);
}

.bg-gradual-red {
  background-image: var(--gradualRed);
  color: var(--white);
}

.bg-gradual-orange {
  background-image: var(--gradualOrange);
  color: var(--white);
}

.bg-gradual-green {
  background-image: var(--gradualGreen);
  color: var(--white);
}

.bg-gradual-purple {
  background-image: var(--gradualPurple);
  color: var(--white);
}

.bg-gradual-pink {
  background-image: var(--gradualPink);
  color: var(--white);
}

.bg-gradual-blue {
  background-image: var(--gradualBlue);
  color: var(--white);
}

.bg-gradual-pinknew {
  background-image: var(--gradualPinkNew);
  color: var(--white);
}

.bg-gradual-cyan {
  background-image: var(--gradualCyan);
  color: var(--white);
}

.bg-gradual-red-light {
  background-image: var(--gradualRedLight);
  color: var(--red);
}

.bg-gradual-orange-light {
  background-image: var(--gradualOrangeLight);
  color: var(--orange);
}

.bg-gradual-green-light {
  background-image: var(--gradualGreenLight);
  color: var(--green);
}

.bg-gradual-purple-light {
  background-image: var(--gradualPurpleLight);
  color: var(--purple);
}

.bg-gradual-pink-light {
  background-image: var(--gradualPinkLight);
  color: var(--pink);
}

.bg-gradual-blue-light {
  background-image: var(--gradualBlueLight);
  color: var(--blue);
}

.bg-gradual-pinknew-light {
  background-image: var(--gradualPinkNewLight);
  color: var(--pink);
}

.bg-gradual-cyan-light {
  background-image: var(--gradualCyanLight);
  color: var(--cyan);
}

.bg-blur {
  background: hsla(0, 0%, 100%, .72);
  -webkit-backdrop-filter: blur(15rpx);
  backdrop-filter: blur(15rpx);
}

.bg-img {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.bg-mask {
  background-color: var(--black);
  position: relative;
}

.bg-mask::after {
  content: "";
  border-radius: inherit;
  width: 100%;
  height: 100%;
  display: block;
  background-color: rgba(0, 0, 0, 0.4);
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
}

.bg-mask view,
.bg-mask cover-view {
  z-index: 5;
  position: relative;
}

.bg-video {
  position: relative;
}

.bg-video video {
  display: block;
  height: 100%;
  width: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  position: absolute;
  top: 0;
  z-index: 0;
  pointer-events: none;
}

/* 十级背景色 */

.bg-oc-gray-0 {
  background-color: var(--oc-gray-0);
  color: var(--white);
}

.bg-oc-gray-1 {
  background-color: var(--oc-gray-1);
  color: var(--white);
}

.bg-oc-gray-2 {
  background-color: var(--oc-gray-2);
  color: var(--white);
}

.bg-oc-gray-3 {
  background-color: var(--oc-gray-3);
  color: var(--white);
}

.bg-oc-gray-4 {
  background-color: var(--oc-gray-4);
  color: var(--white);
}

.bg-oc-gray-5 {
  background-color: var(--oc-gray-5);
  color: var(--white);
}

.bg-oc-gray-6 {
  background-color: var(--oc-gray-6);
  color: var(--white);
}

.bg-oc-gray-7 {
  background-color: var(--oc-gray-7);
  color: var(--white);
}

.bg-oc-gray-8 {
  background-color: var(--oc-gray-8);
  color: var(--white);
}

.bg-oc-gray-9 {
  background-color: var(--oc-gray-9);
  color: var(--white);
}

.bg-oc-red-0 {
  background-color: var(--oc-red-0);
  color: var(--white);
}

.bg-oc-red-1 {
  background-color: var(--oc-red-1);
  color: var(--white);
}

.bg-oc-red-2 {
  background-color: var(--oc-red-2);
  color: var(--white);
}

.bg-oc-red-3 {
  background-color: var(--oc-red-3);
  color: var(--white);
}

.bg-oc-red-4 {
  background-color: var(--oc-red-4);
  color: var(--white);
}

.bg-oc-red-5 {
  background-color: var(--oc-red-5);
  color: var(--white);
}

.bg-oc-red-6 {
  background-color: var(--oc-red-6);
  color: var(--white);
}

.bg-oc-red-7 {
  background-color: var(--oc-red-7);
  color: var(--white);
}

.bg-oc-red-8 {
  background-color: var(--oc-red-8);
  color: var(--white);
}

.bg-oc-red-9 {
  background-color: var(--oc-red-9);
  color: var(--white);
}

.bg-oc-pink-0 {
  background-color: var(--oc-pink-0);
  color: var(--white);
}

.bg-oc-pink-1 {
  background-color: var(--oc-pink-1);
  color: var(--white);
}

.bg-oc-pink-2 {
  background-color: var(--oc-pink-2);
  color: var(--white);
}

.bg-oc-pink-3 {
  background-color: var(--oc-pink-3);
  color: var(--white);
}

.bg-oc-pink-4 {
  background-color: var(--oc-pink-4);
  color: var(--white);
}

.bg-oc-pink-5 {
  background-color: var(--oc-pink-5);
  color: var(--white);
}

.bg-oc-pink-6 {
  background-color: var(--oc-pink-6);
  color: var(--white);
}

.bg-oc-pink-7 {
  background-color: var(--oc-pink-7);
  color: var(--white);
}

.bg-oc-pink-8 {
  background-color: var(--oc-pink-8);
  color: var(--white);
}

.bg-oc-pink-9 {
  background-color: var(--oc-pink-9);
  color: var(--white);
}

.bg-oc-grape-0 {
  background-color: var(--oc-grape-0);
  color: var(--white);
}

.bg-oc-grape-1 {
  background-color: var(--oc-grape-1);
  color: var(--white);
}

.bg-oc-grape-2 {
  background-color: var(--oc-grape-2);
  color: var(--white);
}

.bg-oc-grape-3 {
  background-color: var(--oc-grape-3);
  color: var(--white);
}

.bg-oc-grape-4 {
  background-color: var(--oc-grape-4);
  color: var(--white);
}

.bg-oc-grape-5 {
  background-color: var(--oc-grape-5);
  color: var(--white);
}

.bg-oc-grape-6 {
  background-color: var(--oc-grape-6);
  color: var(--white);
}

.bg-oc-grape-7 {
  background-color: var(--oc-grape-7);
  color: var(--white);
}

.bg-oc-grape-8 {
  background-color: var(--oc-grape-8);
  color: var(--white);
}

.bg-oc-grape-9 {
  background-color: var(--oc-grape-9);
  color: var(--white);
}

.bg-oc-violet-0 {
  background-color: var(--oc-violet-0);
  color: var(--white);
}

.bg-oc-violet-1 {
  background-color: var(--oc-violet-1);
  color: var(--white);
}

.bg-oc-violet-2 {
  background-color: var(--oc-violet-2);
  color: var(--white);
}

.bg-oc-violet-3 {
  background-color: var(--oc-violet-3);
  color: var(--white);
}

.bg-oc-violet-4 {
  background-color: var(--oc-violet-4);
  color: var(--white);
}

.bg-oc-violet-5 {
  background-color: var(--oc-violet-5);
  color: var(--white);
}

.bg-oc-violet-6 {
  background-color: var(--oc-violet-6);
  color: var(--white);
}

.bg-oc-violet-7 {
  background-color: var(--oc-violet-7);
  color: var(--white);
}

.bg-oc-violet-8 {
  background-color: var(--oc-violet-8);
  color: var(--white);
}

.bg-oc-violet-9 {
  background-color: var(--oc-violet-9);
  color: var(--white);
}

.bg-oc-indigo-0 {
  background-color: var(--oc-indigo-0);
  color: var(--white);
}

.bg-oc-indigo-1 {
  background-color: var(--oc-indigo-1);
  color: var(--white);
}

.bg-oc-indigo-2 {
  background-color: var(--oc-indigo-2);
  color: var(--white);
}

.bg-oc-indigo-3 {
  background-color: var(--oc-indigo-3);
  color: var(--white);
}

.bg-oc-indigo-4 {
  background-color: var(--oc-indigo-4);
  color: var(--white);
}

.bg-oc-indigo-5 {
  background-color: var(--oc-indigo-5);
  color: var(--white);
}

.bg-oc-indigo-6 {
  background-color: var(--oc-indigo-6);
  color: var(--white);
}

.bg-oc-indigo-7 {
  background-color: var(--oc-indigo-7);
  color: var(--white);
}

.bg-oc-indigo-8 {
  background-color: var(--oc-indigo-8);
  color: var(--white);
}

.bg-oc-indigo-9 {
  background-color: var(--oc-indigo-9);
  color: var(--white);
}

.bg-oc-blue-0 {
  background-color: var(--oc-blue-0);
  color: var(--white);
}

.bg-oc-blue-1 {
  background-color: var(--oc-blue-1);
  color: var(--white);
}

.bg-oc-blue-2 {
  background-color: var(--oc-blue-2);
  color: var(--white);
}

.bg-oc-blue-3 {
  background-color: var(--oc-blue-3);
  color: var(--white);
}

.bg-oc-blue-4 {
  background-color: var(--oc-blue-4);
  color: var(--white);
}

.bg-oc-blue-5 {
  background-color: var(--oc-blue-5);
  color: var(--white);
}

.bg-oc-blue-6 {
  background-color: var(--oc-blue-6);
  color: var(--white);
}

.bg-oc-blue-7 {
  background-color: var(--oc-blue-7);
  color: var(--white);
}

.bg-oc-blue-8 {
  background-color: var(--oc-blue-8);
  color: var(--white);
}

.bg-oc-blue-9 {
  background-color: var(--oc-blue-9);
  color: var(--white);
}

.bg-oc-cyan-0 {
  background-color: var(--oc-cyan-0);
  color: var(--white);
}

.bg-oc-cyan-1 {
  background-color: var(--oc-cyan-1);
  color: var(--white);
}

.bg-oc-cyan-2 {
  background-color: var(--oc-cyan-2);
  color: var(--white);
}

.bg-oc-cyan-3 {
  background-color: var(--oc-cyan-3);
  color: var(--white);
}

.bg-oc-cyan-4 {
  background-color: var(--oc-cyan-4);
  color: var(--white);
}

.bg-oc-cyan-5 {
  background-color: var(--oc-cyan-5);
  color: var(--white);
}

.bg-oc-cyan-6 {
  background-color: var(--oc-cyan-6);
  color: var(--white);
}

.bg-oc-cyan-7 {
  background-color: var(--oc-cyan-7);
  color: var(--white);
}

.bg-oc-cyan-8 {
  background-color: var(--oc-cyan-8);
  color: var(--white);
}

.bg-oc-cyan-9 {
  background-color: var(--oc-cyan-9);
  color: var(--white);
}

.bg-oc-teal-0 {
  background-color: var(--oc-teal-0);
  color: var(--white);
}

.bg-oc-teal-1 {
  background-color: var(--oc-teal-1);
  color: var(--white);
}

.bg-oc-teal-2 {
  background-color: var(--oc-teal-2);
  color: var(--white);
}

.bg-oc-teal-3 {
  background-color: var(--oc-teal-3);
  color: var(--white);
}

.bg-oc-teal-4 {
  background-color: var(--oc-teal-4);
  color: var(--white);
}

.bg-oc-teal-5 {
  background-color: var(--oc-teal-5);
  color: var(--white);
}

.bg-oc-teal-6 {
  background-color: var(--oc-teal-6);
  color: var(--white);
}

.bg-oc-teal-7 {
  background-color: var(--oc-teal-7);
  color: var(--white);
}

.bg-oc-teal-8 {
  background-color: var(--oc-teal-8);
  color: var(--white);
}

.bg-oc-teal-9 {
  background-color: var(--oc-teal-9);
  color: var(--white);
}

.bg-oc-green-0 {
  background-color: var(--oc-green-0);
  color: var(--white);
}

.bg-oc-green-1 {
  background-color: var(--oc-green-1);
  color: var(--white);
}

.bg-oc-green-2 {
  background-color: var(--oc-green-2);
  color: var(--white);
}

.bg-oc-green-3 {
  background-color: var(--oc-green-3);
  color: var(--white);
}

.bg-oc-green-4 {
  background-color: var(--oc-green-4);
  color: var(--white);
}

.bg-oc-green-5 {
  background-color: var(--oc-green-5);
  color: var(--white);
}

.bg-oc-green-6 {
  background-color: var(--oc-green-6);
  color: var(--white);
}

.bg-oc-green-7 {
  background-color: var(--oc-green-7);
  color: var(--white);
}

.bg-oc-green-8 {
  background-color: var(--oc-green-8);
  color: var(--white);
}

.bg-oc-green-9 {
  background-color: var(--oc-green-9);
  color: var(--white);
}

.bg-oc-lime-0 {
  background-color: var(--oc-lime-0);
  color: var(--white);
}

.bg-oc-lime-1 {
  background-color: var(--oc-lime-1);
  color: var(--white);
}

.bg-oc-lime-2 {
  background-color: var(--oc-lime-2);
  color: var(--white);
}

.bg-oc-lime-3 {
  background-color: var(--oc-lime-3);
  color: var(--white);
}

.bg-oc-lime-4 {
  background-color: var(--oc-lime-4);
  color: var(--white);
}

.bg-oc-lime-5 {
  background-color: var(--oc-lime-5);
  color: var(--white);
}

.bg-oc-lime-6 {
  background-color: var(--oc-lime-6);
  color: var(--white);
}

.bg-oc-lime-7 {
  background-color: var(--oc-lime-7);
  color: var(--white);
}

.bg-oc-lime-8 {
  background-color: var(--oc-lime-8);
  color: var(--white);
}

.bg-oc-lime-9 {
  background-color: var(--oc-lime-9);
  color: var(--white);
}

.bg-oc-yellow-0 {
  background-color: var(--oc-yellow-0);
  color: var(--white);
}

.bg-oc-yellow-1 {
  background-color: var(--oc-yellow-1);
  color: var(--white);
}

.bg-oc-yellow-2 {
  background-color: var(--oc-yellow-2);
  color: var(--white);
}

.bg-oc-yellow-3 {
  background-color: var(--oc-yellow-3);
  color: var(--white);
}

.bg-oc-yellow-4 {
  background-color: var(--oc-yellow-4);
  color: var(--white);
}

.bg-oc-yellow-5 {
  background-color: var(--oc-yellow-5);
  color: var(--white);
}

.bg-oc-yellow-6 {
  background-color: var(--oc-yellow-6);
  color: var(--white);
}

.bg-oc-yellow-7 {
  background-color: var(--oc-yellow-7);
  color: var(--white);
}

.bg-oc-yellow-8 {
  background-color: var(--oc-yellow-8);
  color: var(--white);
}

.bg-oc-yellow-9 {
  background-color: var(--oc-yellow-9);
  color: var(--white);
}

.bg-oc-orange-0 {
  background-color: var(--oc-orange-0);
  color: var(--white);
}

.bg-oc-orange-1 {
  background-color: var(--oc-orange-1);
  color: var(--white);
}

.bg-oc-orange-2 {
  background-color: var(--oc-orange-2);
  color: var(--white);
}

.bg-oc-orange-3 {
  background-color: var(--oc-orange-3);
  color: var(--white);
}

.bg-oc-orange-4 {
  background-color: var(--oc-orange-4);
  color: var(--white);
}

.bg-oc-orange-5 {
  background-color: var(--oc-orange-5);
  color: var(--white);
}

.bg-oc-orange-6 {
  background-color: var(--oc-orange-6);
  color: var(--white);
}

.bg-oc-orange-7 {
  background-color: var(--oc-orange-7);
  color: var(--white);
}

.bg-oc-orange-8 {
  background-color: var(--oc-orange-8);
  color: var(--white);
}

.bg-oc-orange-9 {
  background-color: var(--oc-orange-9);
  color: var(--white);
}

/* ==================
     基础样式-文本
  ==================== */

.text-xs {
  font-size: 20rpx;
}

.text-sm {
  font-size: 24rpx;
}

.text-df {
  font-size: 28rpx;
}

.text-lg {
  font-size: 32rpx;
}

.text-xl {
  font-size: 36rpx;
}

.text-xxl {
  font-size: 44rpx;
}

.text-xxxl {
  font-size: 56rpx;
}

.text-sl {
  font-size: 80rpx;
}

.text-xsl {
  font-size: 120rpx;
}

.text-Abc {
  text-transform: Capitalize;
}

.text-ABC {
  text-transform: Uppercase;
}

.text-abc {
  text-transform: Lowercase;
}

.text-price::before {
  content: "¥";
  font-size: 80%;
  margin-right: 4rpx;
}

.text-cut {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.text-cut-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  word-break: break-all;
}

.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-content {
  line-height: 1.6;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-red,
.line-red,
.lines-red {
  color: var(--red);
}

.text-orange,
.line-orange,
.lines-orange {
  color: var(--orange);
}

.text-yellow,
.line-yellow,
.lines-yellow {
  color: var(--yellow);
}

.text-olive,
.line-olive,
.lines-olive {
  color: var(--olive);
}

.text-green,
.line-green,
.lines-green {
  color: var(--green);
}

.text-cyan,
.line-cyan,
.lines-cyan {
  color: var(--cyan);
}

.text-blue,
.line-blue,
.lines-blue {
  color: var(--blue);
}

.text-purple,
.line-purple,
.lines-purple {
  color: var(--purple);
}

.text-mauve,
.line-mauve,
.lines-mauve {
  color: var(--mauve);
}

.text-pink,
.line-pink,
.lines-pink {
  color: var(--pink);
}

.text-brown,
.line-brown,
.lines-brown {
  color: var(--brown);
}

.text-grey,
.line-grey,
.lines-grey {
  color: var(--grey);
}

.text-gray,
.line-gray,
.lines-gray {
  color: var(--gray);
}

.text-black,
.line-black,
.lines-black {
  color: var(--black);
}

.text-white,
.line-white,
.lines-white {
  color: var(--white);
}