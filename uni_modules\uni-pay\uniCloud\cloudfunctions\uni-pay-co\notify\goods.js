'use strict';
/**
 * 此处建议只改下订单状态，保证能及时返回给第三方支付服务器成功状态
 * 且where条件可以增加判断服务器推送过来的金额和订单表中订单需支付金额是否一致
 * 将消息发送、返佣、业绩结算等业务逻辑异步处理(写入异步任务队列表)
 * 如开启定时器每隔5秒触发一次，处理订单
 * 建议再判断下金额和你业务系统订单中的金额是否一致
 */
module.exports = async (obj) => {


	let {
		data = {}
	} = obj;
	let {
		order_no,
		out_trade_no,
		total_fee
	} = data; // uni-pay-orders 表内的数据均可获取到

	// 此处写你自己的支付成功逻辑开始-----------------------------------------------------------
	// 有三种方式
	// 方式一：直接写数据库操作
	// 方式二：使用 await uniCloud.callFunction 调用其他云函数
	// 方式三：使用 await uniCloud.httpclient.request 调用http接口地址

	// 此处写你自己的支付成功逻辑结束-----------------------------------------------------------
	// user_order_success =  true 代表你自己的逻辑处理成功 返回 false 代表你自己的处理逻辑失败。

	let orderDetail = {
		content: '打印测试'
	}


	console.log("####");
	console.log(out_trade_no);
	console.log("$$$$");

	let res = await uniCloud.database().collection("gp_order").doc(out_trade_no).update({
		isPay: true
	});
	let user_order_success = false;
	if (res.updated > 0)
		user_order_success = true;



	// await uniCloud.callFunction({
	// 	name: "ycxcx",
	// 	data: {
	// 		action: 'updateGroupOrder',
	// 		params: {
	// 			out_trade_no:out_trade_no
	// 		}
	// 	}
	// })

	// await uniCloud.callFunction({
	// 	name: "print-feie",
	// 	data: {
	// 		//action: 'getIndex',
	// 		params: {
	// 			content: '测试打印',
	// 		}
	// 	}
	// })

	return user_order_success;
};