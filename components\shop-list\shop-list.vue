<template>
	<view class="shop-list">
		<view class="list-left">
			<view @click="handleChangeType(item._id)" class="list-left-item"
				:class="typeValue === item._id ? 'active':'' " v-for="item in typeList" :key="item._id">
				{{item.label}}
			</view>
		</view>
		<view class="list-content">
			<view class="list-sort">
				<view class="sort-item" @click="handleSort('sale')" :class="sortType === 'sale' ? 'active' : ''">销量
				</view>
				<view class="sort-item" @click="handleSort('discount')"
					:class="sortType === 'discount' ? 'active' : ''">折扣</view>
				<view class="sort-item" @click="handleSortPrice()"
					:class="sortType === 'priceDescending' || sortType === 'priceAscending' ? 'active' : ''">价格
					<svg v-if="sortType !== 'priceDescending' && sortType!== 'priceAscending'" t="1711812369797"
						class="price-sort-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
						p-id="9721" width="200" height="200">
						<path d="M792.099 623.778h-560l280 336 280-336z" fill="#bfbfbf" p-id="9722"></path>
						<path d="M232.099 399.778h560l-280-336-280 336z" fill="#bfbfbf" p-id="9723"
							data-spm-anchor-id="a313x.search_index.0.i12.3d233a81pttDf5" class=""></path>
					</svg>
					<svg t="1711812369797" v-if="sortType=== 'priceDescending'" class="price-sort-icon"
						viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9721" width="200"
						height="200">
						<path d="M792.099 623.778h-560l280 336 280-336z" fill="#bfbfbf" p-id="9722"></path>
						<path d="M232.099 399.778h560l-280-336-280 336z" fill="#f4ea2a" p-id="9723"
							data-spm-anchor-id="a313x.search_index.0.i12.3d233a81pttDf5" class=""></path>
					</svg>
					<svg t="1711812369797" v-if="sortType=== 'priceAscending'" class="price-sort-icon"
						viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9721" width="200"
						height="200">
						<path d="M792.099 623.778h-560l280 336 280-336z" fill="#f4ea2a" p-id="9722"
							data-spm-anchor-id="a313x.search_index.0.i15.3d233a81pttDf5" class=""></path>
						<path d="M232.099 399.778h560l-280-336-280 336z" fill="#bfbfbf" p-id="9723"
							data-spm-anchor-id="a313x.search_index.0.i12.3d233a81pttDf5" class=""></path>
					</svg>
				</view>
			</view>
			<view class="shop-item" v-for="item in shopList" :key="item._id" @click="handleDetail(item)">
				<image class="img" :src="item.url"></image>
				<view class="shop-content">
					<view class="shop-title">
						<view class="limited" v-if="item.titleTag">
							{{item.titleTag}}
						</view>
						{{item.title}}
					</view>
					<view class="shop-desc" v-if="item.desc">
						{{item.desc}}
					</view>
					<view class="shop-tag" v-if="item.tagList.length || item.discount !== 10">
						<view class="tag-item" v-if="item.discount !== 10">
							{{item.discount}}折
						</view>
						<view v-for="label in item.tagList.split(' ')" :key="label" class="tag-item">{{label}}</view>
					</view>
					<view class="shop-footer">
						<view class="shop-price">
							<view class="unit">￥</view>
							{{item.price}}
							<view class="origin-price" v-if="item.originPrice">
								{{item.originPrice}}
							</view>
						</view>
<!-- 						<view class="shop-button">立即抢</view> -->
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		props: {
			sortType: {
				type: String,
				detault: '' // 销量：sale, 折扣 discount, 价格降序（高到底） priceDescending, 价格升序 priceAscending
			},
			typeValue: {
				type: String,
			},
			typeList: {
				type: Array,
				default: []
			},
			shopList: {
				type: Array,
				default: []
			},
		},
		data() {
			return {};
		},
		methods: {
			handleSortPrice() {
				if (this.sortType === 'priceDescending') { // 降序变升序
					this.handleSort('priceAscending')
				} else if (this.sortType === 'priceAscending') { // 升序变清空
					this.handleSort('')
				} else {
					this.handleSort('priceDescending')
				}
			},
			handleSort(sortType) {
				this.$emit('sort', sortType === this.sortType ? '' : sortType)
			},
			handleChangeType(typeValue) {
				this.$emit('changeType', typeValue)
			},
			handleDetail(item) {
				this.$emit('detail', item)
			},
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #fff;
	}

	.shop-list {
		display: flex;
		font-size: 24rpx;

		.list-left {
			position: fixed;
			width: 160rpx;
			text-align: center;
			background: rgb(248, 248, 248);
			height: 100%;
			overflow-y: auto;
			padding-bottom: 36rpx;

			&-item {
				padding: 24rpx 0;

				&.active {
					background: #fff;

					&::before {
						content: '';
						position: absolute;
						left: 4rpx;
						border-radius: 2px;
						width: 8rpx;
						height: 40rpx;
						background: linear-gradient(to right, #feef3c, #f3cd34);
					}
				}
			}
		}

		.list-content {
			margin-left: 170rpx;

			.list-sort {
				width: 580rpx;
				display: flex;
				justify-content: flex-end;

				.sort-item {
					display: flex;
					align-items: center;
					padding: 12rpx 18rpx;
					color: rgba(0, 0, 0, .7);

					&.active {
						color: #f3cd34;
					}

					.price-sort-icon {
						margin-left: 4rpx;
						width: 20rpx;
						height: 24rpx;
					}
				}
			}

			.shop-item {
				display: flex;
				padding: 10rpx;
				padding-right: 0;

				.img {
					flex-shrink: 0;
					width: 160rpx;
					height: 160rpx;
				}

				.shop-content {
					flex: 1;
					padding: 10rpx;
					display: flex;
					flex-direction: column;

					.shop-title {
						font-size: 26rpx;
						word-break: break-all;

						.limited {
							padding: 2rpx 6rpx;
							border-radius: 6rpx;
							line-height: 26rpx;
							color: rgba(0, 0, 0, 0.8);
							background: linear-gradient(to right, #feef3c, #f3cd34);
							font-size: 20rpx;
							display: inline-block;
						}
					}

					.shop-desc {
						margin-top: 4rpx;
						font-size: 20rpx;
						color: rgba(0, 0, 0, .4);
					}

					.shop-tag {
						display: flex;
						flex-wrap: wrap;
						color: #f3cd34;

						.tag-item {
							margin: 4rpx 8rpx 0 0;
							font-size: 20rpx;
							border-radius: 6rpx;
							padding: 0 4rpx;
							display: inline-block;
							border: 1px solid #feef3c;
						}
					}

					.shop-footer {
						display: flex;
						justify-content: space-between;

						.shop-price {
							margin-top: 4rpx;
							display: flex;
							color: rgb(231, 79, 58);
							align-items: center;

							.unit {
								align-self: flex-end;
								font-size: 20rpx;
							}

							.origin-price {
								margin-top: 2rpx;
								margin-left: 4rpx;
								font-size: 20rpx;
								text-decoration: line-through;
								color: rgb(0, 0, 0, 0.4);
							}
						}


						.shop-button {
							background: linear-gradient(to right, #feef3c, #f3cd34);
							border-radius: 6rpx;
							height: 36rpx;
							display: flex;
							align-items: center;
							padding: 0 12rpx;
							border-radius: 36rpx;
						}
					}
				}
			}
		}
	}
</style>