<!-- 
	团购信息查看页面 
-->
<template>
	<view>
		<!-- 水印背景 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<view class='box'>
			<!-- 商品标题 -->
			<view style="width:700rpx;background-color: #fff;">
				<view style="width:100%;display: block;float:center;text-align: center;">
					<view class='subtitle' style="width:100%;height:100%;font-weight: 900;">
						{{item.title}}
					</view>
				</view>
				<!-- 下单和返回按钮 -->
				<view
					style="display: block;font-size:10rpx;margin-top:10rpx;background-color: #fff;height:70rpx;width:100vw;text-align: right;padding-right:60rpx;">
					<button size="mini" type='primary' style="height:60rpx;line-height:60rpx;text-align:center;"
						@click="showPop" :disabled="isDisabled">下单</button>
					<button size="mini" style="margin-top:0rpx;height:60rpx;line-height:60rpx;margin-left:20rpx;"
						@click="back">返回</button>
				</view>
			</view>
		</view>

		<!-- 商品图片 -->
		<view style="text-align: center;margin:0 10rpx;">
			<image :src="item.pic" style="width:100%;object-fit: contain; border-radius: 15rpx;"></image>
		</view>
		<!-- 商品介绍 -->
		<view class='box'>
			<view class='subtitle'>
				商品介绍：
			</view>
			<view class='subdetail'>
				<mp-html :content="item.intro" />
			</view>
		</view>

		<!-- 购买须知 -->
		<view class='box' v-if="item.xz">
			<view class='subtitle'>
				购买须知：
			</view>
			<view class='subdetail'>
				<mp-html :content="item.xz" />
			</view>
		</view>

		<view class='box'>
			<!-- 评论框 -->
			<view class="comment-box">
				<view class="cu-form-group ">
					<uni-easyinput type="textarea" :disabled="!canComment" autoHeight v-model="content"
						placeholder="输入评论内容"></uni-easyinput>
				</view>

				<button size="mini" style="margin-top:20rpx;margin-left:30%; background-color:blueviolet;color: white;"
					@click="submitComment" :disabled="!canComment">提交评论</button>
				<!-- 如果未购买，显示提示信息 -->
				<view v-if="!canComment" class="comment-hint">购买后方可进行评论</view>
			</view>
			<!-- 所有用户的评论列表 -->
			<view v-for="comment in commentList" :key="comment.commentId" v-if="commentList.length>0" class="comments"
				style="margin-top:5rpx;">
				<view style="font-size:12px;display: inline-flex;">
					<view style='width:300rpx;display: block;'>{{comment.xm}}({{comment.dept}})</view>
					<view style='width:350rpx;text-align: right;'>发表时间: {{ formatDate(comment.createTime) }}</view>
				</view>
				<view style="font-size:12px;padding-left:2%;padding-right:2%">
					{{ comment.content }}
				</view>
			</view>
		</view>

		<!-- 下单弹窗 -->
		<uni-popup ref="popup1" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view class="box"
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view
					style="display:block; padding:40rpx 20rpx 20rpx 20rpx;background-color: #fff; text-align: center;">
					订购信息
				</view>
				<hr style="width:100%;color: #ddd;" />

				<view style="display:inline-flex; padding:20rpx 20rpx 0rpx 20rpx;background-color: #fff;">
					<view class='subtitle'>
						商品单价：
					</view>
					<view class='subtitle'>
						{{item.price}}
					</view>
				</view>
				<view style="display:inline-flex; padding:20rpx 20rpx 0rpx;background-color: #fff;">
					<view class='subtitle'>
						订购数量：
					</view>
					<view class='subtitle'>
						<uni-number-box :max="item.limit" v-model="sl" @change="changeValue" background="#2979FF"
							color="#fff" width="80" />
					</view>
				</view>
				<view style="display:inline-flex; padding:20rpx 20rpx 0rpx;background-color: #fff;">
					<view class='subtitle'>
						姓名：
					</view>
					<view class='subtitle'>
						<uni-easyinput v-model="xm"></uni-easyinput>
					</view>
				</view>
				<view style="display:inline-flex; padding:20rpx 20rpx 0rpx;background-color: #fff;">
					<view class='subtitle' style="">
						电话：
					</view>
					<view class='subtitle'>
						<uni-easyinput v-model="tel"></uni-easyinput>
					</view>
				</view>
				<view style="display:inline-flex; padding:20rpx 20rpx 0rpx;background-color: #fff;">
					<view class='subtitle'>
						收货地址 ：
					</view>
					<view class='subtitle' style="width:300rpx;">
						<uni-easyinput type="textarea" placeholder="江苏省苏州市吴江区XXXX镇/街道XXX社区/小区XXX幢XX号" v-model="addr"
							height="100" width='300' style="width:300rpx;padding:5rpx;"></uni-easyinput>
					</view>
				</view>
				<view style="display:inline-flex; padding:20rpx 20rpx 20rpx;background-color: #fff;">
					<view class='subtitle' style="font-size:30rpx;">
						订单金额 ：
					</view>
					<view style="font-size:30rpx;margin-left:10rpx;margin-top:0rpx;">
						￥{{total_fee / 100}}元
					</view>
				</view>

				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 40rpx;">
						<button type='primary' size="mini" @tap="xd">确 定</button>
						<button type='primary' size="mini" @tap="popClose">取 消</button>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 支付组件 -->
		<uni-pay ref="uniPay" height="70vh" return-url="/pages/tabbar/my/myorder" logo="/static/logo.png"
			@success="onSuccess" @create="onCreate"></uni-pay>
	</view>
</template>

<script>
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		data() {
			return {
				// 横幅图片地址
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				// 背景图片地址
				bgSrc: uni.getStorageSync('bg'),
				// 商品信息
				item: {},
				// 评论列表
				commentList: {},
				// 是否可以评论
				canComment: false,
				// 评论内容
				content: '',
				// 索引
				index: 0,
				// 评分
				star: 0,
				// 姓名
				xm: '',
				// 电话
				tel: '',
				// 自定义数据
				custom: null,
				// 地址
				addr: '',
				// 支付金额，单位分
				total_fee: 0,
				// 业务系统订单号
				order_no: "",
				// 插件支付单号
				out_trade_no: "",
				// 支付描述
				description: "测试订单",
				// 支付回调类型
				type: "goods",
				// 查询订单接口的查询条件
				transaction_id: "",
				// 查询订单支付成功后的返回值
				getOrderRes: {},
				// 评论列表
				commentList: [],
				// 显示状态
				show: 1,
				// 数量
				sl: 0
			}
		},
		computed: {
			// 动态生成背景图片的样式
			backgroundStyle() {
				return `background-image: url(${this.item.pic}); width: 150rpx; height: 150rpx;`
			},
			// 判断下单按钮是否禁用
			isDisabled() {
				if (this.item.qsrq) {
					let targetDateSString = this.item.qsrq;
					// 将字符串转换为 Date 对象
					let targetSDate = new Date(targetDateSString.slice(0, 4), targetDateSString.slice(4, 6) - 1,
						targetDateSString.slice(6, 8));
					console.log(targetSDate);
					console.log(new Date());

					if (new Date() < targetSDate)
						return true;
				}
				console.log(this.item.jzrq);

				if (this.item.jzrq) {
					let targetDateString = this.item.jzrq;
					// 将字符串转换为 Date 对象
					let targetDate = new Date(targetDateString.slice(0, 4), targetDateString.slice(4, 6) - 1,
						targetDateString.slice(6, 8));
					// 直接加一天
					targetDate.setDate(targetDate.getDate() + 1);
					if (new Date() > targetDate)
						return true;
				}

				return false;
			}
		},
		// 页面加载时
		onLoad(options) {
			if (options.params) {
				let params;
				try {
					params = JSON.parse(decodeURIComponent(options.params));
				} catch (e) {
					if (e instanceof URIError) {
						params = JSON.parse(options.params);

					}
				}

				this.item = params;
				this.index = options.index;
				this.xm = uni.getStorageSync("uni_truename") || "";
				this.tel = uni.getStorageSync("uni_tel") || "";
			}
		},
		// 页面显示时
		onShow() {
			this.checkPurchase();
			this.loadComments(this.item._id);
		},
		methods: {
			// 显示下单弹窗
			showPop() {
				this.show = 1;
				this.$refs.popup1.open()
			},
			// 关闭下单弹窗
			popClose() {
				this.$refs.popup1.close();

			},
			// 更改数量时计算总价
			changeValue(e) {
				this.total_fee = Math.round(this.item.price * this.sl * 100);
				console.log(this.total_fee);

			},
			// 获取评分值
			getvalue(e) {
				this.star = e;
			},
			// 提交评论（待实现）
			submitComment() {},
			// 下单
			xd() {
				if (this.sl == 0) {
					this.showModal("数量必须大于0！", '提示');
					return;
				} else if (this.sl > this.item.limit) {
					this.showModal("订购数量不能超过个人限量！", '提示');
					return;
				} else if (this.addr == '') {
					this.showModal("必须填写收货地址！", '提示');
					return;
				} else if (this.xm == '') {
					this.showModal("姓名不能为空！", '提示');
					return;
				} else if (this.tel == '') {
					this.showModal("联系电话不能为空！", '提示');
					return;
				} else if (this.sl > this.item.limit) {
					this.showModal("订购数量不能超过个人限量！", '提示');
					return;
				} else {
					//return;
					this.submitQuantity(this.sl);

				}

			},
			// 显示模态框
			showModal(content, title) {
				if (title == '')
					title = '提示';
				uni.showModal({
					title: title,
					content: content,
					showCancel: false
				})
			},
			// 显示数量输入对话框
			showDialog() {
				uni.showModal({
					title: '请输入数量',
					editable: true,
					placeholderText: '输入您需要的数量',
					success: (res) => {
						if (res.confirm) {
							const quantity = res.content.trim();
							if (this.isValidQuantity(quantity)) {
								console.log('用户点击确定');
								this.submitQuantity(quantity);
							} else {
								uni.showToast({
									title: '请输入有效的数字',
									icon: 'none',
									duration: 2000
								});
								// 可以选择重新弹出输入框让用户重新输入
								//this.showDialog();
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 验证数量是否有效
			isValidQuantity(quantity) {
				// 检验是否为有效数字，且为正整数
				return /^\d+$/.test(quantity) && parseInt(quantity) > 0;
			},
			async submitQuantity(quantity) {
				// 这里编写提交逻辑，如调用 API 等
				let orderRes = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/saveGroupOrder',
						params: {
							"userId": uni.getStorageSync("uid"),
							"dept": uni.getStorageSync("dept"),
							"sl": +quantity,
							"groupid": this.item._id,
							"xm": this.xm,
							"tel": this.tel,
							"addr": this.addr,
							"amount": this.item.price * quantity
						},
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				console.log(orderRes);


				let msg = '已下单，点击确定返回上个页面！';
				let title = '已下单，请及时付款';
				if (orderRes.result.state == 'fail') {
					msg = orderRes.result.msg;
					title = '订购失败';
					this.showModal(msg, title);
					return;
				}

				this.order_no = orderRes.result.id;
				this.pay(quantity)

				// uni.showModal({
				// 	title: title,
				// 	content: msg,
				// 	showCancel: false,
				// 	success: (res) => {
				// 		if (res.confirm) {
				// 			console.log('用户点击确定');
				// 			this.pay(quantity)

				// 			// uni.navigateBack({
				// 			// 	delta: 1 // 返回上一层页面
				// 			// });
				// 		} else if (res.cancel) {
				// 			console.log('用户点击取消');
				// 		}
				// 	}
				// });

			},
			back() {
				uni.navigateBack({
					delta: 1 // 返回上一层页面
				});
			},
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			pay(quantity) {

				uni.showLoading({
					title: '加载中'
				})
				//测试订单
				console.log("测试付款");
				this.total_fee = this.item.price * quantity * 100;

				this.custom = {
					good: this.item,
					sl: quantity,
					amount: this.total_fee / 100,
					created_at: Date.now(),
					pay_mode: '微信支付',
					status_text: '已下单'
				}

				this.createOrder("wxpay");

				// let order = this.orderType == 'takein' ? orders[0] : orders[1]
				// order = Object.assign(order, {
				// 	status: 1
				// })
				// this.SET_ORDER(order)
				// uni.removeStorageSync('cart')
				// uni.reLaunch({
				// 	url: '/pages/take-foods/take-foods'
				// })
				// uni.hideLoading()
			},
			createOrder(provider) {
				//this.order_no = `jrxgpay` + Date.now();
				this.out_trade_no = this.order_no;
				// 发起支付


				this.$refs.uniPay.createOrder({
					provider: provider, // 支付供应商
					total_fee: this.total_fee, // 支付金额，单位分 100 = 1元
					order_no: this.order_no, // 业务系统订单号（即你自己业务系统的订单表的订单号）
					out_trade_no: this.out_trade_no, // 插件支付单号
					description: this.description, // 支付描述
					type: this.type, // 支付回调类型
					qr_code: this.qr_code, // 是否强制使用扫码支付
					openid: this.openid, // 微信公众号需要
					custom: this.custom, // 自定义数据
				});
			},
			onCreate(res) {
				console.log('create: ', res);
				// 如果只是想生成支付二维码，不需要组件自带的弹窗，则在这里可以获取到支付二维码 qr_code_image
			},
			// 监听事件 - 支付成功
			onSuccess(res) {
				console.log('success: ', res);
				// if (res.user_order_success) {
				// 	// 代表用户已付款，且你自己写的回调成功并正确执行了
				// 	uni.reLaunch({
				// 		url: '/pages/tabbar/my/myorder?id=' + res.out_trade_no
				// 	})
				// 	uni.hideLoading()
				// } else {
				// 	// 代表用户已付款，但你自己写的回调执行失败（通常是因为你的回调代码有问题）
				// 	uni.hideLoading()
				// }
			},
			async checkPurchase() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/checkGPurchase',
						params: {
							groupId: this.item._id,
							userId: uni.getStorageSync("uid"), // 应由用户状态管理提供

						},
						"uniIdToken": uni.getStorageSync("token")

					}
				});
				console.log(res);
				if (res.result.code === 0) {
					this.canComment = res.result.purchased;
				} else {
					console.error('检查购买状态失败:', res.result.msg);
				}
			},
			async loadComments(groupId) {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: 'xcx/commentList',
						params: {
							'sc': groupId
						},
						"uniIdToken": uni.getStorageSync("token")

					}
				});
				if (res.result.code === 0) {
					this.commentList = res.result.data;
				} else {
					console.error('加载评论失败:', res.result.msg);
				}
			},
			async submitComment() {
				if (!this.canComment) {
					uni.showToast({
						title: '请先购买即时团购商品后再评论',
						icon: 'none'
					});
					return;
				}


				// 提交评论的逻辑，同前文示例
				let res = await callCloudFunction({
					name: 'admin',
					action: 'xcx/saveComment',
					data: {
						action: 'xcx/saveComment',
						params: {
							lgid: this.item._id,
							userId: uni.getStorageSync("uid"),
							dept: uni.getStorageSync("dept"),
							lx: 1,
							content: this.content,
						},
						"uniIdToken": uni.getStorageSync("token")
					},
				});

				if (res.result.state === 'ok') {
					uni.showToast({
						title: '评论成功'
					});
					this.content = ''; // 清空输入框
				} else {
					uni.showToast({
						title: '评论失败',
						icon: 'none'
					});
				}
			}



		}
	}
</script>

<style>
	.title {
		font-size: 30rpx;
		margin: 10rpx;
		border-radius: 15rpx;
		background-color: #fff;
		padding: 20rpx;
		display: flex;
	}

	.title .left {
		width: 80%;
		display: flex;
	}

	.title .right {
		margin-left: 2%;
		width: 50%;
		display: flex;
	}

	.box {
		font-size: 40rpx;
		margin: 10rpx;
		border-radius: 15rpx;
		background-color: #fff;
		padding: 20rpx;
	}

	.subtitle {
		font-size: 30rpx;
		/* 		//font-weight: 900; */
	}

	.subdetail {
		font-size: 20rpx;
		margin-top: 4px;
	}

	.comment-box {
		margin-bottom: 20px;

	}

	.comment-item {
		margin-bottom: 10px;
	}

	.comment-hint {
		color: #999;
		font-size: 14px;
		padding-top: 5px;
	}

	.comments {
		font-size: 12pt;
		border: 1rpx solid #ccc;
		padding: 20rpx;
	}
</style>