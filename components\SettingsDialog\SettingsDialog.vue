<template>
	<view class="dialog">
		<view class="dialog-content">
			<button size="mini" @click="changeAvatar">修改头像</button>
			<button size="mini" @click="changeNickname">修改昵称</button>
			<button size="mini" @click="changePhone">修改手机号</button>
			<button size="mini" @click="close">关 闭</button>
		</view>
		<CustomDialog ref="customDialog" />
	</view>
</template>

<script>
	import CustomDialog from '../CustomDialog/CustomDialog.vue';
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		components: {
			CustomDialog
		},
		methods: {
			close() {
				this.$emit('close');
			},
			async changeAvatar() {
				try {
					const imageRes = await uni.chooseImage({
						count: 1,
						sizeType: ['compressed'],
						sourceType: ['album', 'camera']
					});
					const filePath = imageRes.tempFilePaths[0];
					
					const uploadFileRes = await uniCloud.uploadFile({
						filePath,
						cloudPath: `avatar/${Date.now()}-${Math.random()}.jpg`
					});
					const avatarUrl = uploadFileRes.fileID;
					let avatarRes=await callCloudFunction({
						name: 'admin',
						data: {
							action: 'xcx/updateAvatar',
							params: {
								"_id": uni.getStorageSync("uid"),
								avatarUrl
							},
							"uniIdToken": uni.getStorageSync("token"),
						},
					});
					uni.showToast({
						title: '头像修改成功',
						icon: 'success'
					});
					let user = uni.getStorageSync("user");
					if (!user) user = {};
					user.avatar = avatarRes.result.avatar;
					uni.setStorageSync("user",user);
					
					
				} catch (error) {
					console.error('修改头像失败:', error);
					uni.showToast({
						title: '头像修改失败',
						icon: 'none'
					});
				}
			},
			async changeNickname() {
				try {
					const value = await this.$refs.customDialog.show('修改昵称', '请输入新昵称');
					await callCloudFunction({
						name: 'admin',
						data: {
							action: 'xcx/updateNick',
							params: {
								"_id": uni.getStorageSync("uid"),
								nickname: value,
							},
							"uniIdToken": uni.getStorageSync("token"),
						},
					});
					uni.showToast({
						title: '昵称修改成功',
						icon: 'success'
					});
					uni.setStorageSync("username",value);
				} catch (error) {
					console.error('修改昵称失败:', error);
					uni.showToast({
						title: '昵称修改失败',
						icon: 'none'
					});
				}
			},
			async changePhone() {
				try {
					const value = await this.$refs.customDialog.show('修改手机号', '请输入新手机号');
					await callCloudFunction({
						name: 'admin',
						data: {
							action: 'xcx/updatePhone',
							"uniIdToken": uni.getStorageSync("token"),
							params: {
								"_id": uni.getStorageSync("uid"),
								phone: value,
							}
						}
					});
					uni.showToast({
						title: '手机号修改成功',
						icon: 'success'
					});
					uni.setStorageSync("mobile",value);
				} catch (error) {
					console.error('修改手机号失败:', error);
					uni.showToast({
						title: '手机号修改失败',
						icon: 'none'
					});
				}
			}
		}
	};
</script>

<style scoped>
	.dialog {
		display: flex;
		justify-content: center;
		align-items: center;
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
	}

	.dialog-content {
		background: #f0f0f0;
		padding-left: 80rpx;
		padding-right: 80rpx;
		padding-top: 30rpx;
		padding-bottom: 30rpx;
		border-radius: 20rpx;
		text-align: center;
	}

	.dialog-content button {
		display: block;
		margin: 20rpx 0;
	}
</style>