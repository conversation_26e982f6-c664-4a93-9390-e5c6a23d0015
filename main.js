// 从 'vue' 导入 createSSRApp 和 provide 函数
import {
	createSSRApp,
	provide
} from 'vue';
// 导入 App 组件
import App from './App.vue';
// 从 './utils/auth' 导入 isLoggedIn 和 login 函数
import {
	isLoggedIn,
	login
} from './utils/auth';

// 定义生成水印的函数
function generateWatermark(text) {
	let canvas, ctx, dataUrl;

	// 判断是否在微信小程序环境
	if (process.env.VUE_APP_PLATFORM === 'mp-weixin') {
		// 微信小程序环境
		return new Promise((resolve) => {
			// 创建离屏 canvas
			const offscreenCanvas = wx.createOffscreenCanvas({
				type: '2d',
				width: 200,
				height: 100
			});
			// 获取 canvas 上下文
			const ctx = offscreenCanvas.getContext('2d');
			// 设置字体
			ctx.font = '12px Arial';
			// 设置填充样式
			ctx.fillStyle = 'rgba(184, 184, 184, 0.5)';
			// 旋转画布
			ctx.rotate(-Math.PI / 4);
			// 绘制文本
			ctx.fillText(text, -20, 100);
			// 将 canvas 转换为 dataURL
			dataUrl = offscreenCanvas.toDataURL('image/png');
			// 将 dataURL 存储到本地
			uni.setStorageSync('bg', dataUrl);
			// 解析 Promise
			resolve(dataUrl);
		});
	} else {
		// H5 环境
		// 创建 canvas 元素
		canvas = document.createElement('canvas');
		// 获取 canvas 上下文
		ctx = canvas.getContext('2d');
		// 设置 canvas 尺寸
		canvas.width = 200;
		canvas.height = 100;
		// 设置字体
		ctx.font = '12px Arial';
		// 设置填充样式
		ctx.fillStyle = 'rgba(184, 184, 184, 0.5)';
		// 旋转画布
		ctx.rotate(-Math.PI / 4);
		// 绘制文本
		ctx.fillText(text, -20, 100);
		// 将 canvas 转换为 dataURL
		dataUrl = canvas.toDataURL('image/png');
		// 将 dataURL 存储到本地
		uni.setStorageSync('bg', dataUrl);
		// 返回 Promise
		return Promise.resolve(dataUrl);
	}
}

// 确保用户已登录的异步函数
async function ensureLoggedIn() {
	// 如果未登录
	if (!isLoggedIn.value) {
		try {
			// 尝试登录
			await login();
		} catch (error) {
			// 登录失败时的错误处理
			console.error('登录失败', error);
			// 跳转到登录页面
			uni.navigateTo({
				url: "/pages/login/login"
			});
		}
	}
}

// 标记应用是否已挂载
let appMounted = false;

// 创建应用的函数
export function createApp() {
	// 如果应用已经挂载，则直接返回
	if (appMounted) {
		return;
	}
	// 创建 SSR 应用实例
	const app = createSSRApp(App);

	// 确保用户已登录，然后生成水印
	ensureLoggedIn().then(() => {
		generateWatermark(uni.getStorageSync("uni_truename") + ' ' + uni.getStorageSync("uni_tel")).then((watermarkDataURL) => {
			// 创建全局数据对象
			const globalData = {
				watermarkDataURL
			};
			// 提供全局数据
			app.provide('globalData', globalData);
		});
	});

	// 标记应用已挂载
	appMounted = true;

	// 返回应用实例
	return {
		app
	};
}