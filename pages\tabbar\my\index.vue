<!-- 
	我的页面 
-->
<template>
	<view>
		<!-- 背景图片和水印 -->
		<canvas id="watermark-canvas" type="2d" :style="{ backgroundImage: 'url(' + bgSrc + ')' }">
		</canvas>
		<!-- 顶部横幅图片 -->
		<img :src="bannerSrc" style="width:750rpx;height:280rpx;" />

		<!-- 顶部个人信息 -->
		<view class="bg-white">
			<view class="flex padding">
				<!-- 用户头像 -->
				<view class="padding-lr-xs">
					<view class="cu-avatar lg round" :style="{ backgroundImage: 'url(' + avatar + ')' }">
					</view>
				</view>
				<!-- 用户名和部门信息 -->
				<view class="padding-xs text-xl text-black">
					<view>你好，{{username}}</view>
					<view class="cu-tag round line-green sm">{{dept}}</view>
				</view>
				<!-- 二维码图片 -->
				<view style="margin-left:180rpx;float:left;">
					<image :src='ewmimg' style="width:120rpx;height:120rpx;" @click='showCode()'></image>
				</view>
			</view>
		</view>

		<!-- 常用功能 -->
		<view class="cu-bar margin-lr-xs margin-top-sm grid col-6 no-border bg-white radius-lg-top">
			<view class="action">
				<text class="text-xl text-black">预约记录</text>
			</view>
			<view class="action" @click="more">
				<!-- <text class="text-lg">更多<text class="cuIcon-right"></text></text> -->
			</view>
		</view>
		<!-- 常用功能图标列表 -->
		<view class="cu-list grid col-4 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom">
			<view class="cu-item" v-for="(item,index) in iconList" :key="index" :bindtap="item.bindtap"
				@click=more(index)>
				<view :class="['cuIcon-'+item.icon,'text-'+item.color,'text-shadow']" style="font-size: 56rpx;">
					<view class="cu-tag badge" v-if="item.badge!=0">
						<block v-if="item.badge!=0">{{item.badge>99?"99+":item.badge}}</block>
					</view>
				</view>
				<text v-if="item.name">{{item.name}}</text>
				<text v-else-if="item.title">{{item.title}}</text>
			</view>
		</view>

		<!-- 消息列表 -->
		<view class="cu-bar margin-lr-xs margin-top-sm grid col-4 no-border bg-white radius-lg-top">
			<view class="action">
				<text class="text-xl text-black">消息列表</text>
			</view>
			<view class="action" @click="goMsg">
				<text class="text-lg">更多<text class="cuIcon-right"></text></text>
			</view>
		</view>
		<!-- 消息列表内容 -->
		<view class="cu-list grid no-border text-gray margin-lr-xs padding-bottom radius-lg-bottom">
			<view v-if="msgList.length>0" class="cu-item" v-for="(item,index) in msgList" :key="index"
				@click="showMsg(item)" style="margin-top:20rpx;margin-left:20rpx;margin-right:20rpx;padding:20rpx;
					background-color: #fff;border: 1px solid #f0f0f0;border-radius: 10rpx;
					width:100%;display: inline-block;">
				<view>
					<!-- 消息日期和查看按钮 -->
					<view class='text-grey'
						style="margin-top:5px;padding-left:2%;padding-right:2%;text-align: left; overflow: hidden;">
						{{ formatDate(item.createTime) }}
						<view style="float:right;margin-right: 30rpx;">点击查看</view>
					</view>
					<!-- 消息标题 -->
					<view class='text-grey'
						style="margin-top:5px;padding-left:4%;padding-right:2%;text-align: left; overflow: hidden;">
						{{ item.title }}
					</view>
					<!-- 消息类型和简介 -->
					<view class='text-red' style="display: inline-flex;width:100%;">
						<text style="margin-left:4%;"> {{item.lx}}</text>
						<text style="margin-left:4%;">{{item.intro}}</text>
					</view>
					<!-- 回复内容（如果有） -->
					<view style="display: inline-flex;width:100%;" v-if="item.hf">
						<text style="margin-left:4%;color:red;">已回复：{{item.hf}}</text>
					</view>
				</view>
			</view>
			<!-- 无消息时显示 -->
			<view v-else style="min-height:280rpx;margin-left:150rpx;margin-top:40rpx;font-size:30rpx;color:#AAA;">
				暂无记录
			</view>
		</view>

		<!-- 其他功能 -->
		<view class="cu-bar margin-lr-xs margin-top-sm grid col-4 no-border bg-white radius-lg-top">
			<view class="action">
				<text class="text-xl">其他功能</text>
			</view>
		</view>
		<!-- 其他功能图标列表 -->
		<view class="cu-list grid col-4 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom">
			<view class="cu-item" v-for="(item,index) in iconOtherList" :key="index" :bindtap="item.bindtap">
				<!-- 客服按钮 -->
				<button v-if="index==1" class="icon-button" open-type="contact">
					<view :class="['cuIcon-'+item.icon,'text-'+item.color,'text-shadow']"
						style="font-size: 56rpx;display:block;">
					</view>
				</button>
				<!-- 其他功能按钮 -->
				<button v-else class="icon-button" @click=other(index)>
					<view :class="['cuIcon-'+item.icon,'text-'+item.color,'text-shadow']"
						style="font-size: 56rpx;display:block;">
					</view>
				</button>
				<text style="margin-top:15rpx;">{{item.name}}</text>
			</view>
		</view>

		<!-- 二维码弹窗 -->
		<uni-popup ref="popup1" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;border-radius: 20rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;
					justify-content: center;align-items: center;padding: 5upx;">
					<img :src="ewmcode" style="width:450rpx;" />
				</view>
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 15upx;">
						<view @tap="ewm_close" style="margin-left: 200rpx;padding: 8upx 15upx 8upx 15upx;">
							确 定
						</view>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 消息弹窗 -->
		<uni-popup ref="popup2" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;margin-bottom: 20rpx;;
					justify-content: center;align-items: center;padding: 5upx;">
					<text style="font-size:30rpx;">{{Message}}</text>
				</view>
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 40rpx;">
						<button type='primary' size="mini" @tap="ewm_close">确 定</button>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 消息详情弹窗 -->
		<uni-popup ref="popup3" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;margin-bottom: 20rpx;;
					justify-content: left;align-items: left;padding: 5upx;">
					<!-- 消息详情内容 -->
					<view style="margin-top:10rpx;" v-if="currentItem.createTime">
						<text style="font-size:30rpx;">日期：</text>
						<text style="font-size:30rpx;">{{currentItem.createTime}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.title">
						<text style="font-size:30rpx;">标题：</text>
						<text style="font-size:30rpx;">{{currentItem.title}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.lx">
						<text style="font-size:30rpx;">分类：</text>
						<text style="font-size:30rpx;">{{currentItem.lx}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.intro">
						<text style="font-size:30rpx;">需求：</text>
						<text style="font-size:30rpx;">{{currentItem.intro}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.doc.type">
						<text style="font-size:30rpx;">类别：</text>
						<text style="font-size:30rpx;">{{currentItem.doc.type}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.doc.yy">
						<text style="font-size:30rpx;">类别：</text>
						<text style="font-size:30rpx;">{{currentItem.doc.yy}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.doc.gx">
						<text style="font-size:30rpx;">关系：</text>
						<text style="font-size:30rpx;">{{currentItem.doc.gx}}</text>
					</view>
					<view style="margin-top:10rpx;" v-if="currentItem.hf">
						<text style="font-size:30rpx;">处理：</text>
						<text style="font-size:30rpx;color:red;">{{currentItem.hf}}</text>
					</view>
				</view>
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 40rpx;">
						<button type='primary' size="mini" @tap="ewm_close">确 定</button>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 客服联系弹窗 -->
		<uni-popup ref="popup4" type="center" :mask-click="true" style="position: absolute; top:200rpx;left:100rpx;">
			<view
				style="width: 550rpx;display: flex;background-color: #f0f0f0;flex-direction: column;box-shadow: 1px 1px 1px #808080;border-radius: 20upx;">
				<view style="color: #606266;font-size: 15px;width: 450rpx;
					padding: 30rpx;display: flex;flex-direction: column;margin-left:50rpx;margin-top:50rpx;margin-bottom: 20rpx;;
					justify-content: center;align-items: center;padding: 5upx;">
					<text style="font-size:30rpx;">{{Message}}</text>
				</view>
				<button open-type="contact">联系客服</button>
				<view style="width: 100%;display: flex;align-items: center;font-size: 12px;color: #333;">
					<view style="width: 95%;display: flex;flex-direction: row;align-items: center;padding: 40rpx;">
						<button type='primary' size="mini" @tap="ewm_close">确 定</button>
					</view>
				</view>
			</view>
		</uni-popup>

		<!-- 设置对话框组件 -->
		<SettingsDialog v-if="showSettingsDialog" @close="showSettingsDialog = false" />

		<!-- <tab-bar :pagePath="currentPage" :isgm="isgm"></tab-bar> -->
	</view>
</template>
<script>
	import {
		login
	} from '../../../utils/auth';
	import SettingsDialog from "@/components/SettingsDialog/SettingsDialog.vue";
	import {
		createQrCodeImg
	} from "../../../utils/wxqrcode.js";
	import {
		callCloudFunction
	} from '/utils/utils.js';
	export default {
		components: {
			SettingsDialog
		},
		data() {
			return {
				bannerSrc: uni.getStorageSync("setting") ? uni.getStorageSync("setting").banner :
					"https://7463-tcb-nd4pre2bpj4n866-0cxs5bab8bf2-1304589992.tcb.qcloud.la/a5775c6c-d448-45ee-8588-b516b3bfa808.banner.jpg",
				bgSrc: uni.getStorageSync('bg'),
				isgm: uni.getStorageSync('uni_isgm'),
				currentPage: '../../../pages/tabbar/my/index',
				username: uni.getStorageSync("username"),
				dept: uni.getStorageSync("dept"),
				uid: uni.getStorageSync("uid"),
				ewmimg: '/static/images/ewm.png',
				Message: '',
				ewmcode: '',
				index: 0,
				show: 0,
				loginurl: '',
				currentItem: {
					doc: {}
				},
				showSettingsDialog: false,
				avatar: uni.getStorageSync("user").avatar ? uni.getStorageSync("user").avatar : '/static/images/nopic.jpg',
				msgList: [],
				iconList: [{
					isgm: 0,
					icon: 'moneybagfill',
					color: 'blue',
					badge: 0,
					action: 'xcx/mySy',
					name: '生活服务'
				}, {
					isgm: 0,
					icon: 'moneybagfill',
					color: 'blue',
					badge: 0,
					action: 'xcx/orderCList',
					name: '食堂订餐'
				}, {
					isgm: 0,
					icon: 'presentfill',
					color: 'blue',
					badge: 0,
					name: '团购订单',
					action: 'xcx/myGroup',
					bindtap: "bindZan"
				}, {
					isgm: 0,
					icon: 'formfill',
					color: 'red',
					badge: 0,
					name: '健康资讯',
					action: 'xcx/myJkzx',
					bindtap: "showResource"
				}, {
					isgm: 0,
					icon: 'shopfill',
					color: 'red',
					badge: 0,
					name: '名医问诊',
					action: 'xcx/myMywz',
					bindtap: "bindPoint"
				}, {
					isgm: 0,
					icon: 'shopfill',
					color: 'red',
					badge: 0,
					name: '就学资讯',
					action: 'xcx/myJxxx',
					bindtap: "bindPoint"
				}, {
					isgm: 0,
					icon: 'shopfill',
					color: 'red',
					badge: 0,
					name: '就业资讯',
					action: 'xcx/myJyxx',
					bindtap: "bindPoint"
				}],

				iconOtherList: [{
					icon: 'service',
					color: 'blue',
					badge: 0,
					name: '技术支持',
					bindtap: "bindTec"
				}, {
					icon: 'mark',
					color: 'blue',
					badge: 0,
					name: '联系客服',
					bindtap: "bindService"
				}, {
					icon: 'weixin',
					color: 'blue',
					badge: 0,
					name: '退出登录',
					bindtap: "bindWeChat"
				}, {
					icon: 'settings',
					color: 'blue',
					badge: 0,
					name: '系统设置',
					bindtap: "bindSet"
				}],
				action: 'xcx/mySj',
				params: {
					pageNumber: 1,
					pageSize: 5,
					dept: uni.getStorageSync("dept"),
					userId: uni.getStorageSync("uid")
				},
				list: [],
				setting: uni.getStorageSync("setting")

			}
		},
		computed: {
			// 根据用户权限过滤图标列表
			icons() {
				return this.iconList.filter(a => a.isgm <= this.isgm);
			},
			// 根据用户权限设置网格样式
			gridClass() {
				return this.isgm ? 'cu-list grid col-5 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom' :
					'cu-list grid col-4 no-border text-black margin-lr-xs padding-bottom radius-lg-bottom';
			}
		},
		onLoad() {
			// 生成用户二维码
			this.createQr(this.uid);
		},
		onShow() {
			// 加载页面数据
			this.loadData();
		},
		methods: {
			// 跳转到消息列表页面
			goMsg() {
				uni.navigateTo({
					url: "../../../pages/tabbar/my/msgs"
				});
			},
			// 跳转到更多功能页面
			more(index) {
				uni.navigateTo({
					url: "../../../pages/tabbar/my/more?index=" + index
				});
			},
			// 处理其他功能按钮点击
			async other(v) {
				if (v == 0) {
					// 显示技术支持信息
					this.Message = "技术电话：" + this.setting.jishu;
					this.show = 1;
					this.$refs.popup2.open()

				} else if (v == 1) {
					// 显示客服信息
					this.Message = "客服电话：" + this.setting.kefu;
					this.show = 1;
					this.$refs.popup4.open()
				} else if (v == 2) {
					// 退出登录
					if (process.env.VUE_APP_PLATFORM === 'mp-weixin') {
						let upUser = await callCloudFunction({
							name: 'usercenter',
							data: {
								action: 'updateUser',
								params: {
									uid: this.uid,
									islogin: 0,
								},
								uniIdToken: uni.getStorageSync('token')
							}
						});
						uni.setStorageSync('islogin', 0);

						login();

					} else {
						uni.clearStorageSync();

						uni.navigateTo({
							url: '../../../pages/login/login2',
							success: () => {
								console.log('切换到 tabBar 页面成功！');
							},
							fail: (err) => {
								console.log('切换失败：', err);
							}
						})
					}

				} else if (v == 3) {
					// 显示设置对话框
					this.showSettingsDialog = true;

				}
			},
			// 显示二维码
			showCode() {
				this.ewmcode = this.ewmimg;
				this.show = 1;
				this.$refs.popup1.open()
			},
			// 关闭弹窗
			ewm_close() {
				this.$refs.popup1.close();
				this.$refs.popup2.close()
				this.$refs.popup3.close()
				this.$refs.popup4.close()

			},
			// 显示订单二维码
			showOrderEwm(v) {
				this.ewmcode = createQrCodeImg(v, {
					size: parseInt(120) //二维码大小  
				});
				this.show = 1;
				this.$refs.popup1.open()
			},
			// 创建二维码
			createQr(code) {
				this.ewmimg = createQrCodeImg(code, {
					size: parseInt(120) //二维码大小  
				});
			},
			// 加载页面数据
			async loadData() {
				let res = await callCloudFunction({
					name: 'admin',
					data: {
						action: this.action,
						params: this.params,
						"uniIdToken": uni.getStorageSync("token")
					}
				});
				if (res.result.code === 0) {
					// 更新各功能的消息数量
					this.iconList[0].badge = res.result.shCount;
					this.iconList[1].badge = res.result.dcCount;
					this.iconList[2].badge = res.result.gpCount;
					this.iconList[3].badge = res.result.jkCount;
					this.iconList[4].badge = res.result.myCount;
					this.iconList[5].badge = res.result.jxCount;
					this.iconList[6].badge = res.result.jyCount;

					this.msgList = res.result.page.list;
				} else {
					console.error('加载数据失败:', res.result.msg);
				}
			},
			// 格式化日期
			formatDate(timestamp) {
				const date = new Date(timestamp);
				return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
			},
			// 显示消息详情
			showMsg(item) {
				this.currentItem = JSON.parse(JSON.stringify(item));
				this.$refs.popup3.open()
			}
		}
	}
</script>

<style>
	page {
		background: #EEE;
	}

	.icon-button {
		width: 180rpx;
		height: 80rpx;
		padding: 0;
		margin: 0;
		border: none;
		border-radius: 50%;
		background-color: transparent;
		display: flex;
		justify-content: center;
		align-items: center;
		outline: none;
		/* Remove outline */
	}

	.icon-button::before,
	.icon-button::after {
		content: '';
		display: none;
		/* Ensure no pseudo-elements are adding lines */
	}
</style>
