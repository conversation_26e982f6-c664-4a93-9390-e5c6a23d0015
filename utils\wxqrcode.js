// 导出 createQrCodeImg 函数
export const createQrCodeImg = function(text, options) {
    // 如果没有传入 options，则初始化为空对象
    options = options || {};
    // 设置 typeNumber，默认为 4
    var typeNumber = options.typeNumber || 4;//4 to 8
    // 设置错误纠正级别，默认为 'M'
    var errorCorrectLevel = options.errorCorrectLevel || 'M';
    // 设置二维码大小，默认为 500
    var size = options.size || 500;
 
    // 声明 qr 变量
    var qr;
 
    try {
        // 创建 qrcode 对象
        qr = qrcode(typeNumber, errorCorrectLevel || 'M');
        // 添加数据
        qr.addData(text);
        // 生成二维码
        qr.make();
    } catch (e) {
        // 如果 typeNumber 已经达到最大值 40
        if(typeNumber >= 40) {
            // 抛出错误，文本太长无法编码
            throw new Error('Text too long to encode');
        } else {
            // 否则，增加 typeNumber 并重新尝试生成
            return gen(text, {
                size: size,
                errorCorrectLevel: errorCorrectLevel,
                typeNumber: typeNumber + 1
            });
        }
    }
 
    // 计算单元格大小
    var cellsize = parseInt(size / qr.getModuleCount());
    // 计算边距
    var margin = parseInt((size - qr.getModuleCount() * cellsize) / 2);
 
    // 创建并返回图片标签
    return qr.createImgTag(cellsize, margin, size);
};
// 注释：以下代码在微信小程序中不需要
// var module = {}; 
// module.exports = {
//         createQrCodeImg : createQrCodeImg
// };

// 确保没有其他的 export const createQrCodeImg 或 function createQrCodeImg()
