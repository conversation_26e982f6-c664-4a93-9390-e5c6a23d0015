{"id": "uni-pay", "displayName": "uni-pay", "version": "2.2.1", "description": "更简单的支付接口调用方式、拉齐不同支付平台", "keywords": ["unipay", "uni-pay", "微信支付", "支付宝", "ios内购"], "repository": "https://gitcode.net/dcloud/uni-pay.git", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "unicloud-template-page"}, "uni_modules": {"dependencies": ["uni-config-center", "uni-id-common", "uni-popup", "uni-list"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y", "app-uvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "u", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "u", "字节跳动": "u", "QQ": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}